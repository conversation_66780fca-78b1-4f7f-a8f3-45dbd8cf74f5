FactoryBot.define do
  factory :booking do
    association :company
    association :booking_link
    sequence(:client_name) { |n| "John Doe #{n}" }
    sequence(:client_email) { |n| "john#{n}@example.com" }
    client_phone { "+420123456789" }
    preferred_date { Date.current + 1.day }
    preferred_period { "morning" }
    status { "pending" }
    message { "Test booking message" }
    location { "Test Location" }
    duration { 60 } # in minutes
    
    trait :with_confirmed_time do
      confirmed_time { Time.current.change(hour: 9, min: 0) }
    end
  end
end 