FactoryBot.define do
  factory :meeting_user do
    association :meeting
    association :user
    association :contract
    sequence(:email) { |n| "meeting_participant#{n}@example.com" } # Provide default email if no user/contract

    # Trait to associate with an existing user
    trait :with_user do
      association :user
      email { nil } # Let email be derived from user
    end

    # Trait to associate with an existing contract (and its user)
    trait :with_contract do
      association :contract
      user { contract.user } # Automatically associate with contract's user
      email { nil } # Let email be derived from contract
    end
  end
end 