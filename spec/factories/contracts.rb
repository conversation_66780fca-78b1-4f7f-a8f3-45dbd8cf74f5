FactoryBot.define do
  factory :contract do
    association :company
    first_name { "Test" }
    last_name { "Contractor" }
    sequence(:email) { |n| "contractor#{n}@example.com" }
    contract_type { "employee" }
    active { true }
    status { 0 }
    valid_since { Time.current }
    valid_through { 1.year.from_now }

    trait :with_user do
      association :user
      after(:create) do |contract|
        contract.update_column(:email, contract.user.email)
      end
    end

    trait :skip_invitation do
      after(:build) do |contract|
        def contract.send_invitation; end
      end
    end
  end
end 