require 'rails_helper'

RSpec.describe "Companies", type: :request do
  let(:user) { create(:user) }
  let(:owner_role) { create(:role, name: 'owner') }
  let(:employee_role) { create(:role, name: 'employee') }

  before do
    # Ensure roles exist
    owner_role
    employee_role
    # Sign in the user for request specs
    sign_in user
  end

  describe "POST /companies" do
    let(:valid_attributes) { { company: { name: "New Test Company" } } }
    let(:invalid_attributes) { { company: { name: "" } } } # Example invalid attribute

    context "when user has no existing companies" do
      it "creates a new Company, assigns owner role, creates a contract, and sets tenant" do
        expect {
          post companies_path, params: valid_attributes
        }.to change(Company, :count).by(1)
         .and change(CompanyUserRole, :count).by(1)
         .and change(Contract, :count).by(1)

        expect(response).to redirect_to(companies_path) # Or wherever it should redirect
        expect(flash[:notice]).to eq('Pracovní prostor byl <PERSON><PERSON> v<PERSON>voř<PERSON>.')

        new_company = Company.last
        expect(new_company.name).to eq("New Test Company")
        expect(user.has_role?('owner', new_company)).to be true
        expect(session[:tenant_id]).to eq(new_company.id)

        # Verify contract details
        contract = new_company.contracts.last
        expect(contract.email).to eq(user.email)
        expect(contract.user).to eq(user)
      end

      it "does not create a company with invalid attributes" do
         expect {
           post companies_path, params: invalid_attributes
         }.not_to change(Company, :count)

         expect(response).to render_template(:new) # Or check for appropriate error status/response
      end
    end

    context "when user already belongs to another company" do
      let!(:existing_company) { create(:company) }
      let!(:existing_company_user_role) { create(:company_user_role, user: user, company: existing_company, role: owner_role) }
      let!(:existing_contract) { create(:contract, user: user, company: existing_company, email: user.email) }

      it "creates a new Company for the same user and associates it correctly" do
        # Ensure setup is correct: user should have 1 company initially
        expect(user.companies.count).to eq(1)
        expect(Contract.where(email: user.email).count).to eq(1)


        expect {
          post companies_path, params: valid_attributes
        }.to change(Company, :count).by(1)
         .and change(CompanyUserRole, :count).by(1)
         .and change { Contract.where(email: user.email).count }.by(1) # Check specifically for contracts with this email

        expect(response).to redirect_to(companies_path)
        expect(flash[:notice]).to eq('Pracovní prostor byl úspěšně vytvořen.')

        new_company = Company.last
        expect(new_company.name).to eq("New Test Company")
        expect(new_company).not_to eq(existing_company)

        # Verify user is owner in the new company
        expect(user.has_role?('owner', new_company)).to be true
        expect(session[:tenant_id]).to eq(new_company.id) # Tenant should switch to the new company

        # Verify the new contract belongs to the new company
        new_contract = new_company.contracts.last
        expect(new_contract.email).to eq(user.email)
        expect(new_contract.user).to eq(user)
        expect(new_contract.company).to eq(new_company)

        # Verify the old contract still exists and belongs to the old company
        expect(existing_contract.reload.company).to eq(existing_company)
        expect(user.companies.count).to eq(2) # User should now belong to two companies
      end
    end
  end

  # Add other actions like GET /companies, GET /companies/:id, PATCH /companies/:id etc. if needed
end 