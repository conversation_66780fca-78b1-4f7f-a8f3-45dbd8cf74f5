require 'rails_helper'

RSpec.describe "Meetings", type: :request do
  # Use FactoryBot for test setup
  let!(:tenant_company) { create(:company) }
  let!(:owner_role) { create(:role, name: 'owner') }
  let!(:user) { create(:user) } 
  # Associate user with the tenant company using the role
  let!(:company_user_role) { create(:company_user_role, user: user, company: tenant_company, role: owner_role, is_primary: true) }

  # Create meeting associated with the user and tenant company
  let!(:meeting) { create(:meeting, created_by: user, company: tenant_company) }

  # Define default day options needed for some tests
  let(:default_day_options) { { Date.today.to_s => { '09:00' => "true", '10:00' => "true" } } }

  before do
    # Sign in the user for the requests
    sign_in user
    # Stub ActsAsTenant to return the correct tenant during controller actions
    allow(ActsAsTenant).to receive(:current_tenant).and_return(tenant_company)
    # Stub authorization check
    allow_any_instance_of(MeetingsController).to receive(:authorize!).and_return(true)
  end

  describe "GET /fetch" do
    it "returns a list of meetings" do
      get fetch_meetings_url
      expect(response).to be_successful
      expect(response.content_type).to match(a_string_including("application/json"))
      json_response = JSON.parse(response.body)
      expect(json_response).not_to be_empty
      expect(json_response.first['title']).to eq(meeting.title)
      expect(json_response.first).to have_key('meeting_users')
    end
  end

  describe "GET /meetings/:id" do
    it "returns the specified meeting" do
      get meeting_url(meeting)
      expect(response).to be_successful
      expect(response.content_type).to match(a_string_including("application/json"))
      json_response = JSON.parse(response.body)
      expect(json_response['id']).to eq(meeting.id)
      expect(json_response['title']).to eq(meeting.title)
      expect(json_response).to have_key('meeting_users')
    end

    it "returns not found for invalid id" do
      get meeting_url(id: 'invalid')
      expect(response).to have_http_status(:not_found)
    end
  end

  describe "POST /meetings" do
    let(:valid_attributes) { 
      { 
        meeting: { 
          title: 'New Meeting', 
          description: 'Test Description', 
          place: 'Online', 
          day_options: default_day_options
        } 
      } 
    }
    let(:invalid_attributes) { { meeting: { title: '', day_options: {} } } }

    context "with valid parameters" do
      it "creates a new Meeting" do
        expect {
          post meetings_url, params: valid_attributes
        }.to change(Meeting, :count).by(1)
        expect(response).to have_http_status(:created)
        expect(response.content_type).to match(a_string_including("application/json"))
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['meeting']['title']).to eq('New Meeting')
        expect(json_response['message']).to eq('Meeting created successfully.')
        expect(Meeting.last.company).to eq(tenant_company)
      end
    end

    context "with invalid parameters" do
      it "does not create a new Meeting" do
        expect {
          post meetings_url, params: invalid_attributes
        }.to change(Meeting, :count).by(0)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.content_type).to match(a_string_including("application/json"))
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['errors']).not_to be_empty
      end
    end
  end

  describe "PATCH /meetings/:id" do
    let(:new_attributes) { 
      { 
        meeting: { 
          title: "Updated Meeting Title", 
          day_options: { Date.tomorrow.to_s => { '14:00' => "true" } } 
        } 
      } 
    }

    it "updates the requested meeting" do
      patch meeting_url(meeting), params: new_attributes
      meeting.reload
      expect(meeting.title).to eq("Updated Meeting Title")
      expect(meeting.day_options.keys.first).to eq(Date.tomorrow.to_s)
      expect(meeting.day_options.values.first).to eq({ '14:00' => "true" })
      expect(response).to be_successful
      expect(response.content_type).to match(a_string_including("application/json"))
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['meeting']['title']).to eq("Updated Meeting Title")
      expect(json_response['message']).to eq('Meeting updated successfully.')
    end

    it "returns unprocessable_entity for invalid updates" do
      patch meeting_url(meeting), params: { meeting: { title: '', day_options: default_day_options } }
      expect(response).to have_http_status(:unprocessable_entity)
      expect(response.content_type).to match(a_string_including("application/json"))
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be false
      expect(json_response['errors']).not_to be_empty
    end
  end

  describe "DELETE /meetings/:id" do
    it "destroys the requested meeting" do
      expect {
        delete meeting_url(meeting)
      }.to change(Meeting.where(company: tenant_company), :count).by(-1)
      expect(response).to be_successful
      expect(response.content_type).to match(a_string_including("application/json"))
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['message']).to eq('Meeting deleted successfully.')
    end
  end

  describe "GET /meetings/conflicts" do
    let!(:contract1) { create(:contract, company: tenant_company, user: user) }
    let(:start_date) { Date.today.to_s }
    let(:end_date) { (Date.today + 7.days).to_s }

    before do
      # Stub the external API call to OpenHolidaysAPI
      stub_request(:get, /openholidaysapi.org/).to_return(status: 200, body: "[]", headers: { 'Content-Type': 'application/json' })
    end

    it "returns conflicts for given contracts and date range" do
      get conflicts_meetings_url, params: { contract_ids: [contract1.id], start_date: start_date, end_date: end_date }
      expect(response).to be_successful
      expect(response.content_type).to match(a_string_including("application/json"))
      json_response = JSON.parse(response.body)
      expect(json_response).to have_key('events')
      expect(json_response).to have_key('works')
      expect(json_response).to have_key('meetings')
    end
  end

  describe "POST /meetings/:id/resend_invitation" do
    let!(:meeting_user) { create(:meeting_user, meeting: meeting, user: nil, contract: nil) }

    before do
      ActiveJob::Base.queue_adapter = :test
    end

    it "resends the invitation email" do
      # Expect the mailer method to be called
      expect(MeetingMailer).to receive(:invitation_email).with(meeting_user).and_call_original

      post resend_invitation_meeting_url(meeting, meeting_user_id: meeting_user.id)

      # Original checks remain
      expect(response).to be_successful
      expect(response.content_type).to match(a_string_including("application/json"))
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['message']).to eq('Invitation resent successfully.')
    end

    it "returns not found for invalid meeting user id" do
      post resend_invitation_meeting_url(meeting, meeting_user_id: 'invalid-id-999')
      expect(response).to have_http_status(:not_found)
    end
  end
end
