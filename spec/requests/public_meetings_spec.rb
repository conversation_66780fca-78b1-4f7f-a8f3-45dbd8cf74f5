require 'rails_helper'
require 'webmock/rspec'

RSpec.describe "PublicMeetings", type: :request do
  # Integration tests that use the database and real objects
  let!(:company) { create(:company) }
  let!(:owner) { create(:user) }
  let!(:owner_role) { create(:role, name: 'owner') }
  let!(:company_user_role) { create(:company_user_role, user: owner, company: company, role: owner_role, is_primary: true) }
  
  # Meeting with day options
  let!(:meeting) do 
    create(:meeting, 
      company: company, 
      created_by: owner, 
      title: "Integration Test Meeting",
      day_options: { 
        Date.today.to_s => { '09:00' => true, '10:00' => true },
        (Date.today + 1.day).to_s => { '11:00' => true, '13:00' => true }
      }
    )
  end
  
  let!(:meeting_user_with_email) { create(:meeting_user, meeting: meeting, email: "<EMAIL>", user: nil, contract: nil) }
  
  # Create another user with contract for testing conflicts
  let!(:participant) { create(:user) }
  let!(:contract) { create(:contract, company: company, user: participant) }
  let!(:meeting_user_with_contract) { create(:meeting_user, meeting: meeting, user: participant, contract: contract, email: nil) }
  
  # Stub the holidays API for all tests
  before(:each) do
    WebMock.stub_request(:get, /openholidaysapi\.org/)
      .to_return(
        status: 200, 
        body: [
          { "startDate" => Date.today.to_s },
          { "startDate" => (Date.today + 5.days).to_s }
        ].to_json,
        headers: { 'Content-Type' => 'application/json' }
      )
  end

  describe "GET /private_meetings/:token" do
    context "with valid token" do
      it "returns successful response" do
        get "/private_meetings/#{meeting_user_with_email.token}"
        expect(response).to be_successful
        # The exact content depends on the view, but we can check for elements we expect to see
        expect(response.body).to include("meeting-calendar")
        expect(response.body).to include(meeting_user_with_email.token)
      end
    end
    
    context "with invalid token" do
      it "returns forbidden status" do
        get "/private_meetings/invalid-token"
        expect(response).to have_http_status(:forbidden)
        json_response = JSON.parse(response.body)
        expect(json_response["error"]).to eq("Link je neaktivní")
      end
    end
  end
  
  describe "GET /private_meetings/:token/meeting" do
    context "with valid token" do
      it "returns meeting data as JSON" do
        get "/private_meetings/#{meeting_user_with_email.token}/meeting", as: :json
        expect(response).to be_successful
        
        json_response = JSON.parse(response.body)
        expect(json_response).to include("meeting", "user", "events")
        expect(json_response["meeting"]["title"]).to eq("Integration Test Meeting")
        expect(json_response["user"]["email"]).to eq("<EMAIL>")
      end
      
      it "includes contract user's events and other data when available" do
        # User with contract should get their data loaded
        get "/private_meetings/#{meeting_user_with_contract.token}/meeting", as: :json
        expect(response).to be_successful
        
        json_response = JSON.parse(response.body)
        expect(json_response).to include("meeting", "user")
        # Events, works, and meetings arrays should exist (even if empty)
        expect(json_response).to include("events")
        expect(json_response).to include("works")
        expect(json_response).to include("meetings")
        expect(json_response).to include("holidays")
      end
    end
    
    context "with invalid token" do
      it "returns not found status" do
        get "/private_meetings/invalid-token/meeting", as: :json
        expect(response).to have_http_status(:not_found)
        json_response = JSON.parse(response.body)
        expect(json_response["error"]).to eq("Invalid or expired token")
      end
    end
  end
  
  describe "PATCH /private_meetings/:token" do
    let(:selected_dates) { { Date.today.to_s => true } }
    
    context "with valid token" do
      it "updates the selected dates and returns success JSON" do
        patch "/private_meetings/#{meeting_user_with_email.token}", params: { selected_dates: selected_dates }
        expect(response).to be_successful
        expect(response.content_type).to match(a_string_including("application/json"))

        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['message']).to include("Vaše preference byly odeslány")
        expect(json_response['messageType']).to eq('success')

        # Verify database was updated
        meeting_user_with_email.reload
        expect(meeting_user_with_email.selected_dates).to include(Date.today.to_s)
      end
      
      it "does not confirm meeting if not all users responded" do
        patch "/private_meetings/#{meeting_user_with_email.token}", params: { selected_dates: selected_dates }
        meeting.reload
        expect(meeting.confirmed_date).to be_nil
      end
      
      it "calls confirmation mailer when all users have responded with common time" do
        # First user selects all dates
        patch "/private_meetings/#{meeting_user_with_email.token}", params: {
          selected_dates: { Date.today.to_s => true, (Date.today + 1.day).to_s => true }
        }
        
        # Verify no confirmation yet
        meeting.reload
        expect(meeting.confirmed_date).to be_nil
        
        # Second user selects only one common date - expect mailer to be called
        patch "/private_meetings/#{meeting_user_with_contract.token}", params: {
          selected_dates: { Date.today.to_s => true }
        }
        
        # Verify meeting is now confirmed with the common time
        meeting.reload
        expect(meeting.confirmed_date).not_to be_nil
        expect(meeting.confirmed_date.to_date).to eq(Date.today)
      end
    end
    
    context "with invalid token" do
      it "returns error JSON" do
        patch "/private_meetings/invalid-token", params: { selected_dates: selected_dates }
        expect(response).to be_successful # The request itself is processed ok
        expect(response.content_type).to match(a_string_including("application/json"))

        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['message']).to include("Schůzka je již potvrzena nebo token je neplatný")
        expect(json_response['messageType']).to eq('error')
      end
    end
    
    context "with confirmed meeting" do
      before do
        meeting.update(confirmed_date: DateTime.now)
      end
      
      it "does not allow updates and returns error JSON" do
        patch "/private_meetings/#{meeting_user_with_email.token}", params: { selected_dates: selected_dates }
        expect(response).to be_successful
        expect(response.content_type).to match(a_string_including("application/json"))

        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['message']).to include("Schůzka je již potvrzena nebo token je neplatný")
        expect(json_response['messageType']).to eq('error')
      end
    end
  end
  
  describe "edge cases" do
    it "handles invalid date formats gracefully" do
      get "/private_meetings/#{meeting_user_with_email.token}/meeting", as: :json
      expect(response).to be_successful
    end
    
    context "when API fails" do
      before(:each) do
        WebMock.stub_request(:get, /openholidaysapi\.org/).to_timeout
      end
      
      it "continues to work without holiday data" do
        get "/private_meetings/#{meeting_user_with_contract.token}/meeting", as: :json
        expect(response).to be_successful
        
        json_response = JSON.parse(response.body)
        # Should still have meeting data
        expect(json_response["meeting"]["title"]).to eq("Integration Test Meeting")
        # Holidays might be empty but the key should exist
        expect(json_response).to have_key("holidays")
        expect(json_response["holidays"]).to eq([])
      end
    end
  end
end 