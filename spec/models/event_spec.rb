require 'rails_helper'

RSpec.describe Event, type: :model do
  let(:company) { create(:company) }
  let(:contract) { create(:contract, :with_user, :skip_invitation, company: company) }
  let(:user) { contract.user }
  let(:daily_log) { create(:daily_log, contract: contract, user: user) }

  describe 'validations' do
    it 'requires a company' do
      event = build(:event, company: nil)
      expect(event).not_to be_valid
      expect(event.errors[:company]).to include("musí existovat")
    end

    it 'requires a contract' do
      event = build(:event, contract: nil)
      expect(event).not_to be_valid
      expect(event.errors[:contract]).to include("musí existovat")
    end

    it 'requires an event_type' do
      event = build(:event, event_type: nil)
      expect(event).not_to be_valid
      expect(event.errors[:event_type]).to include("je povinn<PERSON> polož<PERSON>")
    end

    it 'validates event_type inclusion' do
      expect {
        build(:event, event_type: 'invalid_type')
      }.to raise_error(ArgumentError, /'invalid_type' is not a valid event_type/)
    end

    it 'accepts valid event types' do
      Event.event_types.keys.each do |type|
        event = build(:event, event_type: type)
        expect(event).to be_valid
      end
    end
  end

  describe 'overlap constraints' do
    let(:start_time) { Time.current.beginning_of_day + 9.hours }
    let(:end_time) { start_time + 8.hours }

    context 'when there is no daily log' do
      it 'allows event creation' do
        event = build(:event, 
          company: company,
          contract: contract,
          start_time: start_time,
          end_time: end_time
        )
        expect(event).to be_valid
      end
    end

    context 'when there is a daily log for the same day' do
      before do
        create(:daily_log,
          contract: contract,
          user: user,
          start_time: start_time,
          end_time: end_time
        )
      end

      it 'prevents event creation' do
        event = build(:event,
          company: company,
          contract: contract,
          user: user,
          start_time: start_time,
          end_time: end_time
        )
        expect(event).not_to be_valid
        expect(event.errors[:base]).to include("Nelze vytvořit událost pro den, kdy již existuje záznam práce")
      end
    end

    context 'for multi-day events' do
      let(:multi_day_start) { Time.current.beginning_of_day }
      let(:multi_day_end) { multi_day_start + 2.days }

      before do
        create(:daily_log,
          contract: contract,
          user: user,
          start_time: multi_day_start + 1.day,
          end_time: multi_day_start + 1.day + 8.hours
        )
      end

      it 'prevents event creation if any day overlaps' do
        event = build(:event,
          company: company,
          contract: contract,
          user: user,
          start_time: multi_day_start,
          end_time: multi_day_end
        )
        expect(event).not_to be_valid
        expect(event.errors[:base]).to include("Nelze vytvořit událost pro období, které obsahuje dny se záznamy práce")
      end
    end
  end

  describe 'status transitions' do
    let(:event) { create(:event, company: company, contract: contract) }

    it 'starts with pending status' do
      expect(event.status).to eq('pending')
    end

    it 'can be approved' do
      event.approve!
      expect(event.status).to eq('approved')
    end

    it 'can be cancelled' do
      event.cancel!
      expect(event.status).to eq('cancelled')
    end

    it 'can be set to unapproved' do
      event.set_unapproved
      expect(event.status).to eq('pending')
    end
  end

  describe 'holiday events' do
    it 'automatically sets status to pending for holiday events' do
      event = build(:event,
        company: company,
        contract: contract,
        event_type: 'holiday'
      )
      event.save
      expect(event.status).to eq('pending')
    end

    it 'does not automatically set status to pending for non-holiday events' do
      event = build(:event,
        company: company,
        contract: contract,
        event_type: 'illness'
      )
      event.save
      expect(event.status).to eq('pending')
    end
  end

  describe 'translation methods' do
    let(:event) { create(:event, company: company, contract: contract) }

    it 'returns translated event type' do
      expect(event.translated_event_type).to be_a(String)
    end

    it 'returns translated status' do
      expect(event.translated_status).to be_a(String)
    end
  end

  describe 'JSON serialization' do
    let(:event) { create(:event, company: company, contract: contract) }
    let(:json) { event.as_json }

    it 'includes translated status and event type' do
      expect(json[:t_status]).to be_present
      expect(json[:t_event_type]).to be_present
    end

    it 'formats start and end times' do
      expect(json[:start_time]).to match(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)
      expect(json[:end_time]).to match(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)
    end
  end

  describe 'callbacks' do
    it 'sets workday based on start_time' do
      event = create(:event,
        company: company,
        contract: contract,
        start_time: Time.current
      )
      expect(event.workday).to eq(Time.current.wday)
    end
  end
end 