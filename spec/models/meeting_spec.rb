require 'rails_helper'

RSpec.describe Meeting, type: :model do
  # Setup test data
  let(:company) { create(:company) }
  let(:user) { create(:user, email: "unique_test_#{Time.now.to_i}@example.com") }
  let(:role) { create(:role, name: 'test-role') }
  let!(:company_user_role) { create(:company_user_role, user: user, company: company, role: role, is_primary: true) }
  let(:day_options) { { Date.today.to_s => { '09:00' => true, '10:00' => true } } }
  
  # Subject for validation tests
  subject { build(:meeting, company: company, created_by: user, day_options: day_options) }

  # Association tests
  describe "associations" do
    it { should belong_to(:company) }
    it { should belong_to(:created_by).class_name('User') }
    it { should have_many(:meeting_users).dependent(:destroy) }
  end

  # Validation tests
  describe "validations" do
    it { should validate_presence_of(:title) }
    it { should validate_presence_of(:company) }
    it { should validate_presence_of(:day_options) }
  end

  # Method tests
  describe "#all_responses_submitted?" do
    let!(:meeting) { create(:meeting, 
      company: company, 
      created_by: user, 
      title: "Test all_responses_submitted #{SecureRandom.hex(8)}"
    ) }
    
    context "when all users have responded" do
      before do
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "user1-#{SecureRandom.hex(8)}@example.com", 
          selected_dates: { Date.today.to_s => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "user2-#{SecureRandom.hex(8)}@example.com", 
          selected_dates: { Date.today.to_s => true }
        )
      end
      
      it "returns true" do
        expect(meeting.all_responses_submitted?).to be true
      end
    end
    
    context "when some users have not responded" do
      before do
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "user3-#{SecureRandom.hex(8)}@example.com", 
          selected_dates: { Date.today.to_s => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "user4-#{SecureRandom.hex(8)}@example.com", 
          selected_dates: {} # Empty hash instead of nil due to NOT NULL constraint
        )
      end
      
      it "returns false" do
        expect(meeting.all_responses_submitted?).to be false
      end
    end
    
    context "when no users are attached" do
      # Using a different meeting without users
      let!(:empty_meeting) { create(:meeting, 
        company: company, 
        created_by: user, 
        title: "Empty Meeting #{SecureRandom.hex(8)}"
      ) }
      
      it "returns true" do
        expect(empty_meeting.all_responses_submitted?).to be true
      end
    end
  end

  describe "#find_best_common_time" do
    let!(:meeting) { create(:meeting, 
      company: company, 
      created_by: user, 
      title: "Test find_best_common_time #{SecureRandom.hex(8)}"
    ) }
    
    context "when no users exist" do
      it "returns nil" do
        expect(meeting.find_best_common_time).to be_nil
      end
    end
    
    context "when users have no common times" do
      before do
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "user5-#{SecureRandom.hex(8)}@example.com", 
          selected_dates: { "2023-01-01" => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "user6-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-01-02" => true }
        )
      end
      
      it "returns nil" do
        expect(meeting.find_best_common_time).to be_nil
      end
    end
    
    context "when users have common times" do
      let(:date1) { "2023-01-01" }
      let(:date2) { "2023-01-02" }
      
      before do
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "user7-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { date1 => true, date2 => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "user8-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { date1 => true }
        )
      end
      
      it "returns the earliest common time" do
        expect(meeting.find_best_common_time).to eq(date1)
      end
    end
    
    context "when some users haven't selected dates" do
      before do
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "user9-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-01-01" => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "user10-#{SecureRandom.hex(8)}@example.com",
          selected_dates: {}
        )
      end
      
      it "returns nil" do
        expect(meeting.find_best_common_time).to be_nil
      end
    end
  end

  describe "#find_best_options_with_attendance" do
    let!(:meeting) { create(:meeting, company: company, created_by: user, title: "Attendance Test #{SecureRandom.hex(8)}") }
    let(:date1) { "2024-01-01" }
    let(:date2) { "2024-01-02" }
    let(:date3) { "2024-01-03" }

    context "when attempting to set selected_dates to nil" do
      let(:user) { create(:user, email: "unique_test_#{Time.now.to_i}@example.com") }
      let(:meeting) { create(:meeting) }
      let(:meeting_user) { create(:meeting_user, meeting: meeting, user: user) }

      before do
        allow(InvitationHandler).to receive(:process).and_return(true)
        allow_any_instance_of(Contract).to receive(:send_invitation).and_return(true)
      end

      it "raises ActiveRecord::NotNullViolation when trying to set selected_dates to nil" do
        expect {
          ActiveRecord::Base.connection.execute(
            "UPDATE meeting_users SET selected_dates = NULL WHERE id = #{meeting_user.id}"
          )
        }.to raise_error(ActiveRecord::NotNullViolation)
      end

      it "allows setting selected_dates to empty hash" do
        expect {
          ActiveRecord::Base.connection.execute(
            "UPDATE meeting_users SET selected_dates = '{}'::jsonb WHERE id = #{meeting_user.id}"
          )
        }.not_to raise_error
      end
    end

    context "when no users exist" do
      it "returns nil" do
        expect(meeting.find_best_options_with_attendance).to be_nil
      end
    end

    context "when users have selected dates" do
      # Use build_stubbed to avoid DB callbacks
      let(:user1) { build_stubbed(:meeting_user, meeting: meeting, id: 1, email: "<EMAIL>", selected_dates: { date1 => true, date2 => true }) }
      let(:user2) { build_stubbed(:meeting_user, meeting: meeting, id: 2, email: "<EMAIL>", selected_dates: { date2 => true, date3 => true }) }
      let(:user3) { build_stubbed(:meeting_user, meeting: meeting, id: 3, email: "<EMAIL>", selected_dates: { date1 => true, date3 => true }) }
      let(:stubbed_users) { [user1, user2, user3] }

      it "returns options sorted by attendance count" do
        # Stub the association to return our stubbed users
        allow(meeting).to receive(:meeting_users).and_return(stubbed_users)
        
        options = meeting.find_best_options_with_attendance
        
        expect(options.size).to eq(3)
        
        # Date 2 has 2 users (user1, user2)
        option_date2 = options.find { |o| o[:date] == date2 }
        expect(option_date2[:attendance_count]).to eq(2)
        expect(option_date2[:total_users]).to eq(3)
        expect(option_date2[:missing_users].count).to eq(1)
        expect(option_date2[:missing_users].first[:email]).to eq(user3.email)

        # Date 1 has 2 users (user1, user3)
        option_date1 = options.find { |o| o[:date] == date1 }
        expect(option_date1[:attendance_count]).to eq(2)
        expect(option_date1[:total_users]).to eq(3)
        expect(option_date1[:missing_users].count).to eq(1)
        expect(option_date1[:missing_users].first[:email]).to eq(user2.email)

        # Date 3 has 2 users (user2, user3)
        option_date3 = options.find { |o| o[:date] == date3 }
        expect(option_date3[:attendance_count]).to eq(2)
        expect(option_date3[:total_users]).to eq(3)
        expect(option_date3[:missing_users].count).to eq(1)
        expect(option_date3[:missing_users].first[:email]).to eq(user1.email)
        
        # Check sorting (descending by count, then by date) - In this case counts are equal
        # Rspec doesn't guarantee order on find, so we check the counts individually.
        # If sorting was strict, we'd check options[0], options[1], options[2] directly.
      end
    end
    
    context "when some users have not responded" do
        # Use build_stubbed to avoid DB callbacks
        let(:user1) { build_stubbed(:meeting_user, meeting: meeting, id: 11, email: "<EMAIL>", selected_dates: { date1 => true }) }
        let(:user2) { build_stubbed(:meeting_user, meeting: meeting, id: 12, email: "<EMAIL>", selected_dates: {}) } # User 2 hasn't responded
        let(:stubbed_users) { [user1, user2] }

        it "includes only responses from users who submitted" do
            # Stub the association to return our stubbed users
            allow(meeting).to receive(:meeting_users).and_return(stubbed_users)

            options = meeting.find_best_options_with_attendance
            expect(options.size).to eq(1)
            
            option_date1 = options.first
            expect(option_date1[:date]).to eq(date1)
            expect(option_date1[:attendance_count]).to eq(1)
            expect(option_date1[:total_users]).to eq(2) # Total invited users
            expect(option_date1[:missing_users].count).to eq(1)
            expect(option_date1[:missing_users].first[:email]).to eq(user2.email)
        end
    end
  end

  describe "#generate_extended_date_options" do
    let(:today_str) { Date.today.to_s }
    let(:tomorrow_str) { (Date.today + 1.day).to_s }
    let(:yesterday_str) { (Date.today - 1.day).to_s }
    let(:initial_options) { { today_str => ["09:00", "10:00"], tomorrow_str => ["11:00"] } }
    let(:meeting) { create(:meeting, company: company, created_by: user, day_options: initial_options) }

    context "when extending forward" do
      it "adds future dates with empty time slots" do
        extended_options = meeting.generate_extended_date_options(2, 0) # Extend 2 days forward, 0 backward
        future_date_str = (Date.today + 2.days).to_s
        
        expect(extended_options.keys).to include(today_str, tomorrow_str, future_date_str)
        expect(extended_options[today_str]).to eq(["09:00", "10:00"])
        expect(extended_options[tomorrow_str]).to eq(["11:00"])
        expect(extended_options[future_date_str]).to eq([])
        expect(extended_options.keys.size).to eq(4) # Today, Tomorrow, Day after tomorrow, Day after that
      end
    end

    context "when extending backward" do
      it "adds past dates with empty time slots" do
        extended_options = meeting.generate_extended_date_options(0, 1) # Extend 0 days forward, 1 backward
        
        expect(extended_options.keys).to include(yesterday_str, today_str, tomorrow_str)
        expect(extended_options[yesterday_str]).to eq([])
        expect(extended_options[today_str]).to eq(["09:00", "10:00"])
        expect(extended_options[tomorrow_str]).to eq(["11:00"])
        expect(extended_options.keys.size).to eq(3) # Yesterday, Today, Tomorrow
      end
    end

    context "when extending both forward and backward" do
      it "adds both past and future dates" do
        extended_options = meeting.generate_extended_date_options(1, 1) # Extend 1 day forward, 1 backward
        future_date_str = (Date.today + 2.days).to_s # tomorrow is already there, so +1 means +2 from today

        expect(extended_options.keys).to include(yesterday_str, today_str, tomorrow_str, future_date_str)
        expect(extended_options[yesterday_str]).to eq([])
        expect(extended_options[today_str]).to eq(["09:00", "10:00"])
        expect(extended_options[tomorrow_str]).to eq(["11:00"])
        expect(extended_options[future_date_str]).to eq([])
        expect(extended_options.keys.size).to eq(4) # Today, Tomorrow, Day after tomorrow, Day after that
      end
    end

    context "when initial day_options are empty or nil" do
        # Use build + update_column to bypass validation for this specific test context
        let(:meeting_base) { build(:meeting, company: company, created_by: user, day_options: {Date.today.to_s => ['09:00']}) } 

        before do
            meeting_base.save! # Save a valid meeting first
        end

        it "returns an empty hash if initial options are an empty hash" do
             # Manually set {} after creation
             meeting_base.update_column(:day_options, {})
             expect(meeting_base.generate_extended_date_options(1, 1)).to eq({})
        end
    end
  end

  describe "selected_dates constraint" do
    let(:meeting) { create(:meeting) }
    let(:user) { create(:user, email: "unique_test_#{Time.now.to_i}@example.com") }
    let(:meeting_user) { create(:meeting_user, meeting: meeting, user: user) }

    before do
      allow(InvitationHandler).to receive(:process).and_return(true)
      allow_any_instance_of(Contract).to receive(:send_invitation).and_return(true)
    end

    it "raises error when trying to set selected_dates to nil" do
      expect {
        meeting_user.update_column(:selected_dates, nil)
      }.to raise_error(ActiveRecord::NotNullViolation)
    end

    it "allows setting selected_dates to empty hash" do
      expect {
        meeting_user.update_column(:selected_dates, {})
      }.not_to raise_error
    end
  end
end 