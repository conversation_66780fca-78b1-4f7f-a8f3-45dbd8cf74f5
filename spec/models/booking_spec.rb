require 'rails_helper'

RSpec.describe Booking, type: :model do
  let(:company) { create(:company) }
  let(:booking_link) { create(:booking_link, company: company) }
  
  describe 'validations' do
    it 'is valid with valid attributes' do
      booking = build(:booking, company: company, booking_link: booking_link)
      expect(booking).to be_valid
    end
    
    it 'is not valid without a client name' do
      booking = build(:booking, company: company, booking_link: booking_link, client_name: nil)
      expect(booking).not_to be_valid
      expect(booking.errors[:client_name]).to be_present
    end
    
    it 'is not valid without email or phone' do
      booking = build(:booking, company: company, booking_link: booking_link, client_email: nil, client_phone: nil)
      expect(booking).not_to be_valid
      expect(booking.errors[:base]).to include(booking.errors[:base].first)
    end
    
    it 'is valid with only email' do
      booking = build(:booking, company: company, booking_link: booking_link, client_phone: nil)
      expect(booking).to be_valid
    end
    
    it 'is valid with only phone' do
      booking = build(:booking, company: company, booking_link: booking_link, client_email: nil)
      expect(booking).to be_valid
    end
    
    it 'validates email format if email is present' do
      booking = build(:booking, company: company, booking_link: booking_link, client_email: 'invalid-email')
      expect(booking).not_to be_valid
      expect(booking.errors[:client_email]).to be_present
    end
    
    it 'is not valid without preferred date' do
      booking = build(:booking, company: company, booking_link: booking_link, preferred_date: nil)
      expect(booking).not_to be_valid
      expect(booking.errors[:preferred_date]).to be_present
    end
    
    it 'is not valid without preferred period' do
      booking = build(:booking, company: company, booking_link: booking_link, preferred_period: nil)
      expect(booking).not_to be_valid
      expect(booking.errors[:preferred_period]).to be_present
    end
    
    it 'validates preferred period inclusion' do
      booking = build(:booking, company: company, booking_link: booking_link, preferred_period: 'invalid')
      expect(booking).not_to be_valid
      expect(booking.errors[:preferred_period]).to be_present
    end
    
    it 'is not valid without status' do
      booking = build(:booking, company: company, booking_link: booking_link, status: nil)
      expect(booking).not_to be_valid
      expect(booking.errors[:status]).to be_present
    end
    
    it 'validates status inclusion' do
      booking = build(:booking, company: company, booking_link: booking_link, status: 'invalid')
      expect(booking).not_to be_valid
      expect(booking.errors[:status]).to be_present
    end
    
    it 'prevents email changes after creation' do
      booking = create(:booking, company: company, booking_link: booking_link)
      booking.client_email = '<EMAIL>'
      expect(booking).not_to be_valid
      expect(booking.errors[:client_email]).to include("Nelze změnit email k téhle rezervaci.")
    end
    
    it 'prevents changes to cancelled bookings' do
      booking = create(:booking, company: company, booking_link: booking_link, status: 'cancelled')
      booking.client_name = 'New Name'
      expect(booking).not_to be_valid
      expect(booking.errors[:base]).to include("Zrušenou rezervaci nelze upravovat.")
    end
    
    it 'allows only date/time changes for public updates' do
      booking = create(:booking, company: company, booking_link: booking_link)
      booking.public_update = true
      booking.client_name = 'New Name'
      expect(booking).not_to be_valid
      expect(booking.errors[:base]).to include("Lze upravovat pouze datum a čas rezervace.")
    end
  end
  
  describe 'scopes' do
    let(:booking_link) { create(:booking_link, morning_limit: 5, afternoon_limit: 5, daily_limit: 10) }
    let!(:pending_booking) { create(:booking, company: company, booking_link: booking_link, status: 'pending', preferred_date: Date.current + 1.day) }
    let!(:confirmed_booking) { create(:booking, company: company, booking_link: booking_link, status: 'confirmed', preferred_date: Date.current + 2.days) }
    let!(:completed_booking) { create(:booking, company: company, booking_link: booking_link, status: 'completed', preferred_date: Date.current + 3.days) }
    let!(:cancelled_booking) { create(:booking, company: company, booking_link: booking_link, status: 'cancelled', preferred_date: Date.current + 4.days) }
    let!(:rescheduled_booking) { create(:booking, company: company, booking_link: booking_link, status: 'rescheduled', preferred_date: Date.current + 5.days) }
    
    it 'returns pending bookings' do
      expect(Booking.pending).to include(pending_booking)
      expect(Booking.pending).not_to include(confirmed_booking)
    end
    
    it 'returns confirmed bookings' do
      expect(Booking.confirmed).to include(confirmed_booking)
      expect(Booking.confirmed).not_to include(pending_booking)
    end
    
    it 'returns completed bookings' do
      expect(Booking.completed).to include(completed_booking)
      expect(Booking.completed).not_to include(pending_booking)
    end
    
    it 'returns cancelled bookings' do
      expect(Booking.cancelled).to include(cancelled_booking)
      expect(Booking.cancelled).not_to include(pending_booking)
    end
    
    it 'returns upcoming bookings' do
      future_booking = create(:booking, company: company, booking_link: booking_link, preferred_date: Date.current + 1.day)
      past_booking = create(:booking, company: company, booking_link: booking_link, preferred_date: Date.current - 1.day)
      expect(Booking.upcoming).to include(future_booking)
      expect(Booking.upcoming).not_to include(past_booking)
    end
  end
  
  describe 'status transitions' do
    let(:booking_link) { create(:booking_link, morning_limit: 5, afternoon_limit: 5, daily_limit: 10) }
    let(:booking) { create(:booking, company: company, booking_link: booking_link) }
    
    describe '#confirm' do
      it 'changes status to confirmed' do
        expect(booking.confirm).to be true
        expect(booking.reload.status).to eq('confirmed')
      end
      
      it 'sets confirmed time when provided' do
        time = Time.current.change(hour: 9, min: 0)
        booking.confirm(nil, time)
        expect(booking.reload.confirmed_time.hour).to eq(9)
        expect(booking.reload.confirmed_time.min).to eq(0)
      end
      
      it 'creates a work record' do
        expect { booking.confirm }.to change(Work, :count).by(1)
      end
    end
    
    describe '#complete' do
      it 'changes status to completed' do
        booking.confirm
        expect(booking.complete).to be true
        expect(booking.reload.status).to eq('completed')
      end
    end
    
    describe '#cancel' do
      it 'changes status to cancelled' do
        booking.confirm
        expect(booking.cancel).to be true
        expect(booking.reload.status).to eq('cancelled')
      end
      
      it 'cancels associated work if it exists' do
        booking.confirm
        work = booking.work
        booking.cancel
        expect(work.reload.status).to eq('cancelled')
      end
    end
  end
  
  describe 'token management' do
    let(:booking) { create(:booking, company: company, booking_link: booking_link) }
    
    it 'generates access token on creation' do
      expect(booking.access_token).to be_present
      expect(booking.token_generated_at).to be_present
    end
    
    it 'validates token age' do
      booking.token_generated_at = 73.hours.ago
      expect(booking.valid_token?).to be false
      
      booking.token_generated_at = 71.hours.ago
      expect(booking.valid_token?).to be true
    end
    
    it 'regenerates access token' do
      old_token = booking.access_token
      booking.regenerate_access_token
      expect(booking.access_token).not_to eq(old_token)
      expect(booking.token_generated_at).to be > 1.minute.ago
    end
  end
  
  describe 'work synchronization' do
    let(:booking) { create(:booking, company: company, booking_link: booking_link) }
    
    it 'syncs date changes to work' do
      booking.confirm
      new_date = Date.current + 2.days
      booking.update(preferred_date: new_date)
      expect(booking.work.reload.scheduled_start_date).to eq(new_date)
    end
    
    it 'syncs status changes to work' do
      booking.confirm
      booking.cancel
      expect(booking.work.reload.status).to eq('cancelled')
    end
  end
  
  describe 'booking limits' do
    let(:booking_link) { create(:booking_link, morning_limit: 2, afternoon_limit: 2, daily_limit: 4) }
    let(:date1) { Date.current + 1.day }
    let(:date2) { Date.current + 2.days }
    let(:date3) { Date.current + 3.days }

    it "respects period limits" do
      # Create morning bookings for different dates
      create(:booking, booking_link: booking_link, preferred_date: date1, preferred_period: "morning")
      create(:booking, booking_link: booking_link, preferred_date: date2, preferred_period: "morning")
      
      # Try to create another morning booking for a different date
      booking = build(:booking, booking_link: booking_link, preferred_date: date3, preferred_period: "morning")
      expect(booking).to be_valid
    end

    it "respects daily limits" do
      # Create bookings for different dates
      create(:booking, booking_link: booking_link, preferred_date: date1, preferred_period: "morning")
      create(:booking, booking_link: booking_link, preferred_date: date1, preferred_period: "afternoon")
      
      # Try to create another booking for a different date
      booking = build(:booking, booking_link: booking_link, preferred_date: date2, preferred_period: "morning")
      expect(booking).to be_valid
    end
  end

  describe "confirmed time" do
    let(:booking_link) { create(:booking_link, morning_limit: 5, afternoon_limit: 5, daily_limit: 10) }
    let(:booking) { create(:booking, booking_link: booking_link, preferred_date: Date.current + 1.day, preferred_period: "morning") }
    let(:time) { Time.current.change(hour: 9, min: 0) }

    it "sets confirmed time correctly" do
      booking.confirmed_time = time
      booking.save
      expect(booking.reload.confirmed_time.hour).to eq(9)
      expect(booking.reload.confirmed_time.min).to eq(0)
    end
  end
end 