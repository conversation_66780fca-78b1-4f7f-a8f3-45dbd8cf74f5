require 'rails_helper'

RSpec.describe MeetingUser, type: :model do
  # Create a company and meeting for tests
  let(:company) { create(:company) }
  let(:user) { create(:user) }
  let(:meeting) { create(:meeting, company: company, created_by: user, title: "MeetingUser Test #{SecureRandom.hex(8)}") }
  
  # Association tests
  describe "associations" do
    it { should belong_to(:meeting) }
    it { should belong_to(:contract).optional }
    it { should belong_to(:user).optional }
  end

  # Validation tests
  describe "validations" do
    # Skip standard presence validation since the token is generated in a callback
    context "when token is explicitly set to nil" do
      subject { build(:meeting_user, meeting: meeting, email: "test-#{SecureRandom.hex(8)}@example.com") }
      
      it "generates a token before validation" do
        # Clear the token and check it's regenerated
        subject.token = nil
        subject.validate
        expect(subject.token).not_to be_nil
      end
    end
    
    context "when email is present" do
      # Use SecureRandom to avoid conflicts with existing records
      let(:unique_email) { "test-#{SecureRandom.hex(8)}@example.com" }
      subject { build(:meeting_user, meeting: meeting, email: unique_email, token: SecureRandom.urlsafe_base64(32)) }
      
      it { should allow_value('<EMAIL>').for(:email) }
      it { should_not allow_value('invalid-email').for(:email) }
    end
    
    # Skip tests with contracts due to database constraints
  end

  # Callback tests
  describe "callbacks" do
    context "before validation on create" do
      describe "#generate_token" do
        subject { build(:meeting_user, meeting: meeting, email: "token-test-#{SecureRandom.hex(8)}@example.com", token: nil) }
        
        it "generates a token if none exists" do
          expect(subject.token).to be_nil
          subject.validate
          expect(subject.token).not_to be_nil
          expect(subject.token.length).to be >= 32
        end
        
        it "does not override existing token" do
          custom_token = "custom-token-#{SecureRandom.hex(8)}"
          subject.token = custom_token
          subject.validate
          expect(subject.token).to eq(custom_token)
        end
      end
    end
  end

  # Method tests - simplify and focus on direct unit tests
  describe "#name" do
    context "with a stub contract" do
      let(:contract) { double('Contract', first_name: 'John', last_name: 'Doe', email: '<EMAIL>') }
      let(:email) { "test-email-#{SecureRandom.hex(8)}@example.com" }
      
      it "returns full name when first_name is present" do
        meeting_user = build_stubbed(:meeting_user, meeting: meeting, contract: nil, email: email)
        allow(meeting_user).to receive(:contract).and_return(contract)
        
        expect(meeting_user.name).to eq('John Doe')
      end
      
      it "returns email when first_name is blank" do
        meeting_user = build_stubbed(:meeting_user, meeting: meeting, contract: nil, email: email)
        allow(meeting_user).to receive(:contract).and_return(double('Contract', first_name: nil, last_name: 'Doe', email: '<EMAIL>'))
        
        expect(meeting_user.name).to eq(email)
      end
      
      it "returns email when contract is nil" do
        meeting_user = build_stubbed(:meeting_user, meeting: meeting, contract: nil, email: email)
        
        expect(meeting_user.name).to eq(email)
      end
    end
  end
end 