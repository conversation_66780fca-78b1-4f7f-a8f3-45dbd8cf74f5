require 'rails_helper'

RSpec.describe Work, type: :model do
  let(:company) { create(:company, name: "Test Company", subdomain: "test") }
  let(:contract) do
    contract = build(:contract, company: company, first_name: "Test", last_name: "User")
    allow(contract).to receive(:send_invitation).and_return(true)
    contract.save!
    contract
  end

  describe 'validations' do
    it 'is valid with required attributes' do
      work = build(:work, title: "Test Work", company: company, status: "scheduled")
      expect(work).to be_valid
    end

    it 'requires title' do
      work = build(:work, title: nil, company: company, status: "scheduled")
      expect(work).not_to be_valid
      expect(work.errors[:title]).to be_present
    end

    it 'requires company' do
      work = build(:work, title: "Test Work", company: nil, status: "scheduled")
      expect(work).not_to be_valid
      expect(work.errors[:company]).to be_present
    end

    it 'requires status' do
      work = build(:work, title: "Test Work", company: company, status: nil)
      expect(work).not_to be_valid
      expect(work.errors[:status]).to be_present
    end

    it 'accepts valid status values' do
      valid_statuses = %w[scheduled in_progress completed cancelled rescheduled]
      valid_statuses.each do |status|
        work = build(:work, title: "Test Work", company: company, status: status)
        expect(work).to be_valid
      end
    end

    it 'rejects invalid status values' do
      expect {
        build(:work, title: "Test Work", company: company, status: "invalid_status")
      }.to raise_error(ArgumentError)
    end
  end

  describe 'associations' do
    it 'handles work assignments' do
      work = create(:work, title: "Test Work", company: company, status: "scheduled")
      work_assignment = create(:work_assignment, work: work, contract: contract, company: company)
      
      expect(work.work_assignments.count).to eq(1)
      expect(work.contracts.first).to eq(contract)
    end

    it 'destroys dependent work assignments' do
      work = create(:work, title: "Test Work", company: company, status: "scheduled")
      create(:work_assignment, work: work, contract: contract, company: company)
      
      expect {
        work.destroy
      }.to change(WorkAssignment, :count).by(-1)
    end

    it 'handles work sessions' do
      work = create(:work, title: "Test Work", company: company, status: "scheduled")
      user = create(:user, email: "test_sessions_#{Time.now.to_i}@example.com")
      
      work_session = create(:work_session,
        work: work,
        user: user,
        company: company,
        start_time: Time.current,
        status: "in_progress"
      )
      
      expect(work.work_sessions.count).to eq(1)
    end

    it 'destroys dependent work sessions' do
      work = create(:work, title: "Test Work", company: company, status: "scheduled")
      user = create(:user, email: "test_destroy_#{Time.now.to_i}@example.com")
      
      create(:work_session,
        work: work,
        user: user,
        company: company,
        start_time: Time.current,
        status: "in_progress"
      )
      
      expect {
        work.destroy
      }.to change(WorkSession, :count).by(-1)
    end

    it 'does not require work assignments' do
      work = build(:work, title: "Test Work", company: company, status: "scheduled")
      expect(work).to be_valid
    end
  end

  describe 'date tracking' do
    it 'properly tracks scheduled dates' do
      start_date = Date.today
      end_date = start_date + 7.days
      
      work = create(:work,
        title: "Test Work",
        company: company,
        status: "scheduled",
        scheduled_start_date: start_date,
        scheduled_end_date: end_date
      )
      
      expect(work.scheduled_start_date).to eq(start_date)
      expect(work.scheduled_end_date).to eq(end_date)
    end
  end

  describe 'location information' do
    it 'saves and retrieves location information' do
      work = create(:work,
        title: "Test Work",
        company: company,
        status: "scheduled",
        location: "Test Location",
        latitude: 42.123,
        longitude: -71.456
      )
      
      work.reload
      
      expect(work.location).to eq("Test Location")
      expect(work.latitude).to eq(42.123)
      expect(work.longitude).to eq(-71.456)
    end
  end

  describe 'work type' do
    it 'allows setting work type' do
      work = create(:work,
        title: "Test Work",
        company: company,
        status: "scheduled",
        work_type: "maintenance"
      )
      
      work.reload
      expect(work.work_type).to eq("maintenance")
    end
  end
end 