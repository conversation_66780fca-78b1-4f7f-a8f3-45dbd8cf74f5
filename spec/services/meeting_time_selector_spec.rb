require 'rails_helper'

RSpec.describe "MeetingTimeSelection", type: :model do
  let(:company) { create(:company) }
  let(:user) { create(:user) }
  
  # Create a set of test cases with different types of date selections
  describe "Finding the best common time" do
    context "with simple day options" do
      let(:meeting) do
        create(:meeting, 
          company: company, 
          created_by: user,
          title: "Simple day options #{SecureRandom.hex(8)}",
          day_options: {
            "2023-06-01" => { "09:00" => true, "10:00" => true },
            "2023-06-02" => { "14:00" => true }
          }
        )
      end
      
      it "finds the earliest common time slot" do
        # Create users with their selections
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector1-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-01" => true, "2023-06-02" => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector2-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-01" => true }
        )
        
        # Expected result - the first date which is common to all users
        expect(meeting.find_best_common_time).to eq("2023-06-01")
      end
      
      it "returns nil when no common time exists" do
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector3-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-01" => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector4-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-02" => true }
        )
        
        expect(meeting.find_best_common_time).to be_nil
      end
      
      it "handles empty selections" do
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector5-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-01" => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector6-#{SecureRandom.hex(8)}@example.com",
          selected_dates: {}
        )
        
        expect(meeting.find_best_common_time).to be_nil
      end
    end
    
    context "with complex day options" do
      let(:meeting) do
        create(:meeting, 
          company: company, 
          created_by: user,
          title: "Complex day options #{SecureRandom.hex(8)}",
          day_options: {
            "2023-06-01" => { "09:00" => true, "10:00" => true, "11:00" => true },
            "2023-06-02" => { "09:00" => true, "10:00" => true, "11:00" => true },
            "2023-06-03" => { "09:00" => true, "10:00" => true, "11:00" => true }
          }
        )
      end
      
      it "finds the earliest date among multiple common dates" do
        # First user has all days available
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector7-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { 
            "2023-06-01" => true, 
            "2023-06-02" => true, 
            "2023-06-03" => true 
          }
        )
        
        # Second user has only days 2 and 3 available
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector8-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { 
            "2023-06-02" => true, 
            "2023-06-03" => true 
          }
        )
        
        # Should pick day 2, the earliest common date
        expect(meeting.find_best_common_time).to eq("2023-06-02")
      end
      
      it "preserves the precise time slot format in the results" do
        # Create a different meeting with time slot format
        time_meeting = create(:meeting, 
          company: company, 
          created_by: user,
          title: "Time slot meeting #{SecureRandom.hex(8)}",
          day_options: {
            "2023-06-01 09:00" => true,
            "2023-06-01 10:00" => true,
            "2023-06-02 14:00" => true
          }
        )
        
        create(:meeting_user, 
          meeting: time_meeting, 
          user: nil, 
          contract: nil, 
          email: "selector9-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { 
            "2023-06-01 09:00" => true, 
            "2023-06-02 14:00" => true 
          }
        )
        create(:meeting_user, 
          meeting: time_meeting, 
          user: nil, 
          contract: nil, 
          email: "selector10-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { 
            "2023-06-01 09:00" => true
          }
        )
        
        # Should return the precise time slot format
        expect(time_meeting.find_best_common_time).to eq("2023-06-01 09:00")
      end
    end
    
    context "with many participants" do
      let(:meeting) do
        create(:meeting, 
          company: company, 
          created_by: user,
          title: "Many participants #{SecureRandom.hex(8)}",
          day_options: {
            "2023-06-01" => { "09:00" => true },
            "2023-06-02" => { "09:00" => true },
            "2023-06-03" => { "09:00" => true },
            "2023-06-04" => { "09:00" => true },
            "2023-06-05" => { "09:00" => true }
          }
        )
      end
      
      it "correctly finds the common time with many participants" do
        # Create 5 participants with overlapping but different availabilities
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector11-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-01" => true, "2023-06-03" => true, "2023-06-05" => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector12-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-02" => true, "2023-06-03" => true, "2023-06-04" => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector13-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-03" => true, "2023-06-04" => true, "2023-06-05" => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector14-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-01" => true, "2023-06-03" => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector15-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-03" => true }
        )
        
        # The only common date is June 3rd
        expect(meeting.find_best_common_time).to eq("2023-06-03")
      end
      
      it "returns nil when no common time exists among many participants" do
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector16-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-01" => true, "2023-06-02" => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector17-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-03" => true, "2023-06-04" => true }
        )
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector18-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-04" => true, "2023-06-05" => true }
        )
        
        expect(meeting.find_best_common_time).to be_nil
      end
    end
    
    context "with extreme cases" do
      let(:meeting) do
        create(:meeting, 
          company: company, 
          created_by: user,
          title: "Extreme cases #{SecureRandom.hex(8)}",
          day_options: { "2023-06-01" => { "09:00" => true } }
        )
      end
      
      it "handles meetings with no participants" do
        expect(meeting.find_best_common_time).to be_nil
      end
      
      it "works with only one participant" do
        create(:meeting_user, 
          meeting: meeting, 
          user: nil, 
          contract: nil, 
          email: "selector19-#{SecureRandom.hex(8)}@example.com",
          selected_dates: { "2023-06-01" => true }
        )
        
        expect(meeting.find_best_common_time).to eq("2023-06-01")
      end
    end
  end
end 