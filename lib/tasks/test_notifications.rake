namespace :notifications do
  desc "Create test notifications for development"
  task create_test: :environment do
    user = User.first
    company = user.companies.first
    
    unless user && company
      puts "No users or companies found. Please create some first."
      exit
    end
    
    # Create work assignment notification
    work = company.works.first
    if work
      Notification.create!(
        user: user,
        company: company,
        notifiable: work,
        notification_type: 'work_assignment_added',
        title: "New work assignment: #{work.title}",
        message: "You have been assigned to work '#{work.title}' at #{work.location} on #{work.scheduled_start_date}."
      )
      puts "Created work assignment notification"
    end
    
    # Create booking notification
    booking = company.bookings.first
    if booking
      Notification.create!(
        user: user,
        company: company,
        notifiable: booking,
        notification_type: 'booking_received',
        title: "New booking from #{booking.client_name}",
        message: "You have a new booking on #{booking.preferred_date} from #{booking.client_name}."
      )
      puts "Created booking notification"
    end
    
    # Create event notification
    event = user.events.first
    if event
      Notification.create!(
        user: user,
        company: company,
        notifiable: event,
        notification_type: 'event_pending',
        title: "Event pending approval",
        message: "Your request for #{event.event_type} from #{event.start_time} to #{event.end_time} is pending approval."
      )
      puts "Created event notification"
    end
    
    puts "Test notifications created successfully!"
  end
  
  desc "Clear all notifications"
  task clear: :environment do
    Notification.destroy_all
    puts "All notifications cleared."
  end
end