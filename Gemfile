source "https://rubygems.org"
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby "3.1.2"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 7.0.8", ">= *******"

# The original asset pipeline for Rails [https://github.com/rails/sprockets-rails]
gem "sprockets-rails"

# Use postgresql as the database for Active Record
gem "pg", "~> 1.1"

# Use the Puma web server [https://github.com/puma/puma]
gem "puma", "~> 5.0"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ mingw mswin x64_mingw jruby ]

# gem 'sassc-rails' # Removed as Vite is prioritized

gem 'vite_rails'
gem 'devise'

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri mingw x64_mingw ]
  gem 'rack-mini-profiler'
  gem 'rspec-rails', '~> 6.0'
  gem 'factory_bot_rails', '~> 6.2'
end

group :development do
  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"
  gem 'memory_profiler'
  gem 'bullet'
end

gem 'acts_as_tenant'
gem "action_policy"
gem 'rails-i18n', '~> 7.0.0'
gem 'turbo-rails'
gem 'resend'

gem 'devise_invitable'
#gem 'ffi', '~> 1.15.5'

gem 'scout_apm'

gem 'prawn'
gem 'prawn-table'

gem 'sitemap_generator'

group :test do
  gem 'webmock', '~> 3.19'
  gem 'capybara', '~> 3.39'
  gem 'shoulda-matchers', '~> 5.0'
  gem 'database_cleaner-active_record', '~> 2.1'
  gem "simplecov", require: false
  gem 'rspec-sidekiq', '~> 4.0' # For future background job testing
end

gem 'aws-sdk-s3', require: false
gem 'image_processing', '~> 1.2'
gem "mini_magick", "~> 5.2"
