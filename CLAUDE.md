# AttendifyApp Development Guide

## Build Commands
- Start dev server: `foreman start -f Procfile.dev`
- Rails server only: `bin/rails s`
- Vite dev server: `npm run dev`
- Build assets: `bin/vite build`

## Testing Commands
- Run all tests: `bin/rails test`
- Run single test file: `bin/rails test test/models/user_test.rb`
- Run single test method: `bin/rails test test/models/user_test.rb:42`

## Code Style Guidelines
- Ruby: Follow standard Rails conventions (MVC architecture)
- Models: Use validations, callbacks sparingly, prefer service objects for complex logic
- Controllers: Keep thin, use policies for authorization
- Vue: Use Options API 
- Vue: Uses scattered components registered in the central .js file (application.js)
- Components: Organize by feature in app/frontend/components/
- State management: Use Vuex stores in app/frontend/store/
- Error handling: Use flash messages via app/frontend/utils/flashMessage.js

## Naming Conventions
- Ruby: snake_case for variables/methods, CamelCase for classes
- JavaScript: camelCase for variables/methods, PascalCase for components
- CSS: Use BEM methodology within _variables.scss

## Internationalization (i18n)

### Architecture
- Rails-side: Standard Rails i18n with locale YAML files in `config/locales/`
- Vue-side: vue-i18n integrated with Rails i18n via API endpoint
- Translation loading: Fetched from server via `/api/v1/translations/:locale` endpoint
- Locale detection: Path-based strategy (`/:locale/` in URL paths) with cookie fallback

### Translation Structure
- Frontend translations in YAML under `front:` namespace (in `config/locales/*.yml`)
- Feature-specific translations grouped under feature keys (e.g., `booking:`, `works:`, etc.)
- Common, reusable words at root level (e.g., `front.cancel`, `front.save`)

### Usage in Vue Components
- Access translations with `$t('key', 'Default text')`
- Use feature-scoped keys: `$t('booking.expected_duration', 'Předpokládané trvaní')`
- Use fallback text for resilience and clarity during development

### Translation Guidelines
- Keep keys short and semantic (e.g., use `cancel` not `cancel_booking`)
- Use snake_case for all translation keys
- Add gender suffixes when needed (e.g., `cancelled_f` for feminine form)
- Place common UI terms at root level for reusability
- Never use the `front` prefix in component usage
- Never include the component name in translation keys