class ReportMailer < ApplicationMailer

  def attendance_report(recipient, pdf_data, filename, employee_name, selected_date, total_hours)
    @recipient = recipient
    @sender = employee_name
    @period = selected_date
    @hours = total_hours
    subject = t('mailer.report_mailer.attendance_report.subject', default:'<PERSON><PERSON><PERSON><PERSON> p<PERSON>')
    
    attachments[filename] = pdf_data
    mail(
      to: recipient,
      subject: "#{subject} - #{employee_name} - #{selected_date}"
    )
  end
  
end