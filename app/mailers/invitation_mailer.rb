class InvitationMailer < ApplicationMailer
  
  # ToDo: Legacy. Consider remove.
  def invite_email(invitation)
    @invitation = invitation
    @sender = invitation.sender.email
    @acceptance_url = accept_invitation_url(token: invitation.unique_token)
    @company = invitation.company.name

    mail(
      to: invitation.email,
      subject: "Pozvání do Týmboxu od #{@company}",
      from: "Aplikace Týmbox <<EMAIL>>"
    )
  end
end