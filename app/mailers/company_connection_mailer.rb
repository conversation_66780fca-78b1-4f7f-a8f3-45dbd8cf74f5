class CompanyConnectionMailer < ApplicationMailer
  
  def existing_user_notification(sender:, user:, company:, contract:)
    @sender = sender.email
    @user = user
    @company = company
    @contract = contract
    subject = t('mailer.existing_user_notification.subject', default: 'Nové p<PERSON>vání na Týmboxu')
    from = t('mailer.existing_user_notification.from', default: 'Aplikace Týmbox <<EMAIL>>')
    
    mail(
      to: user.email,
      subject: "#{subject} - #{company.name}",
      from: "#{from}"
    )
  end

  # TODO: USE THIS MAILER INSTEAD OF DEVISE - Devise mailer needs company to be added
  def new_user_invitation(sender:, email:, company:, contract:, token:)
    @sender = sender.email
    @company = company
    @contract = contract
    @token = token
    @invitation_url = accept_user_invitation_url(invitation_token: token, locale: I18n.locale)
    subject = t('mailer.new_user_invitation.subject', default: 'Pozvánka do Týmboxu od')
    from = t('mailer.new_user_invitation.from', default: 'Aplikace Týmbox <<EMAIL>>')
    
    mail(
      to: email,
      subject: "#{subject} #{company.name}",
      from: "#{from}"
    )
  end

end