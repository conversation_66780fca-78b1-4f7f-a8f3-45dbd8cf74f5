class TeamStatusMailer < ApplicationMailer
  def daily_report(owner, company, employees, locale)
    @owner = owner
    @company = company
    @employees = employees
    @date = Time.current.in_time_zone("Europe/Prague").strftime("%d.%m.%Y")
    @locale = locale || @owner.user_setting.language_code
    subject = t('mailer.team_status_mailer.daily_report.subject', default: '<PERSON>ým v p<PERSON><PERSON><PERSON>')
    from = t('mailer.team_status_mailer.from', default: 'Docházka Týmbox <<EMAIL>>')
    
    mail(
      to: owner.email,
      subject: "#{company.name} - #{subject} (#{@date})",
      from: "#{from}"
    )
  end
end