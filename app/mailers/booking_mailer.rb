class BookingMailer < ApplicationMailer

  def confirmation_email(booking)
    @booking = booking
    @booking_link = booking.booking_link
    @company = booking.company
    @management_url = manage_booking_url(company_slug: @company.slug, token: booking.access_token)
    subject = t('mailer.booking_mailer.confirmation_email.suject', default: 'Přijetí rezervace')
    from = t('mailer.booking_mailer.from', default: 'Rezervační systém <<EMAIL>>')
    
    mail(
      to: booking.client_email,
      subject: "#{subject} #{@booking_link.name}",
      from: "#{from} "
    )
  end
  
  # Not being used yet
  def access_email(booking)
    @booking = booking
    @booking_link = booking.booking_link
    @company = booking.company
    @management_url = manage_booking_url(company_slug: @company.slug, token: booking.access_token)
    subject = t('mailer.booking_mailer.access_email.subject', default: 'Přístup k vaš<PERSON> rezervaci')
    from = t('mailer.booking_mailer.from', default: 'Rezervačn<PERSON> systém <<EMAIL>>')

    mail(
      to: booking.client_email,
      subject: "#{subject} #{@booking_link.name}",
      from: "#{from}"
    )
  end
  
  # Notifying the company owners of the booking
  def booking_notification(booking, type, owners_email )
    @booking = booking
    @booking_link = booking.booking_link
    @company = booking.company
    @company_owners_email = owners_email
    from = t('mailer.booking_mailer.from', default: 'Rezervační systém <<EMAIL>>')
    
    @subject_prefix = case type
                     when :new
                       t('mailer.booking_mailer.booking_notification.new', default: 'Nová rezervace')
                     when :update
                       t('mailer.booking_mailer.booking_notification.update', default: 'Aktualizace rezervace')
                     when :cancel
                       t('mailer.booking_mailer.booking_notification.cancel', default: 'Zrušení rezervace')
                     else
                       t('mailer.booking_mailer.booking_notification.title', default: 'Rezervace')
                     end
    
    mail(
      to: @company_owners_email,
      subject: "#{@subject_prefix}: #{booking.client_name} - #{@booking_link.name}",
      from: "#{from}"
    )
  end

  def booking_updated_email(booking, original_details = {})
    @booking = booking
    @booking_link = booking.booking_link
    @company = booking.company
    @original_details = original_details
    @management_url = manage_booking_url(company_slug: @company.slug, token: booking.access_token)
    from = t('mailer.booking_mailer.from', default: 'Rezervační systém <<EMAIL>>')
    subject = t('mailer.booking_mailer.booking_updated_email.subject', default: 'Aktualizace vaší rezervace')
    
    mail(
      to: booking.client_email,
      subject: "#{subject} - #{@booking_link.name}",
      from: "#{from}"
    )
  end

  def booking_confirmed_email(booking)
    @booking = booking
    @booking_link = booking.booking_link
    @company = booking.company
    @management_url = manage_booking_url(company_slug: @company.slug, token: booking.access_token)
    subject = t('mailer.booking_mailer.booking_confirmed_email.subject', default: 'Potvrzení rezervace')
    from = t('mailer.booking_mailer.from', default: 'Rezervační systém <<EMAIL>>')

    mail(
      to: booking.client_email,
      subject: "#{subject} - #{@booking_link.name}",
      from: "#{from}"
    )
  end

  def booking_cancelled_email(booking)
    @booking = booking
    @booking_link = booking.booking_link
    @company = booking.company
    @management_url = manage_booking_url(company_slug: @company.slug, token: booking.access_token)
    subject = t('mailer.booking_mailer.booking_cancelled_email.subject', default: 'Zrušení vaší rezervace')
    from = t('mailer.booking_mailer.from', default: 'Rezervační systém <<EMAIL>>')

    mail(
      to: booking.client_email,
      subject: "#{subject} - #{@booking_link.name}",
      from: "#{from}"
    )
  end
end