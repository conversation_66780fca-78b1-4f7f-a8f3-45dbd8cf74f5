class MeetingMailer < ApplicationMailer
  def invitation_email(meeting, meeting_user)
    @meeting = meeting
    @meeting_user = meeting_user
    @company = @meeting.company
    @public_url = public_meeting_url(token: @meeting_user.token)
    subject = t('mailer.meeting_mailer.invitation_email.subject', default: '<PERSON>zvání na schůzku')
    from = t('mailer.meeting_mailer.from', default: '<PERSON><PERSON><PERSON><PERSON><PERSON> sch<PERSON> <<EMAIL>>')
    
    mail(
      to: @meeting_user.email,
      subject: "#{subject} #{@meeting.title}",
      from: "#{from}"
    )
  end
  
  def confirmation_email(meeting_user)
    @meeting_user = meeting_user
    @meeting = meeting_user.meeting
    @company = @meeting.company
    subject = t('mailer.meeting_mailer.confirmation_email.subject', default: '<PERSON>tv<PERSON><PERSON><PERSON> sch<PERSON>')
    from = t('mailer.meeting_mailer.from', default: '<PERSON><PERSON><PERSON><PERSON><PERSON> sch<PERSON>ze<PERSON> <<EMAIL>>')

    mail(
      to: @meeting_user.email,
      subject: "#{subject} #{@meeting.title}",
      from: "#{from}"
    )
  end
  def date_extension_email(meeting_user)
    @meeting = meeting_user.meeting
    @meeting_user = meeting_user
    @company = @meeting.company
    @url = public_meeting_url(token: @meeting_user.token)
    subject = t('mailer.meeting_mailer.date_extension_email.subject', default: 'Termíny schůzky byly rozšířeny.')
    from = t('mailer.meeting_mailer.from', default: 'Plánovač schůzek <<EMAIL>>')

    mail(
      to: @meeting_user.email,
      subject: "#{subject}",
      from: "#{from}"
    )
  end
  
end