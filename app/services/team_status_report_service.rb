class TeamStatusReportService

  # Policy managed here
  # Only send reports to companies with active Plus or Premium subscriptions
  def self.send_daily_reports
    eligible_companies = Company.joins(:subscriptions)
                               .where(subscriptions: { status: 'active' })
                               .where('subscriptions.start_date <= ? AND subscriptions.expire_date >= ?', Date.today, Date.today)
                               .joins('INNER JOIN plans ON plans.id = subscriptions.plan_id')
                               .where('plans.name IN (?)', ['plus', 'premium'])
                               .distinct
    
    eligible_companies.each do |company|
      puts "Sending daily reports: eligible company: #{company.name}"
      process_company(company)
    end
  end
  
  private
  
  def self.process_company(company)
    puts "Processing company: #{company.inspect}"
    # Skip companies without settings for daily reports
    puts "Company setting: #{company.company_setting.inspect}"
    return unless company.company_setting&.daily_team_reports
    
    eligible_managers = company.users
                              .includes(:user_setting)
                              .joins(:company_user_roles)
                              .joins('INNER JOIN roles ON roles.id = company_user_roles.role_id')
                              .where('roles.name IN (?)', ['owner', 'supervisor', 'admin'])
                              .distinct
    
    puts "Eligible managers: #{eligible_managers.inspect}"
    return if eligible_managers.empty?                         

    # Get employee status data
    employees = fetch_employees_status(company)
    puts "Employees: #{employees.inspect}"
    return if employees.empty?
    
    # Send email to each eligible_managers
    eligible_managers.each do |manager|
      TeamStatusMailer.daily_report(manager, company, employees).deliver_now
    end
  end
  
  def self.fetch_employees_status(company)
    contracts_with_logs = company.contracts
      .joins("LEFT JOIN (
        SELECT DISTINCT ON (contract_id) contract_id, start_time, end_time
        FROM daily_logs
        ORDER BY contract_id, start_time DESC
      ) latest_logs ON latest_logs.contract_id = contracts.id")
      .select(
        'contracts.id',
        'contracts.first_name',
        'contracts.last_name',
        'contracts.phone',
        'latest_logs.start_time',
        'latest_logs.end_time'
      )
      .order('latest_logs.start_time DESC NULLS LAST')
    
    contracts_with_logs.map do |contract|
      {
        id: contract.id.to_s,
        name: "#{contract.first_name} #{contract.last_name}",
        phone: contract.phone,
        start: contract.start_time,
        end: contract.end_time,
        working: contract.end_time.nil? && contract.start_time.present?
      }
    end
  end
end