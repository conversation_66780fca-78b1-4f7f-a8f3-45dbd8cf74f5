module ReportDataService
  
  #include <PERSON><PERSON><PERSON>cher
  extend <PERSON>F<PERSON>cher

  def self.generate_report_data(contract, date_param)
    date_param = date_param.is_a?(Date) ? date_param : Date.parse(date_param.to_s)
    year = date_param.year
    month = date_param.month
    start_date = Date.new(year, month, 1)
    end_date = start_date.end_of_month
    company = contract.company

    # holidays = fetch_holidays(year, month)
    
    # Use the class method from HolidayFetcher
    # The country will default to 'CZ' or use user setting if available in that context (not here)
    holidays = fetch_holidays(year, month) 
    holiday_dates = holidays.map { |date_str| Date.parse(date_str) }
    
    monthly_events = contract.events
      .where(start_time: start_date.beginning_of_day..end_date.end_of_day)
      .where(event_type: [:vacation, :illness, :family_sick, :day_care, :other])
      .select('id, event_type, start_time, end_time')

    monthly_logs = contract.daily_logs
      .includes(:breaks)
      .where(start_time: start_date.beginning_of_day..end_date.end_of_day)
      .select('id, start_time, end_time, duration')
      .index_by { |log| log.start_time.to_date }

    total_worked_hours = 0
    days_worked = 0
    event_counts = {
      'vacation' => 0,
      'illness' => 0,
      'day_care' => 0,
      'family_sick' => 0,
      'other' => 0
    }

    monthly_events.each do |event|
      event_type = event.event_type.to_s
      next unless event_counts.key?(event_type)
      
      start_event_date = [event.start_time.to_date, start_date].max
      end_event_date = [event.end_time.to_date, end_date].min
      
      working_days = (start_event_date..end_event_date).count do |date| 
        (1..5).include?(date.wday) && !holiday_dates.include?(date) && !monthly_logs.key?(date)
      end
      
      event_counts[event_type] += working_days
    end

    days = (1..end_date.day).map do |day|
      date = Date.new(year, month, day)

      event = monthly_events.find do |e| 
        event_date = e.start_time.to_date
        event_end_date = e.end_time&.to_date || event_date
        (event_date..event_end_date).cover?(date)
      end

      if event
        event_info = {
          id: event.id,
          type: event.event_type,
          description: event.translated_event_type
        }
      else
        log_data = monthly_logs[date]

        if log_data
          breaks = log_data.breaks.first
          break_duration = breaks&.duration || 0
          net_duration = [(log_data.duration || 0) - break_duration, 0].max
          total_worked_hours += net_duration

          log_info = {
            id: log_data.id,
            start_time: log_data.start_time,
            end_time: log_data.end_time,
            break_id: breaks&.id,
            break_start: breaks&.start_time,
            break_end: breaks&.end_time,
            duration: net_duration 
          }
        end
      end

      if log_data
        days_worked += 1
      end

      {
        day: day,
        date: date.to_s,
        is_weekend: date.saturday? || date.sunday?,
        is_holiday: holidays.include?(date.to_s),
        log: log_info,
        event: event_info
      }
    end

    { 
      days: days,
      total_hours: total_worked_hours,
      company_name: company.name,
      user_name: [contract.first_name, contract.last_name].join(' '),
      user_email: contract.email,
      selected_date: date_param,
      days_worked: days_worked,
      event_counts: event_counts
    }
  end

  # def self.fetch_holidays(year, month)
  #   month_year = "#{month}-#{year}"
  #   Holiday.for_country('CZ')
  #          .for_month_year(month_year)
  #          .pluck(:date)
  #          .map(&:to_s)
  # rescue StandardError => e
  #   Rails.logger.error("Failed to fetch holidays: #{e.message}")
  #   []
  # end
end