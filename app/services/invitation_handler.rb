# app/services/invitation_handler.rb
class InvitationHandler

  def self.process(email:, company:, sender:, contract:)
    new(email, company, sender, contract).process
  end

  def initialize(email, company, sender, contract)
    @email = email
    @company = company
    @sender = sender
    @contract = contract
    @invited_user = User.find_by(email: @email)
  end
 
  
  def process
    return false if inviting_self?
    
    if existing_user?
      # If invited - user with pending invitation: existing_user_notification
      handle_existing_user
    elsif invited_user?
      # If invited the existing user: sending CompanyConnectionMailer.new_user_invitation
      send_company_interest_notification
    else
      # IF NEW: using Devise invitable gem
      @contract.save  
      handle_new_user
    end
  end
 
private
 
  def inviting_self?
    @sender.email == @email
  end
 
  def existing_user?
    User.exists?(email: @email, invitation_token: nil)
  end

  # If user invited via devise invitable
  def invited_user?
    @invited_user.present? && @invited_user.invitation_token.present?
  end
  
  def handle_new_user
    User.invite!({ email: @email, skip_workspace_setup: true }, @sender)
  end

  def handle_existing_user
    CompanyConnectionMailer.existing_user_notification(
      sender: @sender,
      user: @invited_user,
      company: @company,
      contract: @contract
    ).deliver_now
  end
 
  # TODO: Check if this is correct and solve if needed
  # Using custom new_user_invitation - not devise method!
  def send_company_interest_notification
    CompanyConnectionMailer.new_user_invitation(
      sender: @sender,  
      email: @email,
      company: @company,
      contract: @contract,
      token: @invited_user.invitation_token
    ).deliver_now
  end
  
end