require 'prawn'
require 'prawn/table'

class PdfGenerator
  def self.attendance_report(data)
    pdf = Prawn::Document.new

    roboto_font_path = "#{Rails.root}/app/assets/fonts/Roboto-Regular.ttf"
    roboto_bold_path = "#{Rails.root}/app/assets/fonts/Roboto-Bold.ttf"

    if File.exist?(roboto_font_path)
      pdf.font_families.update(
        "Roboto" => {
          normal: roboto_font_path,
          bold: roboto_bold_path
        }
      )
      pdf.font "Roboto"
    else
      pdf.font "Helvetica"
    end
    
    # Format date
    date_obj = data[:selected_date].is_a?(Date) ? data[:selected_date] : Date.parse(data[:selected_date].to_s)
    month_year = I18n.l(date_obj, format: "%B %Y").capitalize
    
    # Header
    pdf.font_size(18) { pdf.text "DOCHÁZKA", align: :center }
    pdf.move_down 10
    
    pdf.font_size(10) do
      pdf.text "Zaměstnavatel: #{data[:company_name]}"
      pdf.move_down 2.5
      pdf.text "Zaměstnanec: #{data[:employee_name] || data[:user_name]}"
      pdf.move_down 2.5
      pdf.text "Výkaz práce - #{month_year}"
    end
    
    pdf.font_size(10)
    pdf.move_down 10
    
    # Table data
    table_data = []
    
    # Headers
    table_data << [{ content: "Výkaz - #{month_year}", colspan: 6 }]
    table_data << [
      { content: "Den", rowspan: 2 },
      { content: "Pracovní doba", colspan: 2 },
      { content: "Přestávka", colspan: 2 },
      { content: "Celkem hodin", rowspan: 2 }
    ]
    table_data << ["Začátek", "Konec", "Začátek", "Konec"]
    
    # Days data
    data[:days].each do |day|
      day = day.with_indifferent_access
      
      if day[:event].present?
        table_data << [
          day[:day],
          day[:event][:description],
          "",
          "",
          "",
          "-"
        ]
      elsif day[:log].present?
        table_data << [
          day[:day],
          format_time(day[:log][:start_time]),
          format_time(day[:log][:end_time]),
          format_time(day[:log][:break_start]),
          format_time(day[:log][:break_end]),
          format_duration(day[:log][:duration])
        ]
      else
        table_data << [day[:day], "-", "-", "-", "-", "-"]
      end
    end
    
    # Total row
    table_data << ["Spolu", "", "", "", "", format_duration(data[:total_hours])]
    
    # Create table
    pdf.table(table_data, width: pdf.bounds.width * 0.8, position: :center) do
      cells.borders = [:left, :right, :top, :bottom]
      cells.padding = [2, 5]
      cells.align = :center
      
      # Header styling
      row(0..2).font_style = :bold
      
      # Total row styling
      row(-1).font_style = :bold
      
      # Column widths
      columns(0).width = pdf.bounds.width * 0.1  # Day column
      columns(1..4).width = pdf.bounds.width * 0.15  # Time columns
      columns(5).width = pdf.bounds.width * 0.1  # Total hours column
    end
    
    pdf.move_down 10
    
    # Footer and signatures
    pdf.text "Celkový čas: #{format_duration(data[:total_hours])} hod."
    
    # Add summary fields if included
    if data[:include_summary_fields]
      pdf.move_down 5
      pdf.text "Odpracované dny: #{data[:days_worked]}"
      pdf.move_down 5
      pdf.text "Dovolená: #{data[:event_counts][:vacation]}"
      pdf.move_down 5
      pdf.text "Nemocenská: #{data[:event_counts][:illness]}"
      pdf.move_down 5
      pdf.text "Návštěva lékaře: #{data[:event_counts][:day_care]}"
      pdf.move_down 5
      pdf.text "OČR: #{data[:event_counts][:family_sick]}"
      pdf.move_down 5
      pdf.text "Jiné absence: #{data[:event_counts][:other]}"
    end
    
    pdf.move_down 10
    pdf.text "Podpis zaměstnance: "
    pdf.move_down 5
    pdf.text "Datum: "
    
    # Generate filename
    filename = "vykaz_prace_#{I18n.transliterate(month_year).downcase.gsub(' ', '_')}.pdf"
    
    # Return the PDF as a string and the filename
    [pdf.render, filename]
  end
  
  def self.format_time(time)
    return "" unless time
    Time.parse(time.to_s).strftime("%H:%M")
  rescue
    ""
  end
  
  def self.format_duration(seconds)
    return "" unless seconds
    (seconds.to_f / 3600).round(1).to_s
  end
end