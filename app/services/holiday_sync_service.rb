class HolidaySyncService
  # Accepts keyword arguments for country, language, and year
  def initialize(country: 'CZ', language: 'CS', year: Date.current.year)
    @country = country
    @language = language
    @year = year.to_i
  end

  def sync
    (1..12).each do |month|
      sync_month(month)
    end
  end

  private

  def sync_month(month)
    last_day = Date.new(@year, month, -1).day

    puts "\n\n--------------------------------------------------------- language: #{@language} "


    url = URI("https://openholidaysapi.org/PublicHolidays?countryIsoCode=#{@country}&languageIsoCode=#{@language}&validFrom=#{@year}-#{month}-01&validTo=#{@year}-#{month}-#{last_day}")
    
    puts "\n\n--------------------------------------------------------- url: #{url} "

    begin
      response = Net::HTTP.get(url)
      holidays = JSON.parse(response)

      unless holidays.is_a?(Array)
        Rails.logger.error("Invalid response format for #{month}/#{@year}: Expected Array, got #{holidays.class}")
        return
      end

      holidays.each do |holiday_data|
        unless holiday_data.is_a?(Hash)
          Rails.logger.warn("Skipping non-hash holiday data for #{month}/#{@year}: #{holiday_data.inspect}")
          next
        end
        
        Rails.logger.debug("Processing holiday data for #{month}/#{@year}: #{holiday_data.inspect}")
        begin
          start_date_str = holiday_data['startDate']
          unless start_date_str.is_a?(String)
            Rails.logger.error("Invalid 'startDate' format for #{month}/#{@year}: Expected String, got #{start_date_str.class}. Data: #{holiday_data.inspect}")
            next
          end
          
          date = Date.parse(start_date_str)
          month_year = "#{month}-#{@year}"
          
          # Handle multi-language name structure
          name_data = holiday_data['name']
          name = "Unknown Holiday Name" 
          if name_data.is_a?(Array) && name_data.first.is_a?(Hash) && name_data.first.key?('text')
            name = name_data.first['text']
          elsif name_data.is_a?(String)
             # Handle case where name might sometimes be a simple string (future-proofing)
             name = name_data
          else
             Rails.logger.warn("Unexpected name format for #{month}/#{@year}: #{name_data.inspect}. Using default. Data: #{holiday_data.inspect}")
          end
          
          # Assuming description is simpler or nullable for now
          description = holiday_data['description']

          Holiday.find_or_create_by!(
            date: date,
            country: @country,
            month_year: month_year,
            name: name,
            description: description 
          )
        rescue ArgumentError => e
          Rails.logger.error("Failed to parse date for holiday data in #{month}/#{@year}: #{e.message}. Data: #{holiday_data.inspect}")
          next
        rescue StandardError => e
          Rails.logger.error("Failed to process holiday data for #{month}/#{@year}: #{e.message}. Data: #{holiday_data.inspect}")
          next
        end
      end
    rescue JSON::ParserError => e
      Rails.logger.error("Failed to parse JSON response for #{month}/#{@year}: #{e.message}")
    rescue StandardError => e
      Rails.logger.error("Failed to sync holidays for #{month}/#{@year}: #{e.message}")
    end
  end
end 