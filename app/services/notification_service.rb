class NotificationService
  class << self
    # Create work assignment notification
    def create_work_assignment_notification(work_assignment, action = 'added')
      return unless work_assignment.contract.user # Skip if no user associated
      
      notification_type = "work_assignment_#{action}"
      work = work_assignment.work
      
      title = case action
      when 'added'
        I18n.t('notifications.work_assignment.added.title', work_title: work.title)
      when 'removed'
        I18n.t('notifications.work_assignment.removed.title', work_title: work.title)
      end
      
      message = case action
      when 'added'
        I18n.t('notifications.work_assignment.added.message', 
               work_title: work.title,
               location: work.location,
               date: I18n.l(work.scheduled_start_date, format: :long))
      when 'removed'
        I18n.t('notifications.work_assignment.removed.message', work_title: work.title)
      end
      
      create_notification(
        user: work_assignment.contract.user,
        company: work_assignment.company,
        notifiable: work_assignment,
        notification_type: notification_type,
        title: title,
        message: message,
        data: {
          work_id: work.id,
          work_title: work.title,
          work_location: work.location,
          contract_id: work_assignment.contract_id
        }
      )
    end
    
    # Create work status change notification
    def create_work_status_notification(work, old_status)
      work.work_assignments.includes(contract: :user).each do |assignment|
        next unless assignment.contract.user
        
        create_notification(
          user: assignment.contract.user,
          company: work.company,
          notifiable: work,
          notification_type: 'work_status_changed',
          title: I18n.t('notifications.work_status.changed.title', work_title: work.title),
          message: I18n.t('notifications.work_status.changed.message',
                         work_title: work.title,
                         old_status: I18n.t("works.#{old_status}"),
                         new_status: I18n.t("works.#{work.status}")),
          data: {
            work_id: work.id,
            old_status: old_status,
            new_status: work.status
          }
        )
      end
    end
    
    # Mark all notifications as read for a user
    def mark_all_as_read(user)
      user.notifications.unread.update_all(read_at: Time.current)
    end
    
    # Clean up old notifications
    def cleanup_old_notifications
      Notification.old.destroy_all
    end
    
    private
    
    def create_notification(attributes)
      notification = Notification.create!(attributes)
      
      # Send email if Plus tier (placeholder for now)
      if notification.company.current_subscription&.plus_or_higher? && should_send_email?(notification)
        # WorkAssignmentMailer.notification_email(notification).deliver_later
      end
      
      notification
    end
    
    def should_send_email?(notification)
      # Check user preferences when implemented
      %w[work_assignment_added work_assignment_removed].include?(notification.notification_type)
    end
  end
end