class DailyActivitiesController < ApplicationController
  before_action :set_tenant_company
  before_action :set_contract
  before_action :set_daily_activity, only: [:update, :destroy]
  before_action :set_work, only: [:start_work_activity]

  def index
    if params[:start_date] && params[:end_date]
      # Date range query for reports
      start_date = Date.parse(params[:start_date]).beginning_of_day
      end_date = Date.parse(params[:end_date]).end_of_day
      
      @activities = DailyActivity
        .includes(:work)
        .where(
          user: current_user,
          company: @company,
          contract: @contract,
          start_time: start_date..end_date
        ).order(start_time: :asc)
    else
      # Single day query
      start_of_day = Date.parse(params[:date] || Date.current.to_s).beginning_of_day
      end_of_day = start_of_day.end_of_day
      
      @activities = DailyActivity
        .includes(:work)
        .where(
          user: current_user,
          company: @company,
          contract: @contract,
          start_time: start_of_day..end_of_day
        ).order(created_at: :desc)
    end

    render json: @activities.as_json(include: :work)
  end

  def create
    @daily_activity = DailyActivity.new(daily_activity_params)
    @daily_activity.user = current_user
    @daily_activity.company = @company
    @daily_activity.contract = @contract
    
    if @daily_activity.save
      render json: @daily_activity, status: :created
    else
      render json: { errors: @daily_activity.errors.full_messages }, 
             status: :unprocessable_entity
    end
  end

  def update
    if @daily_activity.update(daily_activity_params)
      render json: @daily_activity
    else
      render json: { errors: @daily_activity.errors.full_messages }, 
             status: :unprocessable_entity
    end
  end

  def destroy
    if @daily_activity.destroy
      render json: { 
        message: activities_t('messages.activity_deleted')
      }
    else
      render json: { errors: @daily_activity.errors.full_messages }, 
             status: :unprocessable_entity
    end
  end
  
  # Start a work activity
  def start_work_activity
    # Check if user is assigned to this work
    work_assignment = @work.work_assignments.find_by(contract: @contract)
    unless work_assignment
      return render json: { error: activities_t('errors.not_assigned_to_work') }, status: :forbidden
    end
    
    # Check for active work activity
    active_activity = DailyActivity.where(
      user: current_user,
      company: @company,
      end_time: nil,
      activity_type: ['travel_to_work', 'work_at_location', 'work_remote']
    ).first
    
    if active_activity
      return render json: { error: activities_t('errors.active_work_exists') }, status: :unprocessable_entity
    end
    
    # Create new work activity
    @daily_activity = DailyActivity.new(
      user: current_user,
      company: @company,
      contract: @contract,
      work: @work,
      work_assignment: work_assignment,
      activity_type: params[:activity_type] || 'work_at_location',
      description: params[:description] || "#{@work.title}",
      start_time: Time.current,
      daily_log_id: params[:daily_log_id]
    )
    
    if @daily_activity.save
      # Create work session if starting actual work (not travel)
      if ['work_at_location', 'work_remote'].include?(@daily_activity.activity_type)
        work_session = @work.work_sessions.create!(
          user: current_user,
          company: @company,
          work_assignment: work_assignment,
          daily_activity: @daily_activity,
          start_time: Time.current,
          status: 'in_progress'
        )
        
        render json: { 
          activity: @daily_activity.as_json(include: [:work, :work_assignment]),
          work_session: work_session,
          message: activities_t('messages.work_activity_started')
        }, status: :created
      else
        render json: { 
          activity: @daily_activity.as_json(include: [:work, :work_assignment]),
          message: activities_t('messages.travel_activity_started')
        }, status: :created
      end
    else
      render json: { errors: @daily_activity.errors.full_messages }, status: :unprocessable_entity
    end
  end
  
  # Get current work activity
  def current_work_activity
    activity = DailyActivity.where(
      user: current_user,
      company: @company,
      end_time: nil,
      activity_type: ['travel_to_work', 'work_at_location', 'work_remote']
    ).includes(:work, :work_assignment).first
    
    if activity
      work_session = WorkSession.find_by(daily_activity: activity, status: 'in_progress')
      render json: { 
        activity: activity.as_json(include: [:work, :work_assignment]),
        work_session: work_session
      }
    else
      render json: { activity: nil, work_session: nil }
    end
  end
  
  # End current work activity
  def end_work_activity
    activity = DailyActivity.find_by(
      id: params[:id],
      user: current_user,
      company: @company,
      end_time: nil
    )
    
    unless activity
      return render json: { error: activities_t('errors.activity_not_found') }, status: :not_found
    end
    
    activity.end_time = Time.current
    
    # Add notes to work session if provided
    if params[:notes].present? && activity.work_session
      activity.work_session.update(check_in_notes: params[:notes])
    end
    
    if activity.save
      # End associated work session if exists
      work_session = WorkSession.find_by(daily_activity: activity, status: 'in_progress')
      if work_session
        work_session.update!(
          end_time: Time.current,
          status: 'completed'
        )
      end
      
      render json: { 
        activity: activity.as_json(include: [:work, :work_assignment]),
        work_session: work_session,
        message: activities_t('messages.work_activity_ended')
      }
    else
      render json: { errors: activity.errors.full_messages }, status: :unprocessable_entity
    end
  end

  private

  def set_daily_activity
    @daily_activity = DailyActivity.find_by(
      id: params[:id],
      user: current_user,
      company: @company
    )
    
    unless @daily_activity
      render json: { error: 'Aktivita nebyla nalezena.' }, status: :not_found
    end
  end

  def set_contract
    @contract = current_user.contracts.find_by(company: @company)
    unless @contract
      redirect_to root_path, alert: 'Nejdřive sa musíte připojit k Pracovnímu prostoru.'
    end
  end

  def set_tenant_company
    @company = ActsAsTenant.current_tenant
  end

  def daily_activity_params
    params.require(:daily_activity)
          .permit(:description, :start_time, :end_time, :daily_log_id, :work_id, 
                  :work_assignment_id, :activity_type, :location_coordinates)
  end
  
  def set_work
    @work = @company.works.find_by(id: params[:work_id])
    unless @work
      render json: { error: activities_t('errors.work_not_found') }, status: :not_found
    end
  end
  
  # Helper method for controller-specific translations
  def activities_t(key, **options)
    t("controllers.daily_activities.#{key}", **options)
  end

end