class UserSettingsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_user_setting

  def index
    render json: @user_setting
  end

  def edit
  end

  def update
    @user_setting = current_user.user_setting

    if @user_setting.update(user_setting_params)
      render json: @user_setting, status: :ok
    else
      render json: @user_setting.errors, status: :unprocessable_entity
    end
  end

  private

  def set_user_setting
    @user_setting = current_user.user_setting
  end

  def user_setting_params
    params.require(:user_setting).permit(:start_time, :end_time, :break_start, :auto_break, :country_code, :language_code)
  end
end