class CronController < ApplicationController
  skip_before_action :require_login
  skip_before_action :set_current_tenant
  skip_before_action :verify_authenticity_token
  
  # Validate Render webhook token
  before_action :authenticate_webhook
  
  def daily_team_status
    TeamStatusReportService.send_daily_reports
    render json: { status: "success", message: "Daily team status reports sent" }
  end

  # Create a new "Cron Job"
  # Set the schedule to 30 7 * * * (7:30 AM UTC, which is 8:30 AM CET in standard time)
  # For the command, use: curl -X POST https://your-app.onrender.com/cron/daily_team_status -H "webhook-signature: ${RENDER_WEBHOOK_SECRET}"
  
  private
  
  def authenticate_webhook
    payload = request.raw_post
    # signature = request.headers['Render-Webhook-Signature']
    signature = request.headers['webhook-signature']
    # Header Name Discrepancy: The official Render documentation states the header name is webhook-signature.
    # Your code uses Render-Webhook-Signature. You should update your code to use request.headers['webhook-signature'].
    # https://render.com/docs/webhooks
    
    # Get the webhook secret from environment
    webhook_secret = Rails.application.credentials.dig(:render, :webhook_secret)
    # webhook_secret = Rails.application.credentials.render[:webhook_secret]
    
    # Calculate expected signature
    expected_signature = OpenSSL::HMAC.hexdigest('sha256', webhook_secret, payload)
    puts "Expected Signature: #{expected_signature}"
    puts "Received Signature: #{signature}"
    
    
    unless ActiveSupport::SecurityUtils.secure_compare(expected_signature, signature)
      render json: { error: "Unauthorized" }, status: :unauthorized
    end
  end
end