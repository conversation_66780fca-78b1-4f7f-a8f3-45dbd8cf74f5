class BookingsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_tenant_company
  before_action :set_booking, except: [:index, :fetch]
  before_action :authorize_view, only: [:index, :fetch, :show]
  before_action :authorize_booking_actions, only: [:update, :confirm, :cancel, :convert_to_work]


  def index
  end
  
  def fetch
    allowed_statuses = %w[pending confirmed cancelled]
    status = params[:status]
    
    @bookings = @company.bookings.includes(:booking_link, :work).order(created_at: :desc)
    @bookings = @bookings.where(status: status) if allowed_statuses.include?(status)
    
    render json: { 
      bookings: @bookings.as_json(include: [:booking_link, :work]), 
      company_slug: @company.slug 
    }, status: :ok
  end

  def show
  end


  def update
    original_details = {
      preferred_date: @booking.preferred_date.to_s,
      preferred_period: @booking.preferred_period,
      specific_time: @booking.specific_time&.strftime('%H:%M')
    }

    if @booking.update(booking_params)
      BookingMailer.booking_updated_email(@booking, original_details).deliver_now if @booking.client_email.present?
      render json: { 
        success: true,
        booking: @booking,
        message: booking_t('messages.updated') 
      }
    else
      render :show
    end

  end

  # def destroy
  #   @booking.destroy
  #   redirect_to bookings_path, notice: 'Rezervace byla smazána'
  # end

  def confirm
    if params[:booking].present?
      time = booking_params[:specific_time] || nil
      date = booking_params[:preferred_date] || @booking.preferred_date
    else
      time = nil
      date = @booking.preferred_date
    end
    
    success = @booking.confirm(date, time)
    
    if success
      # Send confirmation email to the client
      BookingMailer.booking_confirmed_email(@booking).deliver_now if @booking.client_email.present?

      render json: {
        success: true, booking: @booking,
        message: booking_t('messages.confirmed') 
      }
    else
      render json: { success: false, errors: @booking.errors.full_messages }
    end
  end

  def convert_to_work
    work = @booking.convert_to_work
    if work
      render json: {
        success: true, booking: @booking.reload, work: work,
        message: booking_t('messages.converted_to_work') 
      }
    else
      render json: { success: false, errors: @booking.errors.full_messages }
    end
  end

  def cancel
    success = @booking.cancel
    if success
      # Send cancellation email to the client
      BookingMailer.booking_cancelled_email(@booking).deliver_now if @booking.client_email.present?
      render json: {
        success: true, booking: @booking,
        message: booking_t('messages.cancelled') 
      }
    else
      render json: { success: false, errors: @booking.errors.full_messages }
    end
  end



  private
  
  def authorize_view
    authorize! @company, to: :view_bookings?
  end

  def authorize_booking_actions
    authorize! @company, to: :manage_booking_actions?
  end

  # Helper method for controller-specific translations
  # Makes translation calls cleaner and more maintainable
  def booking_t(key, **options)
    t("controllers.bookings.#{key}", **options)
  end

  def set_tenant_company
    @company = ActsAsTenant.current_tenant
  end

  def set_booking
    @booking = @company.bookings.find(params[:id])
  end

  def booking_params
    params.require(:booking).permit(
      :client_name,
      :client_email,
      :client_phone,
      :preferred_date,
      :preferred_period,
      :specific_time,
      :message,
      :status,
      :confirmed_time,
      :location,
      :latitude,
      :longitude,
      :duration
    )
  end
end