class ApplicationController < ActionController::Base
  include LocaleHandler

  layout :determine_layout
  before_action :http_basic_authenticate
  
  before_action :require_login # FIXME: URGENT:  Only logged in users allowed to use the app. Split landingpages.
  before_action :configure_permitted_parameters, if: :devise_controller?
  before_action :set_current_tenant
  before_action :check_and_refresh_tenant_session
  before_action :set_locale

  skip_before_action :require_login, if: :devise_controller?
  skip_before_action :set_current_tenant, only: [:after_sign_in_path_for]

  rescue_from ActionPolicy::Unauthorized, with: :user_not_authorized
  rescue_from ActiveRecord::RecordNotFound, with: :record_not_found

  # TODO: protect_from_forgery with: :exception
  # TODO: First create a non user landing page to redirect to when not logged in and set it as a root path
  # TODO: Test the usuall ruby on rails app security breaks vectors

  #protect_from_forgery with: :exception

  private

  def determine_layout
    puts "------------------------- Current controller: #{params[:controller]} ------------------------"
    if current_user.present?
      'application'
    elsif params[:controller] == 'public_bookings'
      'tymlink'
    elsif params[:controller] == 'public_meetings'
      'meeting'
    else
      'welcome'
    end
  end

  def configure_permitted_parameters
    # Existing permit for user update
    devise_parameter_sanitizer.permit(:user_update, keys: [:password, :password_confirmation, :current_password])
    # Explicitly permit params for the reset password action
    devise_parameter_sanitizer.permit(:reset_password, keys: [:reset_password_token, :password, :password_confirmation])
  end

  def require_login
    if devise_password_reset_flow?
      #Skipping require_login for Devise password reset
      return
    end

    unless user_signed_in?
      # Require login triggered: User not signed in
      flash[:error] = t("flash.registration_needed", default: "Registration needed.")
      redirect_to root_path
    else
      Rails.logger.debug("Require login passed: User signed in as #{current_user.email}")
      puts "\n ---------- Require login passed: User signed in as #{current_user.email} \n"
    end
  end

  def devise_password_reset_flow?
    params[:controller] == 'devise/passwords' && %w[edit update].include?(params[:action])
  end
  
  def user_not_authorized(exception)
    policy_name = exception.policy.to_s.underscore

    flash[:alert] = t "#{policy_name}.#{exception.rule}", scope: "action_policy", default: "Nemáte oprávnění."
    redirect_to root_path
  end

  def set_current_tenant
    company_id = session[:tenant_id]
    if company_id.present?
      set_tenant_for_company(company_id)
    else
      ActsAsTenant.current_tenant = nil
    end
  end

  def set_tenant_for_company(company_id)
    company = Company.find(company_id)
    if company && current_user.companies.include?(company)
      ActsAsTenant.current_tenant = company
    else
      handle_unauthorized_access
    end
  end

  def handle_unauthorized_access
    render json: { error: "Unauthorized access" }, status: :forbidden
    reset_tenant_session
  end

  def after_accept_path_for(resource)
    puts "\n ---------- after_accept_path_for ----- \n\n"
    set_tenant_id_for_resource(resource)
    # Redirect to a page where they can manage pending contracts
    company_connections_path
  end
  
  # Tenant is Company which User works for based on the Contract
  # Tenant is not set from subdomain, nor params, but in session[:tenant_id]
  # Tenant will be set to request header when SPA, thus the current setup 
  def after_sign_in_path_for(resource)
    puts "\n ---------- after_sign_in_path_for ----- \n\n"
    set_tenant_id_for_resource(resource)
    super(resource)
    # If after_accept_path is not enough
    # if Contract.exists?(email: resource.email, user_id: nil)
    #   # Has pending contracts to handle
    #   pending_contracts_path
    # else
    #   # Normal login flow
    #   set_tenant_id_for_resource(resource)
    #   super(resource)
    # end
  end

  def set_tenant_id_for_resource(resource)
    primary_role = resource.company_user_roles.find_by(is_primary: true)
    if primary_role
      session[:tenant_id] = primary_role.company_id
    else
      determine_and_set_tenant(resource)
    end
  end

  # Default logic if no primary is set
  def determine_and_set_tenant(resource)
    role = resource.company_user_roles.first
    if role
      session[:tenant_id] = role.company_id
    else
      handle_no_company(resource)
    end
  end
  
  # Old logic for tenant setting
  # def determine_and_set_tenant(resource)
  #   contract = resource.contracts.first
  #   company = contract&.company || resource.companies.first
  #   if company
  #     session[:tenant_id] = company.id
  #   else
  #     handle_no_company(resource)
  #   end
  # end

  def after_sign_out_path_for(_resource_or_scope)
    if session[:tenant_id].present? && current_user
      company = Company.find_by(id: session[:tenant_id])
      current_user.set_primary_company(company) if company
    end
    super
  end

  def handle_no_company(resource)
    session[:tenant_id] = nil
  end
  
  def reset_tenant_session
    session.delete(:tenant_id)
    redirect_to root_path, alert: "You do not have access here."
  end

  def check_and_refresh_tenant_session
    if user_signed_in? && session[:tenant_id].nil? && current_user.company_user_roles.exists?
      # User is logged in but lost tenant session - restore it
      set_tenant_id_for_resource(current_user)
      set_current_tenant
    end
  end

  # Only uncomment if you want session expiration separate from Devise timeout
  # def check_session_expiration
  #   if session[:tenant_id] && session[:last_activity_time] && Time.now - session[:last_activity_time] > 500.minutes.to_i
  #     reset_tenant_session
  #     redirect_to login_path, alert: "Your session has expired."
  #   else
  #     session[:last_activity_time] = Time.now
  #   end
  # end

  def http_basic_authenticate
    #authenticate_or_request_with_http_basic("Restricted Area") do |username, password|
      #username == ENV["HTTP_USERNAME"] && password == ENV["HTTP_PASSWORD"]
      #username == "tester" && password == "nasacesta"
    #end
  end

  # def skip_login_for_devise?
  #   # You can customize this based on the controller or actions you want to skip
  #   devise_controllers = ['devise/sessions', 'devise/registrations']
  #   devise_controllers.include?(params[:controller])
  # end

  def record_not_found(error)
    render json: { success: false, error: error.message }, status: :not_found
  end

  def set_locale
    
    # I18n.locale = params[:locale] || cookies[:locale] || I18n.default_locale
    
    # New logic for locale setting
     
    # Locale from URL path param
    locale_from_path = params[:locale]
    if locale_from_path.present? && I18n.available_locales.map(&:to_s).include?(locale_from_path)
      I18n.locale = locale_from_path
      # Update cookie if path locale is different and valid, ensuring it's kept in sync
      if cookies[:locale] != locale_from_path
        cookies[:locale] = { value: locale_from_path, expires: 1.year.from_now, same_site: :lax, path: '/' }
      end
      return
    end

    # Locale from cookie
    locale_from_cookie = cookies[:locale]
    if locale_from_cookie.present? && I18n.available_locales.map(&:to_s).include?(locale_from_cookie)
      I18n.locale = locale_from_cookie
      return
    end

    # 3. Default locale (fallback)
    I18n.locale = I18n.default_locale
  end

  # Automatically add locale to Rails-generated URLs
  def default_url_options(options = {})
    # Always include the locale in the path, consistent with path-based locale strategy.
    # This ensures that links generated by url_for, path helpers, etc., include the current locale.
    { locale: I18n.locale }.merge(options)
  end

end
