class PublicMeetingsController < ApplicationController
  skip_before_action :require_login
  before_action :find_meeting_user
  
  def show
    if @meeting_user
      puts "--------- public meeting show #{@meeting_user.inspect} -------- "
      @company = @meeting_user.meeting.company
      puts "--------- public meeting show company #{@company.inspect} -------- "
      render :show, layout: 'meeting'
    else
      render json: { error: public_meetings_t('errors.link_inactive') }, status: :forbidden
    end
  end
  
  def meeting
    if @meeting_user
      # If meeting is confirmed or meeting dates have passed, return no data
      meeting = @meeting_user.meeting
      if meeting.confirmed_date.present? || (meeting.day_options.keys.any? { |date| Date.parse(date) < Date.today })
        render json: { status: 'completed' }
        return
      end

      # Get data for the calendar
      data = {
        meeting: @meeting_user.meeting.as_json(only: [:id, :title, :description, :place, :day_options, :confirmed_date]),
        user: @meeting_user.as_json(only: [:id, :email, :selected_dates]),
        events: []
      }

      # Get date range from day_options
      dates = @meeting_user.meeting.day_options.keys.map { |d| DateTime.parse(d).to_date rescue nil }.compact
      start_date = dates.min
      end_date = dates.max

      if start_date && end_date
        # Load holidays first because here we laod it for unregistered/unsigned users too
        holidays = []
        (start_date..end_date).map(&:month).uniq.each do |month|
          year = start_date.year
          holidays += fetch_holidays(year, month)
        end
        data[:holidays] = holidays
            
        # If the user is part of the company, load their events
        if @meeting_user.contract&.user&.present?
          user = @meeting_user.contract.user
          company = @meeting_user.meeting.company

          puts "\n --------- user #{user.inspect} -------- \n\n"
          
          # Load events
          events = user.events
            .where(contract: user.contracts.where(company: company))
            .where('start_time <= ? AND end_time >= ?', end_date.end_of_day, start_date.beginning_of_day)
          
          puts "\n --------- user events #{user.events.inspect} -------- \n\n"
          puts "\n --------- events #{events.inspect} -------- \n\n"

          # Load works
          works = company.works
            .joins(:work_assignments)
            .where(work_assignments: { contract_id: user.contracts.where(company: company).pluck(:id) })
            .where('scheduled_start_date <= ? AND scheduled_end_date >= ?', end_date, start_date)
          
          meetings = Meeting
            .joins(:meeting_users)
            .where(meeting_users: { user_id: user.id })
            .where.not(id: @meeting_user.meeting.id)
            .where.not(confirmed_date: nil)
            .where('confirmed_date >= ? AND confirmed_date <= ?', start_date.beginning_of_day, end_date.end_of_day)
            .distinct

          # Add to data
          data[:events] = events.as_json(only: [:id, :title, :start_time, :end_time, :event_type])
          data[:works] = works.as_json(only: [:id, :title, :scheduled_start_date, :scheduled_end_date])
          data[:meetings] = meetings.as_json(only: [:id, :title, :confirmed_date])
           
        end
      end
          
      render json: data
    else
      render json: { error: public_meetings_t('errors.invalid_token') }, status: :not_found
    end
  end
  
  def update
    if @meeting_user && @meeting_user.meeting.confirmed_date.nil?

      if @meeting_user.selected_dates.present? && !@meeting_user.selected_dates.empty?
        render json: { 
          success: false, 
          message: public_meetings_t('messages.preferences_already_sent'),
          messageType: 'alert'
        }
        return
      end
      
      if @meeting_user.update(selected_dates: params[:selected_dates], status: 'replied')
        # Check if all users have responded
        if @meeting_user.meeting.all_responses_submitted?
          find_best_common_time
          # potrebujeme zrusit zobrazovanie ownera - teda napriklad tym, ze budeme zaznamenavat confirmed status pre meeting user 
        end
        
        # redirect_to root_path, notice: 'Vaše preferene byly odeslány. Na e-mail vám přijde zpráva s potvrzením termínu schůzky.'
        render json: { 
          success: true, 
          message: public_meetings_t('messages.preferences_sent'),
          messageType: 'success'
        }
      else
        render json: { success: false, errors: @meeting_user.errors.full_messages }, status: :unprocessable_entity
      end
    else
      render json: { 
        success: false, 
        message: public_meetings_t('errors.meeting_confirmed_or_invalid_token'),
        messageType: 'error'
      }
    end
  end
  
  def find_best_common_time
    @meeting = @meeting_user.meeting
    @company = @meeting.company
    best_time = @meeting.find_best_common_time
    
    if best_time
      time_obj = Time.zone.parse(best_time).utc
      @meeting.update(confirmed_date: time_obj, status: 'confirmed')
      
      creator = @meeting.created_by
      if creator && !@meeting.meeting_users.exists?(user_id: creator.id) && !@meeting.meeting_users.exists?(email: creator.email)
        creator_contract = @company.contracts.find_by(user_id: creator.id)

        if creator_contract
          @meeting.meeting_users.create!(
            contract: creator_contract,
            user: creator,
            status: 'confirmed',
          )
        else
          # If no contract, just use email
          @meeting.meeting_users.create!(email: creator.email)
        end
        @meeting.meeting_users.reload
      end

      # Send confirmation emails
      @meeting.meeting_users.each do |meeting_user|
        MeetingMailer.confirmation_email(meeting_user).deliver_now
      end
    end
  end
  


  private

  # Helper method for controller-specific translations
  # Makes translation calls cleaner and more maintainable
  def public_meetings_t(key, **options)
    t("controllers.public_meetings.#{key}", **options)
  end
  
  def find_meeting_user
    @meeting_user = MeetingUser.find_by(token: params[:token])
  end
  
  def fetch_holidays(year, month)
    month_year = "#{month}-#{year}"
    
    # Get country code with fallbacks:
    # 1. User's setting
    # 2. Map from I18n.locale
    # 3. CZ as default
    locale_to_country = {
      cs: 'CZ',
      sk: 'SK',
    }
    
    country_code = current_user&.user_setting&.country_code || 
                  locale_to_country[I18n.locale] || 
                  'CZ'
                  
    Holiday.for_country(country_code)
           .for_month_year(month_year)
           .pluck(:date)
           .map(&:to_s)
  rescue StandardError => e
    Rails.logger.error("Failed to fetch holidays: #{e.message}")
    []
  end
end