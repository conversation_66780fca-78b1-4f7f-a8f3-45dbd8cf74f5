class AssignmentsController < ApplicationController
  before_action :authenticate_user! 

  #TODO: Add more security layers
  #Authorization: Add checks to restrict switching to unauthorized tenants.
  #before_action :check_current_company
  #before_action :permitted_user?
  #before_action :set_current_tenant
  #TODO: Consider securing against Throttling
  #TODO: For high-privilege tenants, require additional confirmation (e.g., re-entering credentials or an OTP).
  #FIXME: not needing allow params?
  #FIXME: Gracefully handle scenarios where: A tenant association is removed mid-session.
  #FIXME: A user attempts to switch to a tenant that no longer exists.
  
  def index
    # Default scope will ensure only active roles are shown
    @company_user_roles = current_user.company_user_roles.includes(:company, :role)
  end

  # AXIOS CALL
  def switch
    tenant_id = params[:company_id]
    puts "--------- #{tenant_id} -------- "
    # Use unscoped to bypass default_scope and then check active status explicitly
    # This ensures we can properly handle the error message if a role exists but is inactive
    company_user_role = current_user.company_user_roles.find_by(company_id: tenant_id)
    puts "---------- current_user.company_user_roles: #{ current_user.company_user_roles.inspect } -------- "
    puts "-------- company_user_role:  #{company_user_role.inspect} --------"

    # Security checks including active status
    if company_user_role && company_user_role.active && valid_role?(company_user_role)
      current_user.set_primary_company(company_user_role.company)
      session[:tenant_id] = company_user_role.company_id

      Rails.logger.info "User #{current_user.id} switched to tenant #{company_user_role.company_id} at #{Time.current}"
      redirect_to root_path, notice: "Přepnuto na #{company_user_role.company.name}"
    else
      # More specific message based on the issue
      message = if company_user_role.nil?
                  "Nemáte oprávnění přepnout na vybranou firmu."
                elsif !company_user_role.active
                  "Váš přístup k této firmě byl pozastaven nebo ukončen."
                else
                  "Nemáte oprávnění přepnout na vybranou firmu."
                end
      redirect_to root_path, alert: message
    end
  end


  private 

  def valid_role?(company_user_role)
    %w[owner employee].include?(company_user_role.role.name)
  end

  # Not implemented yet, not neccessary
  def check_authorization(company_user_role)
    if company_user_role.role.name != "owner" && company_user_role.role.name != "employee"
      redirect_to root_path, alert: "Your role does not allow switching to this tenant."
    end
  end

  # Not implemented yet, not neccessary
  def check_current_company
    if company_user_role.company.archived?
      redirect_to root_path, alert: "The selected tenant is no longer active."
    end
  end

end
