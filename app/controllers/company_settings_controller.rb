class CompanySettingsController < ApplicationController
  
  include ActionPolicy::Controller

  before_action :authenticate_user!
  before_action :set_company_setting

  authorize :user, through: :current_user

  def edit
    authorize! @company_setting.company, to: :manage?
    render json: {
      company_setting: @company_setting,
      logo_url: @company.logo_url,
      original_logo_url: @company.original_logo_url
    }
  end

  def update
    authorize! @company_setting.company, to: :manage?

    if @company_setting.update(company_setting_params)
      render json: { success: true, company_setting: @company_setting, message: company_settings_t('messages.updated') }
    else
      # render with validation errors
      render json: { errors: @company_setting.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def update_logo
    authorize! @company_setting.company, to: :manage_logo?
    @company = @company_setting.company

    if logo_params[:logo].present?
      @company.logo.attach(logo_params[:logo])
      # Add error handling if attach fails or company becomes invalid
      if @company.valid? # Check validity after attach (before save if needed)
        # Assuming attach implicitly saves or handles persistence correctly with Active Storage
        # If not, uncomment the save call:
        # if @company.save
          render json: {
            success: true,
            logo_url: @company.logo_url,
            original_logo_url: @company.original_logo_url,
            message: company_settings_t('messages.logo_updated')
          }
        # else
        #   render json: { errors: @company.errors.full_messages }, status: :unprocessable_entity
        # end
      else
        # If attach makes the company invalid (e.g., content type validation)
        render json: { errors: @company.errors.full_messages }, status: :unprocessable_entity
      end
    else
      render json: { errors: [company_settings_t('errors.no_logo_file')] }, status: :unprocessable_entity
    end
  rescue => e # Catch potential errors during attach/validation
     render json: { errors: [company_settings_t('errors.logo_update_error', message: e.message)] }, status: :unprocessable_entity
  end

  private

  # Helper method for controller-specific translations
  # Makes translation calls cleaner and more maintainable
  def company_settings_t(key, **options)
    t("controllers.company_settings.#{key}", **options)
  end

  def set_company_setting
    @company = ActsAsTenant.current_tenant
    @company_setting = @company.company_setting
  end

  def company_setting_params
    params.require(:company_setting).permit(
        :break_duration,
        :auto_end,
        :auto_break,
        :allow_overtime,
        :timezone,
        :approve_vacations,
        :daily_team_reports
      )
  end

  def logo_params
    params.permit(:logo)
  end
end