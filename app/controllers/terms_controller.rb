class TermsController < ApplicationController
  
  skip_before_action :require_login

  def show
    locale = params[:locale] || I18n.locale
    type = params[:type]
    
    # Attempt to find locale-specific template first
    template_options = [
      "terms/#{type}_#{locale}",
      "terms/#{type}.#{locale}"
    ]
    
    # Try to find a template with one of the naming patterns
    template_to_render = nil
    template_options.each do |template|
      begin
        # Check if template exists
        lookup_context.find_template(template, [], true)
        template_to_render = template
        break
      rescue ActionView::MissingTemplate => e
        next
      end
    end
    
    # If no locale-specific template found, fallback to the default
    if template_to_render.nil?
      case type
      when 'tos'
        render 'terms/tos', layout: false
      when 'gdpr'
        render 'terms/gdpr', layout: false
      else
        head :not_found
      end
    else
      render template_to_render, layout: false
    end
  end
end