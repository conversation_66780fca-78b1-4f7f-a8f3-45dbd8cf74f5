class WorkSessionsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_tenant_company
  before_action :set_contract
  before_action :set_work_session, only: [:check_in, :check_out]
  
  # Check in at work location
  def check_in
    if @work_session.check_in_time.present?
      return render json: { error: sessions_t('errors.already_checked_in') }, status: :unprocessable_entity
    end
    
    # Validate location if coordinates provided
    if params[:latitude].present? && params[:longitude].present?
      @work_session.check_in_location = "#{params[:latitude]},#{params[:longitude]}"
      
      # Optional: Validate proximity to work location
      if @work_session.work.latitude.present? && @work_session.work.longitude.present?
        distance = calculate_distance(
          params[:latitude].to_f, params[:longitude].to_f,
          @work_session.work.latitude, @work_session.work.longitude
        )
        
        # If more than 500 meters away, warn but allow check-in
        if distance > 500
          @work_session.check_in_notes = sessions_t('messages.far_from_location', distance: distance.round)
        end
      end
    end
    
    @work_session.check_in_time = Time.current
    
    if @work_session.save
      render json: { 
        work_session: @work_session,
        message: sessions_t('messages.checked_in_successfully')
      }
    else
      render json: { errors: @work_session.errors.full_messages }, status: :unprocessable_entity
    end
  end
  
  # Check out from work location
  def check_out
    unless @work_session.check_in_time.present?
      return render json: { error: sessions_t('errors.not_checked_in') }, status: :unprocessable_entity
    end
    
    if @work_session.check_out_time.present?
      return render json: { error: sessions_t('errors.already_checked_out') }, status: :unprocessable_entity
    end
    
    @work_session.check_out_time = Time.current
    
    if @work_session.save
      render json: { 
        work_session: @work_session,
        message: sessions_t('messages.checked_out_successfully')
      }
    else
      render json: { errors: @work_session.errors.full_messages }, status: :unprocessable_entity
    end
  end
  
  private
  
  def set_work_session
    @work_session = WorkSession.find_by(
      id: params[:id],
      user: current_user,
      company: @company
    )
    
    unless @work_session
      render json: { error: sessions_t('errors.session_not_found') }, status: :not_found
    end
  end
  
  def set_contract
    @contract = current_user.contracts.find_by(company: @company)
    unless @contract
      render json: { error: sessions_t('errors.no_workspace_connection') }, status: :unauthorized
    end
  end
  
  def set_tenant_company
    @company = ActsAsTenant.current_tenant
  end
  
  # Calculate distance between two coordinates in meters
  def calculate_distance(lat1, lon1, lat2, lon2)
    rad_per_deg = Math::PI / 180
    earth_radius = 6371 * 1000 # in meters
    
    dlat = (lat2 - lat1) * rad_per_deg
    dlon = (lon2 - lon1) * rad_per_deg
    
    a = Math.sin(dlat / 2)**2 + 
        Math.cos(lat1 * rad_per_deg) * 
        Math.cos(lat2 * rad_per_deg) * 
        Math.sin(dlon / 2)**2
        
    c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    
    earth_radius * c
  end
  
  # Helper method for controller-specific translations
  def sessions_t(key, **options)
    t("controllers.work_sessions.#{key}", **options)
  end
end