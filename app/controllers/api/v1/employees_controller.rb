module Api
  module V1
    class EmployeesController < ApplicationController
      include ActionPolicy::Controller

      before_action :authenticate_user!
      before_action :set_tenant_company
      
      #RULE: Contracts are used to manage employees work, logs, events, etc.
      #RULE: CompanyUserRoles and Roles are used to manage employees roles in the company and permissions
      #RULE: Roles are flexible in the future and now they work well for 1 user role check. (users_controller)
      # at_work is a method in the daily_log model that checks if the start_time is not nil
      # def index
      #   employees = @company.contracts.includes([:user]).map do |contract|
      #     {
      #       id: "#{contract.id}",
      #       name: "#{contract.first_name} #{contract.last_name}",
      #       start: contract.daily_logs.last&.start_time,
      #       end: contract.daily_logs.last&.end_time,
      #       working: contract.daily_logs.at_work&.last,
      #     }
      #   end
      #   render json: employees
      # end

      # For now not including user model as not used
      def index
        # Preload only the latest daily log for each contract
        contracts_with_logs = @company.contracts.active_only
          .where.not(user_id: nil) 
          .joins("LEFT JOIN (
            SELECT DISTINCT ON (contract_id) contract_id, start_time, end_time
            FROM daily_logs
            ORDER BY contract_id, start_time DESC
          ) latest_logs ON latest_logs.contract_id = contracts.id")
          .select(
            'contracts.id',
            'contracts.first_name',
            'contracts.last_name',
            'contracts.phone',
            'latest_logs.start_time',
            'latest_logs.end_time'
          )
          .order('latest_logs.start_time DESC NULLS LAST')
        
        employees = contracts_with_logs.map do |contract|
          # Find current work activity for this contract
          current_activity = DailyActivity
            .joins(:work)
            .where(
              contract_id: contract.id,
              end_time: nil,
              activity_type: ['work_at_location', 'work_remote', 'travel_to_work']
            )
            .select('daily_activities.*, works.title as work_title, works.location as work_location')
            .first
          
          work_info = if current_activity
            {
              title: current_activity.work_title,
              location: current_activity.work_location,
              activity_type: current_activity.activity_type,
              formatted_type: format_activity_type(current_activity.activity_type)
            }
          end
          
          {
            id: contract.id.to_s,
            name: "#{contract.first_name} #{contract.last_name}",
            phone: contract.phone,
            start: contract.start_time,
            end: contract.end_time,
            working: contract.end_time.nil? && contract.start_time.present?,
            current_work: work_info
          }
        end

        puts "\n ---------- employees: #{employees.inspect} ---------- \n\n"
      
        render json: employees
      end
      
      private
      
      def format_activity_type(type)
        case type
        when 'work_at_location'
          I18n.t('works.activities.at_customer', default: 'U zákazníka')
        when 'work_remote'
          I18n.t('works.activities.work_remote', default: 'Práce na dálku')
        when 'travel_to_work'
          I18n.t('works.activities.travel_to_work', default: 'Cesta k zákazníkovi')
        else
          type
        end
      end


      private

      def set_tenant_company
        @company = ActsAsTenant.current_tenant
      end

    end
  end
end