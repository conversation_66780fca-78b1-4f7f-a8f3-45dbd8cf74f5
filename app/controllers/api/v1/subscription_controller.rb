module Api
  module V1
    class SubscriptionController < ApplicationController
      before_action :authenticate_user!
      before_action :set_tenant_company
      
      def status
        plan_name = @company.current_plan&.name || 'Free'
        
        available_features = []
        
        # Feats in plus are also available in premium
        # We use: bookings (feature), manager roles (scattered actions)
        if ['plus', 'premium'].include?(plan_name)
          available_features.push('booking', 'reservation', 'meeting')
        end
        
        # Pleaceholder, not using now
        if plan_name == 'premium'
          available_features.push('advanced_reporting', 'api_access')
        end
        
        render json: {
          success: true,
          current_plan: plan_name,
          subscription_active: @company.current_subscription.present?,
          available_features: available_features,
          expires_at: @company.current_subscription&.expire_date
        }
      end
      
      private
      
      def set_tenant_company
        @company = ActsAsTenant.current_tenant
      end
    end
  end
end