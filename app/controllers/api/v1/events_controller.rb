module Api
  module V1
    class EventsController < ApplicationController
      before_action :authenticate_user!
      before_action :set_tenant_company
      
      # FIXME: This is a security vulnerability. We should use action_policy gem to authorize the user
      # FIXME: only upcomming events should be shown
      def index
        status = params[:status] || 'pending'
        events = @company.events.includes([:contract])
        events = events.public_send(status).map do |event|
          event.as_json.merge(
            name: "#{event.contract.first_name} #{event.contract.last_name}",
          )
        end
        render json: events
      end

      def confirm
        @event = @company.events.find(params[:id])
        if @event.update(status: 'approved')
          render json: { success: true, event: @event }, status: :ok
        else
          render json: { success: false, errors: @event.errors.full_messages }, status: :unprocessable_entity
        end
      end

      private 

      def set_tenant_company
        @company = ActsAsTenant.current_tenant
      end

    end
  end
end