class CompanyConnectionsController < ApplicationController
  
  before_action :authenticate_user!

  def index
  end
  
  # Originaly contract is scope under the company tenant
  def fetch
    ActsAsTenant.without_tenant do
      @pending_contracts = Contract.where(email: current_user.email, user_id: nil)
    end
    render json: @pending_contracts.as_json(
        include: { company: { only: [:id, :name] } }
      )
  end

  def accept
    ActsAsTenant.without_tenant do
      @contract = Contract.find(params[:id])

      company_user_role = CompanyUserRole.find_or_initialize_by(
        user: current_user,
        company: @contract.company,
        active: true
      )
      company_user_role.role = Role.find_by(name: 'employee')
      company_user_role.is_primary = true unless current_user.company_user_roles.exists?
      
      if company_user_role.save
        @contract.update(user: current_user)
        set_tenant_id_for_resource(current_user)  
        set_current_tenant
        redirect_to root_path, notice: '<PERSON>z<PERSON><PERSON> akcept<PERSON>no.'
      else
        redirect_to company_connections_path, 
                    alert: '<PERSON><PERSON><PERSON> akceptovat pozvání.'
      end
    end
  end

  # def decline
  #   @contract = Contract.find(params[:id])
  #   @contract.destroy
  #   redirect_to pending_contracts_path, notice: '<PERSON>z<PERSON><PERSON> odmítnuto.'
  # end

end