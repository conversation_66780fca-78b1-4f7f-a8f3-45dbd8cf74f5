class CompanyUserRole < ApplicationRecord
  belongs_to :company
  belongs_to :user
  belongs_to :role
  
  validates :is_primary, uniqueness: { scope: :user_id }, if: :is_primary
  validates :company_id, uniqueness: { 
    scope: [:user_id, :role_id, :active], 
    message: "User can have only one active role of each type in a company" 
  }

  # Only show active roles by default
  default_scope { where(active: true) }

  # Check this version as the scope have been changed when creating tests
  # Define scopes to access all roles when needed
  scope :with_inactive, -> { unscope(where: :active) }
  scope :active, -> { where(active: true) }
  scope :inactive, -> { where(active: false) }

  # TODO: rework to enums
  # For this model is a con of enums: 
  # More rigid if roles need to change frequently or if you need to add complex logic related to roles (e.g., a Role can have many permissions).
  # enum role: { admin: 0, manager: 1, employee: 2 }  # Example roles
end
