class Booking < ApplicationRecord
  acts_as_tenant(:company)
  
  belongs_to :booking_link
  belongs_to :company
  has_one :work, dependent: :nullify

  validates :client_name, presence: true
  validate :email_or_phone_present
  validates :client_email, format: { with: URI::MailTo::EMAIL_REGEXP }, allow_blank: true
  validates :preferred_date, presence: true
  validates :preferred_period, presence: true, inclusion: { in: %w[morning afternoon] }
  validates :status, presence: true, inclusion: { in: %w[pending confirmed completed cancelled rescheduled] }
  validate :client_email_unchanged, on: :update
  validate :prevent_cancelled_booking_changes, on: :update
  validate :only_date_time_changes_for_public, on: :update
  validate :check_booking_limits, on: :create
  validate :location_required_if_booking_link_requires_it


  attr_accessor :public_update
  
  before_create :generate_access_token
  after_save :sync_to_work, if: :should_sync_to_work?
  after_save :sync_status_to_work, if: :saved_change_to_status?

  
  scope :pending, -> { where(status: 'pending') }
  scope :confirmed, -> { where(status: 'confirmed') }
  scope :completed, -> { where(status: 'completed') }
  scope :cancelled, -> { where(status: 'cancelled') }
  scope :upcoming, -> { where('preferred_date >= ?', Date.current) }


  def generate_access_token
    self.access_token = SecureRandom.urlsafe_base64(24)
    self.token_generated_at = Time.current
  end
  
  def valid_token?
    token_generated_at > 72.hours.ago
  end
  
  def regenerate_access_token
    update(
      access_token: SecureRandom.urlsafe_base64(24),
      token_generated_at: Time.current
    )
  end
  
  def confirm(date = nil, time = nil)
    # First we are confirming date:
    # If the date is confirmed without change, it stays opened as preferred_date - DATE
    # If the date is changed to some other: preferred_date will change - DATE
    # Specific_time is a preliminary time but can be changed when confirming- TIME
    # Confirmed_time is the final time when the booking is confirmed - TIME

    self.status = 'confirmed'
    self.preferred_date = date if date.present?
    # self.specific_time = time if time.present?
    # self.confirmed_time = time || specific_time || nil
    self.confirmed_time = time ||nil
    
    save_result = save

    convert_to_work if save_result

    save_result
  end
  
  def complete
    self.status = 'completed'
    save
  end
  
  def cancel
    self.status = 'cancelled'
    save_result = save
    
    if save_result
      work = company.works.find_by(booking_id: self.id)

      if work.present? && work.status == "scheduled"
        work_updated = work.update(status: "cancelled")
        work.reload if work_updated 
      end
    end
    
    save_result
  end
  
  def convert_to_work
    return nil unless self.reload.status == 'confirmed'

    create_work(
      company: company,
      title: booking_link.name,
      description: "Rezervace: #{client_name}.\n#{client_email}\n#{client_phone}\n#{message}",
      location: location,
      status: "scheduled",
      work_type: "Rezervace",
      scheduled_start_date: preferred_date,
      preferred_period: preferred_period,
      specific_time: specific_time,
      confirmed_time: confirmed_time,
      duration: duration
    )
    puts "\n --------- preferred date: #{preferred_date}"
    puts "\n --------- scheduled start date: #{work.scheduled_start_date}"


  end

  def self.model_t(key, **options)
    I18n.t("models.booking.#{key}", **options)
  end

  private

  def check_booking_limits
    return unless booking_link && preferred_date && preferred_period
    
    if booking_link.exceeds_period_limit?(preferred_date, preferred_period)
      errors.add(:base, self.class.model_t('errors.period_fully_booked'))
    end
    
    if booking_link.exceeds_daily_limit?(preferred_date)
      errors.add(:base, self.class.model_t('errors.day_fully_booked'))
    end
  end
  
  # Only sync when the booking is confirmed and has an associated work
  def should_sync_to_work?
    status == 'confirmed' && work.present? &&
    (saved_change_to_preferred_date? || saved_change_to_preferred_period? || 
     saved_change_to_specific_time? || saved_change_to_confirmed_time? || 
     saved_change_to_duration? || saved_change_to_location?)
  end
  
  def sync_to_work

    work.update(
      scheduled_start_date: preferred_date,
      scheduled_end_date: preferred_date,
      preferred_period: preferred_period,
      specific_time: specific_time
    )
  end

  def sync_status_to_work
    return unless work.present?
    
    case status
    when 'confirmed'
      work.update(status: 'scheduled') if work.status != 'scheduled'
    when 'cancelled'
      work.update(status: 'cancelled')
    when 'rescheduled'
      if status_previously_changed? && status_previous_change[0] == 'confirmed'
        work.update(status: 'rescheduled')
      end
    when 'pending'
      if status_previously_changed? && status_previous_change[0] == 'confirmed'
        work.update(status: 'rescheduled')
      end
    end
  end

  def email_or_phone_present
    if client_email.blank? && client_phone.blank?
      errors.add(:base, self.class.model_t('errors.email_or_phone_required'))
    end
  end

  def client_email_unchanged
    if client_email_changed? && self.persisted?
      errors.add(:client_email, self.class.model_t('errors.email_cannot_be_changed'))
    end
  end

  def prevent_cancelled_booking_changes
    if status_was == 'cancelled'
      errors.add(:base, self.class.model_t('errors.cancelled_booking_cannot_be_edited'))
    end
  end

  def only_date_time_changes_for_public
    return unless public_update
    
    allowed_changes = ['preferred_date', 'preferred_period', 'specific_time', 'status']

    changed_attrs = self.changed - allowed_changes
    
    if changed_attrs.any?
      errors.add(:base, self.class.model_t('errors.only_date_time_editable'))
    end
  end

  def location_required_if_booking_link_requires_it
    if booking_link&.location_required && location.blank?
      errors.add(:location, self.class.model_t('errors.location_required'))
    end
  end

end