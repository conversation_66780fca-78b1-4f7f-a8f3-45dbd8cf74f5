class User < ApplicationRecord
  
  attr_accessor :skip_workspace_setup

  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :invitable, :database_authenticatable, :registerable, :recoverable, :rememberable, :validatable, :trackable, :confirmable
  #:omniauthable, omniauth_providers: [:google_oauth2]

  has_one :user_profile, dependent: :destroy
  has_one :user_setting, dependent: :destroy
  has_many :company_user_roles
  has_many :companies, through: :company_user_roles
  has_many :roles, through: :company_user_roles
  has_many :contracts, dependent: :nullify
  has_many :daily_logs
  has_many :daily_activities
  has_many :breaks
  has_many :events
  has_many :work_sessions, dependent: :nullify
  has_many :meetings, foreign_key: 'created_by_id'
  has_many :meeting_users, dependent: :destroy
  has_many :notifications, dependent: :destroy

  validate :email_unchanged, on: :update
  # validates :tos_accepted_at, presence: true
  # validates :gdpr_accepted_at, presence: true
  
  # after_create :setup_personal_workspace
  # after_create :create_user_profile
  # after_create :create_default_settings
  after_create :setup_initial_user_state
  before_save :terms_accepted

  before_destroy :handle_company_disconnection

  # After user accepts invitation to join the company: 
  # Create his profile and default settings
  # But do not create his personal workspace
  #TODO: Instead make it available to user to have 2 workspaces without premium
  after_invitation_accepted do
    # setup_personal_workspace
    create_user_profile
    create_default_settings
  end

  def primary_company
    company_user_roles.find_by(is_primary: true)&.company
  end
  
  def set_primary_company(company)
    ActiveRecord::Base.transaction do
      # Clear previous primary
      company_user_roles.update_all(is_primary: false)
      
      # Set new primary
      role = company_user_roles.find_by(company: company)
      role.update!(is_primary: true) if role
    end
  end

  def role_in(company)
    return nil unless company
    @company_roles ||= {}
    @company_roles[company.id] ||= company_user_roles.find_by(company: company)&.role
    # company_user_roles.find_by(company: company)&.role
  end

  def has_role?(role_name, company)
    company_user_roles.joins(:role).exists?(company: company, roles: { name: role_name })
  end

  # scope :with_role_in_company, ->(role_name, company) {
  #   joins(:company_user_roles)
  #     .joins('INNER JOIN roles ON roles.id = company_user_roles.role_id')
  #     .where(company_user_roles: { company_id: company.id }, roles: { name: role_name })
  # }

  # scope :with_roles_in_company, ->(company_id) {
  #   joins(:company_user_roles, :roles)
  #     .where(company_user_roles: { company_id: company_id })
  #     .select('users.*, array_agg(roles.name) AS have_roles')
  #     .group('users.id')
  # }

  # When User leaving company - terminate all contracts and deactivate user roles
  # Inactive CompanyUserRole - User can be Invited again to the Company later
  def leave_company(company)
    ActiveRecord::Base.transaction do
      # Find and terminate all contracts
      contracts = Contract.where(user_id: self.id, company_id: company.id)
      contracts.each do |contract|
        contract.terminate!
      end
      
      # Handle company user roles
      company_user_roles.where(company: company).update_all(active: false)
      
      # Remove primary company flag if needed
      if primary_company&.id == company.id
        # Find another active company to set as primary
        another_role = company_user_roles.where.not(company_id: company.id)
                                        .where(active: true)
                                        .first
        if another_role
          another_role.update(is_primary: true)
        end
      end
      
      true
    rescue => e
      Rails.logger.error("Error leaving company: #{e.message}")
      false
    end
  end


  # Override
  # This ensures that the reset password instructions are explicitly sent using Devise::Mailer.
  # def send_reset_password_instructions(attributes = {})
  #   recoverable = find_or_initialize_with_errors(reset_password_keys, attributes, :not_found)
  #   if recoverable.persisted?
  #     # Explicitly call Devise::Mailer
  #     Devise::Mailer.reset_password_instructions(recoverable, recoverable.reset_password_token).deliver_now
  #   end
  #   recoverable
  # end

  def create_default_settings
    build_user_setting.save
  end

  def create_user_profile
    self.build_user_profile.save
  end

  def setup_personal_workspace
    ActiveRecord::Base.transaction do
      create_personal_workspace
    end
  end


  private

  def terms_accepted
    self.tos_accepted_at = true
    self.gdpr_accepted_at = true
  end

  def setup_initial_user_state
    return if skip_workspace_setup
    
    public_send(:setup_personal_workspace)
    public_send(:create_user_profile)
    public_send(:create_default_settings)
  end

  def create_personal_workspace
    name = generate_workspace_name
    company = Company.create!(
      name: name.titleize,
      slug: name.parameterize,
      subdomain: name.parameterize,
      is_personal: true
    )

    owner_role = Role.find_or_create_by!(name: 'owner')
    company_user_roles.create!(
      company: company,
      role: owner_role,
      is_primary: true
    )

    company.contracts.create!(
      user: self,
      first_name: user_profile&.first_name || email,
      last_name: user_profile&.last_name || '',
      email: email
    )

    company
  end

  def email_unchanged
    if email_changed? && self.persisted?
      errors.add(:email, "není možné změnit")
    end
  end

  def handle_company_disconnection
    #Remove the association between the User and CompanyUserRoles: Set the user_id to nil to keep
    # the record but disassociate it from the user.
    #company_user_roles.update_all(user_id: nil)
    
    # company_user_roles.each do |role|
    #   contract = Contract.find_by(user_id: self.id, company_id: role.company_id)
    #   contract.destroy if contract
    # end
  end

  def generate_workspace_name
    compound_adjectives = %w[
      skvele uzasne nezastavitelne vyjimecne nezapomenutelne 
      prekvapive neporazitelne fantasticky neprekonatelne 
      neuveritelne jedinecne uspesne nadejne
    ]
    
    qualities = %w[
      chytra rychla moudra pevna silna uspesna odvazna 
      aktivni ambiciozni tvoriva bystra zdatna pracovita 
      schopna nadana ustretova spolehliva inovativni odhodlana 
      vytrvala organizovana dusledna precizni zodpovedna pratelska 
      otevrena progresivni kompetentni efektivni duveryhodna 
      kreativni
    ]
    
    hash = SecureRandom.uuid.first(6).to_i(16).to_s(36) 
  
    "#{compound_adjectives.sample}-#{qualities.sample}-firma-#{hash}"
  end

  
end
