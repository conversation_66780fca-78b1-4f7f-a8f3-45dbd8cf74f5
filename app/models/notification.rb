class Notification < ApplicationRecord
  belongs_to :user
  belongs_to :company
  belongs_to :notifiable, polymorphic: true
  
  # Notification types
  NOTIFICATION_TYPES = %w[
    work_assignment_added
    work_assignment_removed
    work_status_changed
    work_check_in_reminder
    booking_received
    invitation_received
    event_pending
  ].freeze
  
  # Validations
  validates :notification_type, presence: true, inclusion: { in: NOTIFICATION_TYPES }
  validates :title, presence: true
  
  # Scopes
  scope :unread, -> { where(read_at: nil) }
  scope :read, -> { where.not(read_at: nil) }
  scope :unprocessed, -> { where(processed_at: nil) }
  scope :processed, -> { where.not(processed_at: nil) }
  scope :pending, -> { unread.unprocessed }
  scope :recent, -> { order(created_at: :desc) }
  scope :for_mainbox, -> { pending.recent }
  
  # Auto-cleanup old notifications (30 days)
  scope :old, -> { where('created_at < ?', 30.days.ago) }
  
  # Instance methods
  def read?
    read_at.present?
  end
  
  def unread?
    read_at.nil?
  end
  
  def processed?
    processed_at.present?
  end
  
  def pending?
    unread? && !processed?
  end
  
  def mark_as_read!
    update!(read_at: Time.current) if unread?
  end
  
  def mark_as_processed!
    update!(processed_at: Time.current) if processed_at.nil?
  end
  
  # Get the associated work for work-related notifications
  def work
    case notifiable_type
    when 'WorkAssignment'
      notifiable.work
    when 'Work'
      notifiable
    end
  end
  
  # Serialize notification for API
  def as_json(options = {})
    super(options.merge(
      methods: [:read?, :pending?],
      include: {
        notifiable: {
          only: [:id],
          methods: [:to_s]
        }
      }
    ))
  end
end