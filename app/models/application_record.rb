class ApplicationRecord < ActiveRecord::Base
  primary_abstract_class

  # Helper method for model-specific translations
  # Makes translation calls cleaner and more maintainable
  # Usage: User.model_t('errors.invalid_email_format')
  def self.model_t(key, **options)
    I18n.t("models.#{name.underscore}.#{key}", **options)
  end

  # Instance method version for convenience
  def model_t(key, **options)
    self.class.model_t(key, **options)
  end
end
