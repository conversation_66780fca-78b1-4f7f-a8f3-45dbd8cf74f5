class DailyActivity < ApplicationRecord
  belongs_to :user
  belongs_to :company
  belongs_to :contract, optional: true
  belongs_to :daily_log, optional: true
  belongs_to :work, optional: true
  belongs_to :work_assignment, optional: true
  
  has_one :work_session, dependent: :nullify

  before_save :set_duration
  
  # Activity types
  ACTIVITY_TYPES = %w[travel_to_work work_at_location work_remote break regular].freeze
  
  validates :activity_type, inclusion: { in: ACTIVITY_TYPES }, allow_nil: true

  def as_json(options={})
    super(options.merge(methods: [:duration_in_text]))
  end

  def duration_in_text
    return nil unless start_time && end_time

    total_seconds = (end_time - start_time).to_i
    return "#{total_seconds} s" if total_seconds < 60

    total_minutes = total_seconds / 60
    hours = total_minutes / 60
    minutes = total_minutes % 60

    if hours > 0
      "#{hours} hod. #{minutes} min."
    else
      "#{minutes} min."
    end
  end

  private

  def set_duration
    if self.end_time.present?
      self.duration = (self.end_time - self.start_time).to_i
    end
  end
  
end