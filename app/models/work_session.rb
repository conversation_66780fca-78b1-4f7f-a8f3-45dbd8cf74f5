class WorkSession < ApplicationRecord
  acts_as_tenant(:company)
  
  belongs_to :work
  belongs_to :user
  belongs_to :company
  belongs_to :daily_activity, optional: true
  belongs_to :work_assignment, optional: true
  
  validates :start_time, presence: true
  validates :status, presence: true
  
  # Scopes for filtering
  scope :checked_in, -> { where.not(check_in_time: nil) }
  scope :active, -> { where(status: 'in_progress') }
  
  before_save :calculate_duration
  
  enum status: { in_progress: 0, completed: 1, cancelled: 2 }
  
  private
  
  def calculate_duration
    if start_time.present? && end_time.present?
      self.duration = (end_time - start_time).to_i
    end
  end
end