class Meeting < ApplicationRecord
  acts_as_tenant(:company)
  
  belongs_to :company
  belongs_to :created_by, class_name: 'User', foreign_key: 'created_by_id'
  has_many :meeting_users, dependent: :destroy
  
  validates :title, presence: true
  validates :company, presence: true
  validates :day_options, presence: true

  enum status: { pending: 0, confirmed: 1, cancelled: 2 }
  
  def all_responses_submitted?
    # meeting_users.all? { |mu| mu.selected_dates.present? }
    meeting_users.all? { |mu| mu.status == 'replied' }
  end
  

  def find_best_common_time
    return nil if meeting_users.empty?
    
    # Get all selected dates from all users
    all_selected_dates = meeting_users.map(&:selected_dates).reject(&:nil?)
    
    # Return nil if any user hasn't submitted dates
    return nil if all_selected_dates.length < meeting_users.length
    
    # Return nil if any user submitted empty dates
    return nil if all_selected_dates.any?(&:empty?)
    
    # Find the intersection of all selected dates
    common_times = all_selected_dates.reduce(nil) do |intersection, user_dates|
      user_date_keys = user_dates.keys
      if intersection.nil?
        user_date_keys
      else
        intersection & user_date_keys
      end
    end

    # If there is a common time, set all meeting_users to confirmed
    # and set the meeting itself as confirmed
    if common_times.present?
      meeting_users.update_all(status: 'confirmed')
      self.update(status: 'confirmed')
    end
    
    # Return the first common slot (earliest date), if any
    common_times.sort.first if common_times.present?
  end

  def find_best_options_with_attendance
    return nil if meeting_users.empty?
    
    # Get all selected dates from all users
    all_user_selections = meeting_users.map do |mu|
      { user_id: mu.id, email: mu.email, dates: mu.selected_dates || {} }
    end
    
    # Create a hash of dates with their attendance count and who can attend
    date_availability = {}
    all_user_selections.each do |user|
      user[:dates].keys.each do |date_key|
        date_availability[date_key] ||= { count: 0, users: [] }
        date_availability[date_key][:count] += 1
        date_availability[date_key][:users] << { id: user[:user_id], email: user[:email] }
      end
    end
    
    # Sort dates by attendance count (highest first)
    best_options = date_availability.sort_by { |_date, data| -data[:count] }
    
    # For each option, identify who can and can't make it
    best_options.first(3).map do |date, data|
      available_users = data[:users]
      missing_users = meeting_users.reject { |mu| available_users.any? { |u| u[:id] == mu.id } }
      {
        date: date,
        attendance_count: data[:count],
        total_users: meeting_users.count,
        available_users: available_users,
        missing_users: missing_users.map { |mu| { id: mu.id, email: mu.email } }
      }
    end
  end

  def generate_extended_date_options(days_forward = 7, days_backward = 0)
    current_options = day_options || {}
    dates = current_options.keys.map { |date_str| Date.parse(date_str) }
    
    # Find the earliest and latest dates in current options
    return {} if dates.empty?
    
    earliest_date = dates.min
    latest_date = dates.max
    
    # Generate new date range
    new_start_date = earliest_date - days_backward.days
    new_end_date = latest_date + days_forward.days
    
    # Generate new day options
    new_options = {}
    (new_start_date..new_end_date).each do |date|
      date_str = date.to_s
      new_options[date_str] = current_options[date_str] if current_options[date_str]
      new_options[date_str] ||= []
    end
    
    new_options
  end

end