class Invitation < ApplicationRecord
  attr_readonly :email

  belongs_to :company
  belongs_to :sender, class_name: 'User'
  belongs_to :recipient, class_name: 'User', optional: true
  belongs_to :contract, optional: true, counter_cache: true
  
  # TODO: Add a Counter Column: Generate a migration to add a column to the parent model 
  #Counter cache appropriate for the contract
  #belongs_to :contract, counter_cache: true
  #add_column :posts, :comments_count, :integer, default: 0, null: false
  
  #TODO: Pridat aj sem acts_as_tenant?

  # Validate if the email of the recipient has invited from the company
  validates :email, 
  presence: true, 
  format: { with: URI::MailTo::EMAIL_REGEXP },
  uniqueness: { 
    scope: [:company_id], 
    message: -> { model_t('errors.user_already_invited') } 
  }
  validates :unique_token, presence: true, uniqueness: true

  scope :pending, -> { where(status: 'pending') }
  scope :accepted, -> { where(status: 'accepted') }
  #scope :expired, -> { where('expires_at < ?', Time.current) }

  before_validation :generate_unique_token, on: :create

  def original_status
    read_attribute(:status)
  end

  def expired?
    expires_at < Time.current
  end

  def pending?
    status == 'pending'
  end

  def translated_status
    I18n.t("statuses.#{status}")
  end

  # TODO: Legacy method, consider remove
  def resend
    if pending?
      InvitationMailer.invite_email(self).deliver_now
    else
      errors.add(:base, self.class.model_t('errors.cannot_resend_invitation'))
      false
    end
  end

  def self.model_t(key, **options)
    I18n.t("models.invitation.#{key}", **options)
  end

  private

  def generate_unique_token
    self.unique_token ||= loop do
      random_token = SecureRandom.urlsafe_base64(16)
      break random_token unless self.class.exists?(unique_token: random_token)
    end

    self.expires_at ||= 7.days.from_now
  end

  # In case of the background job
  # def self.expire_old_invitations!
  #   where('expires_at < ?', Time.current)
  #     .where(status: 'pending')
  #     .update_all(status: 'expired')
  # end

end