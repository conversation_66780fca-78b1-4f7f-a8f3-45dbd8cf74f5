class MeetingUser < ApplicationRecord
  belongs_to :meeting
  belongs_to :contract, optional: true
  belongs_to :user, optional: true
  
  enum status: { pending: 0, replied: 1, confirmed: 2, cancelled: 3 }

  # validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :email, format: { with: URI::MailTo::EMAIL_REGEXP }, if: -> { email.present? }

  validates :token, presence: true, uniqueness: true
  before_validation :set_email_from_contract, on: :create
  
  before_validation :generate_token, on: :create
  
  def name
    contract&.first_name.present? ? "#{contract.first_name} #{contract.last_name}" : email
  end
  
  private
  
  def set_email_from_contract
    return if email.present? # Skip if email is already set
    
    if contract.present?
      # Try to get email from contract first
      if contract.email.present?
        self.email = contract.email
      # If contract email is missing, try to get email from contract's user
      elsif contract.user&.email.present?
        self.email = contract.user.email
      end
    end
    
    # If we still don't have an email, raise an error
    if email.blank?
      errors.add(:email, "must be present. Neither contract nor contract's user has an email.")
    end
  end

  def generate_token
    self.token ||= SecureRandom.urlsafe_base64(32)
  end
end