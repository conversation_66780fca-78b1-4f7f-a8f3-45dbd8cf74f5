class DailyLog < ApplicationRecord
  #include ActiveModel::Serializers::<PERSON><PERSON><PERSON>
  
  acts_as_tenant(:company)

  attr_accessor :is_report_creation, :time_warning

  has_many :daily_activities
  has_many :breaks, dependent: :destroy

  belongs_to :contract
  belongs_to :company
  belongs_to :user, optional: true

  validates :company, presence: true
  validates :contract, presence: true

  validate :validate_time_entries, on: :update
  validate :validate_time_entries, if: :should_validate_time?
  validate :no_event_overlap, on: :create


  before_save :set_day
  before_save :set_duration

  scope :is_working, -> { where('end_time IS NULL') }
  scope :is_finished, -> { where('end_time IS NOT NULL') }

  # def attributes
  #   super.merge('duration_in_text' => nil)
  # end

  def as_json(options={})
    super(options.merge(methods: [:duration_in_text]))
  end

  def reopen
    update(end_time: nil)
  end

  def close 
    update(end_time: Time.current)
  end

  # def approve!
  #   self.update_column(:status, "finished")
  # end

  def self.at_work
    where.not(start_time: nil).is_working
  end

  def duration_in_text
    return nil unless start_time && end_time

    total_seconds = (end_time - start_time).to_i
    return "#{total_seconds} s" if total_seconds < 60

    total_minutes = total_seconds / 60
    hours = total_minutes / 60
    minutes = total_minutes % 60

    if hours > 0
      "#{hours} hod. #{minutes} min."
    else
      "#{minutes} min."
    end
  end


  private

  # After Timezone refactor
  def no_event_overlap
    return unless start_time

    log_date = start_time.to_date
    day_range = DateTimeHelper.day_range(log_date)
    
    puts "\n CONTRACT ID: #{contract_id.inspect} \n\n "
    puts "\n LOG DATE: #{log_date.inspect} \n\n "
    puts "\n DAY RANGE: #{day_range.inspect} \n\n "

    event = Event.where(
      contract_id: contract_id,
      user_id: user_id
      ).where('start_time <= ? AND end_time >= ?', day_range.end, day_range.begin).first
    
    puts "\n EVENT: #{event.inspect} \n\n "

    if event.present?
      errors.add(:base, "Nelze vytvořit záznam práce pro den, kdy již existuje událost")
    end
  end

  def should_validate_time?
    # Validate on update or when creating from report
    persisted? || is_report_creation
  end

  def validate_time_entries
    return unless start_time && end_time

    # Check for one log per day
    # After timezone refactor
    day_range = DateTimeHelper.day_range(start_time.to_date)
    same_day_log = DailyLog.where(user_id: user_id, contract_id: contract_id)
                          .where(start_time: day_range)
                          .where.not(id: id)

    if same_day_log.exists?
      errors.add(:base, 'Denní záznam pro tento den již existuje.')
      return
    end

    # Check remaining time rules
    validate_time_rules
  end

  def validate_time_rules
    now = Time.current

    # No future dates
    puts "\n START TIME: #{start_time.inspect} \n\n "
    puts "\n END TIME: #{end_time.inspect} \n\n "
    puts "\n NOW: #{now.inspect} \n\n "

    if start_time > now
      errors.add(:start_time, 'Začátek záznamu nemůže být v budoucnosti.')
      #self.time_warning = {type: 'info', message: "Začátek záznamu je #{start_time.strftime("%H:%M")}." }
    end
    
    if end_time > now
      #errors.add(:end_time, 'Konec záznamu nemůže být v budoucnosti.')
      self.time_warning = {type: 'info', message: "Konec záznamu je #{end_time.strftime("%H:%M")}." }
    end

    # end must be after start
    if end_time < start_time
      errors.add(:end_time, 'Konec záznamu nemůže být před začátkem.')
    end

    # No overlapping periods
    overlapping = DailyLog.where(
      user_id: user_id, 
      contract_id: contract_id
    ).where.not(id: id)
     .where('(start_time, end_time) OVERLAPS (?, ?)', start_time, end_time)

    if overlapping.exists?
      errors.add(:base, 'Časové období se překrývá s existujícím záznamem.')
    end
  end

  def set_duration
    if self.end_time.present?
      self.duration = (self.end_time - self.start_time).to_i
    end
  end

  def set_day
    self.workday = self.start_time.wday
  end

end
