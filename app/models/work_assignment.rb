class WorkAssignment < ApplicationRecord
  acts_as_tenant(:company)

  belongs_to :work
  belongs_to :contract
  belongs_to :company
  
  has_many :daily_activities
  has_many :work_sessions
  
  validates :work, presence: true
  validates :contract, presence: true
  validates :company, presence: true
  validates :role, presence: true
  
  validates :contract_id, uniqueness: { scope: :work_id }
  
  before_destroy :check_for_related_activities
  
  private
  
  def check_for_related_activities
    if daily_activities.exists?
      errors.add(:base, I18n.t('activerecord.errors.models.work_assignment.has_daily_activities'))
      throw(:abort)
    elsif work_sessions.exists?
      errors.add(:base, I18n.t('activerecord.errors.models.work_assignment.has_work_sessions'))
      throw(:abort)
    end
  end
end