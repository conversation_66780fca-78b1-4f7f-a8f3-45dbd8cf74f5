class UserSetting < ApplicationRecord
  belongs_to :user
  
  validates :start_time, presence: true
  validates :end_time, presence: true
  validates :break_start, presence: true
  validate :valid_time_sequence

  after_initialize :set_defaults, if: :new_record?

  private

  def set_defaults
    self.start_time ||= "08:00"
    self.end_time ||= "16:00"
    self.break_start ||= "12:00"
  end

  def valid_time_sequence
    return unless start_time && end_time && break_start

    if end_time <= start_time
      errors.add(:end_time, "must be after start time")
    end

    unless break_start.between?(start_time, end_time)
      errors.add(:break_start, "must be between start and end time")
    end
  end
end