class Break < ApplicationRecord
  #acts_as_tenant(:company)
  
  belongs_to :daily_log
  belongs_to :user
  belongs_to :contract

  validates :daily_log, presence: true
  validates :user, presence: true
  validates :start_time, presence: true
  validate :one_break_per_day
  validate :break_within_daily_log

  before_validation :set_associations, on: :create
  before_save :set_duration

  def as_json(options={})
    super(options.merge(methods: [:duration_in_text]))
  end

  def duration_in_text
    return nil unless duration

    total_minutes = duration / 60
    hours = total_minutes / 60
    minutes = total_minutes % 60

    if hours > 0
      "#{hours} hod. #{minutes} min."
    else
      "#{minutes} min."
    end
  end

  private

  def set_associations
    if daily_log
      self.user = daily_log.user
      self.contract = daily_log.contract
    end
  end

  def set_duration
    if end_time.present?
      self.duration = end_time - start_time
      #self.duration = (total_seconds / 60).to_i
    end
  end
  
  # After timezone refactor
  def one_break_per_day
    return unless user && contract
  
    day_range = DateTimeHelper.day_range(start_time.to_date)

    existing_break = Break.where(user: user, contract: contract)
                       .where(start_time: day_range)
    existing_break = existing_break.where.not(id: id) if persisted?
  
    if existing_break.exists?
      errors.add(:base, 'Pro dnešní den již existuje přestávka')
    end
  end

  def break_within_daily_log
    return unless daily_log && start_time
    
    if start_time < daily_log.start_time
      errors.add(:start_time, 'musí být po začátku denního záznamu')
    end

    if end_time.present?
      if start_time > end_time
        errors.add(:end_time, 'musí být po začátku přestávky')
      end

      if daily_log.end_time && end_time > daily_log.end_time
        errors.add(:end_time, 'musí být před koncem denního záznamu')
      end
    end
  end
end