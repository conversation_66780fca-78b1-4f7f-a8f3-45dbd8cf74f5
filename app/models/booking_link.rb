class BookingLink < ApplicationRecord
  include HolidayFetcher
  
  acts_as_tenant(:company)
  
  belongs_to :company
  has_many :bookings, dependent: :destroy
  
  validates :name, presence: true
  validates :slug, presence: true, uniqueness: { scope: :company_id }
  
  before_validation :generate_slug, on: :create
  
  def to_param
    slug
  end

  def exceeds_period_limit?(date, period)
    return false unless morning_limit.present? || afternoon_limit.present?
    
    period_limit = period == 'morning' ? morning_limit : afternoon_limit
    return false if period_limit.nil? || period_limit <= 0
    
    count = bookings.where(preferred_date: date, preferred_period: period)
                    .where.not(status: 'cancelled')
                    .count
                    
    if include_works_in_count
      works_count = company.works
                    .where(scheduled_start_date: date)
                    .where.not(status: ['cancelled', 'completed'])
                    .where(booking_id: nil) 
                    .where(preferred_period: period)
                    .count
      count += works_count
    end
    
    count >= period_limit
  end

  # TODO: check if no need to use daily limit
  def exceeds_daily_limit?(date)
    return false unless daily_limit.present? && daily_limit > 0
    
    count = bookings.where(preferred_date: date)
                    .where.not(status: 'cancelled')
                    .count
                    
    if include_works_in_count
      works_count = company.works
                    .where(scheduled_start_date: date)
                    .where.not(status: ['cancelled', 'completed'])
                    .where(booking_id: nil) 
                    .count
      count += works_count
    end
    
    count >= daily_limit
  end
  
  def available_for_booking?(date, period)
    # Check if it's a holiday and booking holidays is disabled
    if is_holiday?(date) && !book_holidays
      return false
    end
    
    !exceeds_period_limit?(date, period) && !exceeds_daily_limit?(date)
  end

  def self.model_t(key, **options)
    I18n.t("models.booking_link.#{key}", **options)
  end
  
  private
  
  def is_holiday?(date)
    # Use the included HolidayFetcher concern to check holidays
    year = date.year
    month = date.month
    
    # Get holidays for the company's country context
    # The HolidayFetcher will handle user country code appropriately
    holidays = fetch_holidays(year, month)
    
    holidays.include?(date.to_s)
  end
  
  def generate_slug
    return if slug.present?
    
    base_slug = name.parameterize
    self.slug = base_slug
    
    counter = 1
    while BookingLink.where(company_id: company_id, slug: slug).exists?
      self.slug = "#{base_slug}-#{counter}"
      counter += 1
    end
  end
end