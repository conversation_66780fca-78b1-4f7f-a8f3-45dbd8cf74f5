class Event < ApplicationRecord
  acts_as_tenant(:company)

  belongs_to :company
  belongs_to :contract
  belongs_to :user, optional: true

  enum event_type: { illness: 0, day_care: 1, family_sick: 2, other: 3, vacation: 4, travel: 5 }
  # <PERSON><PERSON><PERSON><PERSON><PERSON> (nemocenská), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (OČR), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> absence
  # Principal: <PERSON><PERSON> absence: This is important company policy: we minimize the personal data we collect. Always look
  # for the way how to minimize sensible information and lok for overrides. It is a hard thing to do, but
  # important one. 

  validates :company, presence: true
  validates :contract, presence: true
  validates :event_type, presence: true, inclusion: { in: event_types.keys }
  validate :no_daily_log_overlap, on: :create

  before_create :set_pending_if_vacation
  before_save :set_day
  
  scope :pending, -> { where(status: 'pending') }
  scope :approved, -> { where(status: 'approved') }
  scope :rejected, -> { where(status: 'rejected') }

  def approve!
    self.update_column(:status, "approved")
  end

  def cancel!
    self.update_column(:status, "cancelled")
  end

  def set_unapproved
    self.status = 'pending'
  end

  def translated_event_type
    I18n.t("event_types.#{event_type}")
  end

  def translated_status
    I18n.t("statuses.#{status}")
  end
  
  def as_json(options = {})
    super(options).merge(
      t_status: translated_status,
      t_event_type: translated_event_type,
      start_time: formatted_time(start_time),
      end_time: formatted_time(end_time)
    )
  end
  
  def self.model_t(key, **options)
    I18n.t("models.event.#{key}", **options)
  end

  private
  
  def no_daily_log_overlap
    # For single day events
    
    if start_time.to_date == end_time.to_date
      day_range = DateTimeHelper.day_range(start_time.to_date)
      log = DailyLog.where(
        contract_id: contract_id,
        user_id: user_id
        ).where(start_time: day_range).first
      
      if log.present?
        errors.add(:base, self.class.model_t('errors.cannot_create_with_existing_log'))
        return
      end
    else
      # For multi-day events, check each day in the range
      (start_time.to_date..end_time.to_date).each do |date|
        day_range = DateTimeHelper.day_range(date)
        log = DailyLog.where(
          contract_id: contract_id,
          user_id: user_id
          ).where(start_time: day_range).first
        
        if log.present?
          errors.add(:base, self.class.model_t('errors.cannot_create_overlapping_logs'))
          break
        end
      end
    end
  end
  
  def set_day
    self.workday = self.start_time.wday
  end

  #TODO: Set pending only if company will have settings for holidays turned on 
  def set_pending_if_vacation
    self.status = 'pending' if self.event_type == "vacation"
  end

  def formatted_time(time)
    time.present? ? time.strftime("%Y-%m-%d %H:%M:%S") : nil
  end

end
