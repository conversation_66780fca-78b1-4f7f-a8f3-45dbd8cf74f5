class Holiday < ApplicationRecord
  validates :date, presence: true
  validates :country, presence: true
  validates :month_year, presence: true

  scope :for_month_year, ->(month_year) { where(month_year: month_year) }
  scope :for_country, ->(country) { where(country: country) }
  scope :for_date_range, ->(start_date, end_date) { where(date: start_date..end_date) }

  # TODO: is this used ?
  # def self.sync_holidays(country = 'CZ', year = Date.current.year)
  #   HolidaySyncService.new(country, year).sync
  # end

end 