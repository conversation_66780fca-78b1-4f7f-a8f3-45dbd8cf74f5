class Work < ApplicationRecord
  acts_as_tenant(:company)

  belongs_to :company
  belongs_to :client, optional: true
  belongs_to :booking, optional: true
  
  has_many :work_assignments, dependent: :destroy
  has_many :contracts, through: :work_assignments
  has_many :work_sessions, dependent: :destroy
  has_many :orders
  
  validates :title, presence: true
  validates :company, presence: true
  validates :status, presence: true
  
  validate :must_have_at_least_one_assignment, on: :update
  
  enum status: {  
    scheduled: "scheduled", 
    in_progress: "in_progress", 
    completed: "completed", 
    cancelled: "cancelled",
    rescheduled: "rescheduled"
  }

  
  private
  
  def must_have_at_least_one_assignment
    if work_assignments.empty? || work_assignments.all?(&:marked_for_destruction?)
      errors.add(:base, I18n.t('activerecord.errors.models.work.at_least_one_assignment'))
    end
  end

end