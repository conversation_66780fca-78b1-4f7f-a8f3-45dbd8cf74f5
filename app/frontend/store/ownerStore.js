import axios from 'axios';

export default {
  namespaced: true,
  state: {
    employees: [],
    events: []
  },
  getters: {
    employees: state => state.employees,
    events: state => state.events
  },
  mutations: {
    setEmployees(state, employees) {
      state.employees = employees;
    },
    setEvents(state, events) {
      state.events = events;
    },
    EVENT_CONFIRMED(state, eventId) {
      state.events = state.events.filter(e => e.id !== eventId);
    },
  },
  actions: {
    async fetchEmployees({ commit }) {
      try {
        const response = await axios.get('/api/v1/employees');
        commit('setEmployees', response.data);
      } catch (error) {
        console.error('Error fetching employees:', error.response ? error.response.data : error.message);
      }
    },
    async fetchEvents({ commit }, status = 'pending') {
      try {
        const response = await axios.get('/api/v1/events', { params: {status} });
        commit('setEvents', response.data);
      } catch (error) {
        console.error('Error fetching pending events:', error.response ? error.response.data : error.message);
      }
    },
    async confirmEvent({ commit }, eventId) {
      try {
        const response = await axios.post(`/api/v1/events/${eventId}/confirm`);
        if (response.data.success) {
          commit('EVENT_CONFIRMED', eventId);
        }
        return response.data;
      } catch (error) {
        console.error('Error confirming event:', error);
        throw error;
      }
    }
  }
};