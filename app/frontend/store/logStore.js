// src/store/logStore.js
import axios from 'axios';
import { updateLog } from '../services/logService';  // Import the service

export default {
  namespaced: true,
  state: {
    dailyLogs: []
  },
  getters: {
    lastDailyLog: state => state.dailyLogs.length ? state.dailyLogs[0] : null,
    lastOpenLog: state => state.dailyLogs.find(log => log.end_time === null) || null,
    hasOpenRecords: (state, getters) => !!getters.lastOpenLog
  },
  mutations: {
    setDailyLogs(state, logs) {
      state.dailyLogs = logs;
    },
    addLog(state, log) {
      state.dailyLogs.unshift(log);
    },
    updateLogEndTime(state, logId) {
      const log = state.dailyLogs.find(log => log.id === logId);
      if (log) {
        log.end_time = new Date().toISOString();
      }
    }
  },
  actions: {
    async fetchLogs({ commit }) {
      try {
        const today = new Date().toISOString().split('T')[0];
        const response = await axios.get('/daily_logs/fetch', { params: { date: today } });
        commit('setDailyLogs', response.data);
      } catch (error) {
        console.error('Error fetching logs:', error.response ? error.response.data : error.message);
      }
    },
    async updateLastLog({ getters, commit }) {
      const lastOpenLog = getters.lastOpenLog;
      if (lastOpenLog) {
        const result = await updateLog(lastOpenLog.id, '');
        if (result.success) {
          commit('updateLogEndTime', lastOpenLog.id);
        } else {
          console.error('Failed to update log:', result.error);
        }
      }
    }
  }
};