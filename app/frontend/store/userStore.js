import axios from 'axios';

export default {
  namespaced: true,
  state: {
    role: '',
    roles: [],
    permissions: {},
    email: '',
    hasPlusPlan: false,
    hasPremiumPlan: false,
    currentPlan: null
  },
  mutations: {
    setUserRole(state, role) {
      state.role = role;
    },
    setUserRoles(state, roles) {
      state.roles = Array.isArray(roles) ? roles : [roles].filter(Boolean);
    },
    setPermissions(state, permissions) {
      state.permissions = permissions || {};
    },
    setEmail(state, email) {  
      state.email = email;
    },
    setPlanInfo(state, { hasPlusPlan, hasPremiumPlan, currentPlan }) {
      state.hasPlusPlan = hasPlusPlan;
      state.hasPremiumPlan = hasPremiumPlan;
      state.currentPlan = currentPlan;
    }
  },
  getters: {
    firstRole: state => state.role,
    hasRole: state => role => state.roles.includes(role),
    hasAnyRole: state => roleList => roleList.some(role => state.roles.includes(role)),
    isOwner: state => state.roles.includes('owner'),
    isAdmin: state => state.roles.includes('admin'),
    isSupervisor: state => state.roles.includes('supervisor'),
    isManager: state => ['owner', 'admin', 'supervisor'].some(r => state.roles.includes(r)),
    
    can: state => permission => !!state.permissions[permission],
    
    hasPlusPlan: state => state.hasPlusPlan,
    hasPremiumPlan: state => state.hasPremiumPlan,
    currentPlan: state => state.currentPlan
  },
  actions: {
    async fetchUserData({ commit, state }) {

       // Skip fetching if we already have roles loaded
      // if (state.roles.length > 0) {
      //   return;
      // }
      
      try {
        const response = await axios.get('/api/v1/user');
        const { role, roles, permissions, email, has_plus_plan, has_premium_plan, current_plan } = response.data;

        commit('setUserRole', role);
        commit('setUserRoles', roles || role);
        commit('setPermissions', permissions);
        commit('setEmail', email);
        commit('setPlanInfo', {
          hasPlusPlan: has_plus_plan,
          hasPremiumPlan: has_premium_plan,
          currentPlan: current_plan
        });
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    }
  }
};