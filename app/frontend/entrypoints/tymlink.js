import '../../assets/stylesheets/tymlink.css';
import '../utils/axiosSetup'; 
import axios from 'axios';
import { createApp } from 'vue/dist/vue.esm-bundler';  
import FlashMessages from '../components/FlashMessages.vue';
import BookingCalendar from '../components/BookingCalendar.vue';
import i18n, { loadLocaleMessages, getStartingLocale } from '../i18n';

const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
if (csrfToken) {
  axios.defaults.headers.common['X-CSRF-Token'] = csrfToken;
} else {
  console.error('CSRF token not found.');
}

// Set default headers for JSON requests
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.headers.put['Content-Type'] = 'application/json';
axios.defaults.headers.patch['Content-Type'] = 'application/json';

document.addEventListener('DOMContentLoaded', async () => {
  const locale = getStartingLocale();
  await loadLocaleMessages(i18n, locale);

  const bookingCalendarElement = document.getElementById('booking-calendar');
    if (bookingCalendarElement) {
      const bookingLinkId = bookingCalendarElement.dataset.bookingLinkId;
      const companySlug = bookingCalendarElement.dataset.companySlug;
      const bookingToken = bookingCalendarElement.dataset.bookingToken;
      const bookingCalendarApp = createApp(BookingCalendar, {
        bookingLinkId: bookingLinkId,
        companySlug: companySlug,
        bookingToken: bookingToken
      });
      bookingCalendarApp.use(i18n);
      bookingCalendarApp.mount(bookingCalendarElement);
    }

  const flashMessagesDiv = document.getElementById('flash-messages');
  if (flashMessagesDiv) {
    const flashMessagesApp = createApp(FlashMessages);
    flashMessagesApp.mount(flashMessagesDiv);
  }

  document.querySelectorAll('[data-confirm]').forEach(element => {
    element.addEventListener('click', (e) => {
      const message = element.getAttribute('data-confirm');
      if (!window.confirm(message)) {
        e.preventDefault();
      } else {
        const event = new CustomEvent('flashMessage', {
          detail: { text: 'Action confirmed', type: 'success' }
        });
        document.dispatchEvent(event);
      }
    });
  });

});