import '../css/main.css';
import '../../assets/stylesheets/welcome.css';
import '../../assets/stylesheets/terms.css';
import '../utils/axiosSetup'; 
import axios from 'axios';
import { createApp, defineAsyncComponent } from 'vue/dist/vue.esm-bundler';  
import FlashMessages from '../components/FlashMessages.vue';
import Terms from '../components/Terms.vue';
import CookieConsent from '../components/CookieConsent.vue';
import BookingCalendar from '../components/BookingCalendar.vue';
import SubscriptionModal from '../components/SubscriptionModal.vue';
import NewsletterModal from '../components/NewsletterModal.vue';
import MeetingCalendar from '../components/meetings/MeetingCalendar.vue';
import i18n, { loadLocaleMessages, getStartingLocale } from '../i18n';

const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
if (csrfToken) {
  axios.defaults.headers.common['X-CSRF-Token'] = csrfToken;
} else {
  console.error('CSRF token not found.');
}

// Set default headers for JSON requests
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.headers.put['Content-Type'] = 'application/json';
axios.defaults.headers.patch['Content-Type'] = 'application/json';

document.addEventListener('DOMContentLoaded', async () => {
  const locale = getStartingLocale();
  await loadLocaleMessages(i18n, locale);

  const mountComponentIfExists = (elementId, Component, props = {}) => {
    const element = document.getElementById(elementId);
    if (element) {
      const app = createApp(Component, props);
      app.use(i18n);
      app.mount(element);
      return app;
    }
    return null;
  };
  
  mountComponentIfExists('flash-messages', FlashMessages);
  mountComponentIfExists('cookie-consent', CookieConsent);

  const termsElement = document.createElement('div');
  termsElement.id = 'terms';
  document.body.appendChild(termsElement);
  const termsApp = createApp(Terms);
  termsApp.mount('#terms');

  document.querySelectorAll('.terms-link').forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      document.dispatchEvent(new CustomEvent('openTerms', {
        detail: e.target.dataset.terms
      }));
    });
  });

  const bookingCalendarElement = document.getElementById('booking-calendar');
  if (bookingCalendarElement) {
    const bookingLinkId = bookingCalendarElement.dataset.bookingLinkId;
    mountComponentIfExists('booking-calendar', BookingCalendar, {
      bookingLinkId: bookingLinkId
    });
  }

  const meetingCalendarElement = document.getElementById('meeting-calendar');
  if (meetingCalendarElement) {
    const token = meetingCalendarElement.dataset.token;
    const companyName = meetingCalendarElement.dataset.companyName;
    const meetingCalendarApp = createApp(MeetingCalendar, { token, companyName });
    meetingCalendarApp.use(i18n);
    meetingCalendarApp.mount(meetingCalendarElement);
  }

  const subscriptionModalElement = document.getElementById('subscription-modal');
  if (subscriptionModalElement) {
    const subscriptionModalApp = createApp(SubscriptionModal);
    const subscriptionModalInstance = subscriptionModalApp.mount('#subscription-modal');
    
    window.openSubscriptionModal = (tier) => {
      subscriptionModalInstance.open(tier);
    };
  }

  const newsletterModalElement = document.createElement('div');
  newsletterModalElement.id = 'newsletter-modal';
  document.body.appendChild(newsletterModalElement);
  
  const newsletterModalApp = createApp(NewsletterModal);
  const newsletterModalInstance = newsletterModalApp.mount('#newsletter-modal');

  document.getElementById('newsletter-signup')?.addEventListener('click', (e) => {
    e.preventDefault();
    newsletterModalInstance.open();
  });

  document.querySelectorAll('[data-confirm]').forEach(element => {
    element.addEventListener('click', (e) => {
      const message = element.getAttribute('data-confirm');
      if (!window.confirm(message)) {
        e.preventDefault();
      } else {
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { text: 'Action confirmed', type: 'success' }
        }));
      }
    });
  });

  document.querySelectorAll('.pwd-field').forEach(toggleButton => {
    const parentContainer = toggleButton.parentElement;
    if (!parentContainer) return;

    const input = parentContainer.querySelector('input[type="password"], input[type="text"]');
    if (!input) return;

    toggleButton.addEventListener('click', () => {
      const isPassword = input.type === 'password';
      input.type = isPassword ? 'text' : 'password';
      toggleButton.textContent = isPassword ? 'skrýt' : 'ukázat';
      toggleButton.setAttribute('aria-label', isPassword ? 'skrýt' : 'ukázat');
    });
  });

  const flashMessages = JSON.parse(document.getElementById('flash-messages')?.dataset.messages || '{}');
  if (flashMessages.analytics_event === "registration_complete" && typeof gtag === 'function') {
    setTimeout(() => {
      gtag('event', 'conversion', {
        'event_category': 'registration',
        'event_label': 'user_signup',
        'send_to': 'G-HWE817PRNM'
      });
      
      gtag('event', 'user_registration');
    }, 100);
  }
});