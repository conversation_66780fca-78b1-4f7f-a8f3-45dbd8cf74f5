import '../../assets/stylesheets/tymlink.css';
import '../utils/axiosSetup'; 
import axios from 'axios';
import { createApp } from 'vue/dist/vue.esm-bundler';  
import FlashMessages from '../components/FlashMessages.vue';
import MeetingCalendar from '../components/meetings/MeetingCalendar.vue';
import i18n, { loadLocaleMessages, getStartingLocale } from '../i18n';

const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
if (csrfToken) {
  axios.defaults.headers.common['X-CSRF-Token'] = csrfToken;
} else {
  console.error('CSRF token not found.');
}

// Set default headers for JSON requests
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.headers.put['Content-Type'] = 'application/json';
axios.defaults.headers.patch['Content-Type'] = 'application/json';

document.addEventListener('DOMContentLoaded', async () => {
  const locale = getStartingLocale();
  await loadLocaleMessages(i18n, locale);

  const meetingCalendarElement = document.getElementById('meeting-calendar');
  if (meetingCalendarElement) {
    const token = meetingCalendarElement.dataset.token;
    const companyName = meetingCalendarElement.dataset.companyName;
    const meetingCalendarApp = createApp(MeetingCalendar, { token, companyName });
    meetingCalendarApp.use(i18n);
    meetingCalendarApp.mount(meetingCalendarElement);
  }

  const flashMessagesDiv = document.getElementById('flash-messages');
  if (flashMessagesDiv) {
    const flashMessagesApp = createApp(FlashMessages);
    flashMessagesApp.use(i18n);
    flashMessagesApp.mount(flashMessagesDiv);
  }

  document.querySelectorAll('[data-confirm]').forEach(element => {
    element.addEventListener('click', (e) => {
      const message = element.getAttribute('data-confirm');
      if (!window.confirm(message)) {
        e.preventDefault();
      } else {
        const event = new CustomEvent('flashMessage', {
          detail: { text: 'Action confirmed', type: 'success' }
        });
        document.dispatchEvent(event);
      }
    });
  });

});