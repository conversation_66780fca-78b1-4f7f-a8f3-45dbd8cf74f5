import '../css/main.css';
import '../css/application.css';
import '../utils/axiosSetup'; 
import axios from 'axios';
import { createApp } from 'vue/dist/vue.esm-bundler';  
import store from '../store';
//import i18n from '../i18n';
import i18n, { loadLocaleMessages, getStartingLocale } from '../i18n';

import HeaderDropdown from '../components/HeaderDropdown.vue';
import FlashMessages from '../components/FlashMessages.vue';
import Holidays from '../components/Holidays.vue';
import Sidebar from '../components/Sidebar.vue';
import Topbar from '../components/Topbar.vue';

import Mainbox from '../components/Mainbox.vue';
import DailyLogsApp from '../components/dailylogs/DailyLogsIndex.vue';
import EventList from '../components/events/EventList.vue';
import CompanyIndex from '../components/companies/CompanyIndex.vue';
import MonthlyReport from '../components/MonthlyReport.vue';
import OwnerMonthlyReport from '../components/OwnerMonthlyReport.vue';
import CompanyConnections from '../components/CompanyConnections.vue';
import CookieConsent from '../components/CookieConsent.vue';
import ContractsList from '../components/contracts/ContractsList.vue';
import WorksIndex from '../components/works/WorksIndex.vue';
import BookingsIndex from '../components/bookings/BookingsIndex.vue';
import UserSettingsForm from '../components/user_profile/UserSettingsForm.vue';
import MeetingsIndex from '../components/meetings/MeetingsIndex.vue';
import ActivityDashboard from '../components/reports/ActivityDashboard.vue';

// Import the Central Modal
import CentralModal from '../components/shared/CentralModal.vue';

// Import components to be loaded into the modal
import EventForm from '../components/events/EventForm.vue';
import WorkForm from '../components/works/WorkForm.vue';
import MeetingForm from '../components/meetings/MeetingForm.vue';

// Temporary solution for the application layout before the SPA refactoring
function updateElementVisibility() {
  const isManager = store.getters['userStore/isManager'];
  document.querySelectorAll('.is-manager').forEach(el => {
    el.style.visibility = isManager ? 'visible' : 'hidden';
  });
  
  const canManageContracts = store.getters['userStore/can']('can_manage_contracts');
  if (canManageContracts) {
    document.querySelectorAll('.can-manage-contracts').forEach(el => {
      el.style.display = '';
    });
  }
}


const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
if (csrfToken) {
  axios.defaults.headers.common['X-CSRF-Token'] = csrfToken;
} else {
  console.error('CSRF token not found.');
}

// Set default headers for JSON requests
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.headers.put['Content-Type'] = 'application/json';
axios.defaults.headers.patch['Content-Type'] = 'application/json';

// Centralized data initialize
let dataInitialized = false;
async function initializeAppData() {
  if (dataInitialized) return;

  try {
    const subscriptionResponse = await axios.get('/api/v1/subscription_status');
    const subscriptionData = subscriptionResponse.data;
    window.appSubscription = subscriptionData;
    
    await store.dispatch('userStore/fetchUserData');
    
    dataInitialized = true;
    updateElementVisibility();
    return subscriptionData;
  } catch (error) {
    console.error('Error initializing app data:', error);
    return { current_plan: 'free', available_features: [] };
  }
}

document.addEventListener('DOMContentLoaded', async () => {
  const subscriptionData = await initializeAppData();
  
  // Explicitly load translations before mounting any components
  // TODO: This is a temporary solution to load translations before mounting any components.
  const locale = getStartingLocale();
  await loadLocaleMessages(i18n, locale);
  
  // Create the main Vue app instance *before* mounting individual components
  // This is necessary for app.component to work correctly across all mounted instances
  // We mount temporary placeholder apps for components that need their own scope first.
  // Let's refine this: We need *one* main app instance if we want global components.
  // The current structure mounts multiple apps. This needs a bigger refactor.

  // --- TEMPORARY WORKAROUND for multiple apps --- 
  // We will mount the CentralModal as its own app, and register the forms 
  // globally *within that specific app instance*. This isn't ideal but fits the current structure.
  // A better long-term solution is a single root Vue app.

  // Mount the Central Modal
  const centralModalElement = document.getElementById('central-modal-app');
  if (centralModalElement) {
    const centralModalApp = createApp(CentralModal);
    // Register components globally *for this app instance*
    centralModalApp.component('EventForm', EventForm);
    centralModalApp.component('WorkForm', WorkForm);
    centralModalApp.component('MeetingForm', MeetingForm);
    // If forms need the store:
    // centralModalApp.use(store); 
    centralModalApp.use(i18n);
    centralModalApp.mount(centralModalElement);
  }

  const cookieConsentApp = createApp(CookieConsent);
  cookieConsentApp.use(i18n);
  cookieConsentApp.mount('#cookie-consent');
  document.addEventListener('cookieConsentUpdated', (event) => {
    const { consents } = event.detail;
  });

  const sidebarElement = document.getElementById('sidebar-app');
  if (sidebarElement) {
    const propsData = { ...sidebarElement.dataset }; 
    if (propsData.isWorking) {
      propsData.isWorking = propsData.isWorking === 'true';
    }
    const sidebarApp = createApp(Sidebar, propsData);
    sidebarApp.use(store);
    sidebarApp.use(i18n);
    sidebarApp.mount(sidebarElement);
  }

  const topbarElement = document.getElementById('topbar-app');
  if (topbarElement) {
    const propsData = { ...topbarElement.dataset };
    const topbarApp = createApp(Topbar, propsData);
    topbarApp.use(store);
    topbarApp.use(i18n);
    topbarApp.mount(topbarElement);
  }

  const holidaysElement = document.getElementById('holidays-app');
  if (holidaysElement) {
    const holidaysApp = createApp(Holidays);
    holidaysApp.use(i18n);
    holidaysApp.mount(holidaysElement);
  }

  const dailyLogsElement = document.getElementById('daily-logs-index');
  if (dailyLogsElement) {
    const dailyLogsApp = createApp(DailyLogsApp);
    dailyLogsApp.use(store); 
    dailyLogsApp.use(i18n);
    dailyLogsApp.mount(dailyLogsElement);
  }

  const eventListElement = document.getElementById('event-list');
  if (eventListElement) {
    const eventListApp = createApp(EventList)
    eventListApp.use(store); 
    eventListApp.use(i18n);
    eventListApp.mount(eventListElement);
  }

  const companyListElement = document.getElementById('company-list');
  if (companyListElement) {
    const companyIndexApp = createApp(CompanyIndex);
    companyIndexApp.use(store);
    companyIndexApp.use(i18n);
    companyIndexApp.mount(companyListElement);
  }

  const worksElement = document.getElementById('works-index');
  if (worksElement) {
    const worksApp = createApp(WorksIndex);
    worksApp.use(i18n);
    worksApp.mount(worksElement);
  }

  const bookingsElement = document.getElementById('bookings-index');
  if (bookingsElement) {
    const bookingsApp = createApp(BookingsIndex);
    bookingsApp.use(store);
    bookingsApp.use(i18n);
    bookingsApp.mount(bookingsElement);
  }

  const meetingsElement = document.getElementById('meetings-index');
  if (meetingsElement) {
    const meetingsApp = createApp(MeetingsIndex);
    meetingsApp.use(store);
    meetingsApp.use(i18n);
    meetingsApp.mount(meetingsElement);
  }

  const userSettingsElement = document.getElementById('user-settings-form');
  if (userSettingsElement) {
    const userSettingsApp = createApp(UserSettingsForm);
    userSettingsApp.use(store);
    userSettingsApp.use(i18n);
    userSettingsApp.mount(userSettingsElement);
  }

  const dropdownElement = document.getElementById('header-dropdown');
  if (dropdownElement) {
    const user = JSON.parse(dropdownElement.dataset.user);
    const companyData = {
      name: dropdownElement.dataset.companyName || null,
    }
    dropdownElement.dataset.subscription = JSON.stringify(subscriptionData);
    const dropdownApp = createApp(HeaderDropdown, { 
      user,
      company: companyData,
      subscription: subscriptionData
    });
    dropdownApp.use(store);
    dropdownApp.use(i18n);
    dropdownApp.mount(dropdownElement);
  }

  const mainBoxElement = document.getElementById('mainbox');
  if (mainBoxElement) {
    mainBoxElement.dataset.subscription = JSON.stringify(subscriptionData);
    const mainboxApp = createApp(Mainbox, { subscription: subscriptionData });
    mainboxApp.use(store); 
    mainboxApp.use(i18n);
    mainboxApp.mount(mainBoxElement);
  }

  const monthlyRepElement = document.getElementById('monthly-report');
  if (monthlyRepElement) {
    const reportApp = createApp(MonthlyReport);
    reportApp.use(i18n);
    reportApp.mount(monthlyRepElement);
  }

  const ownerMonthlyRepElement = document.getElementById('owner-monthly-report');
  if (ownerMonthlyRepElement) {
    const ownerReportApp = createApp(OwnerMonthlyReport);
    ownerReportApp.use(store);
    ownerReportApp.use(i18n);
    ownerReportApp.mount(ownerMonthlyRepElement);
  }

  const companyConnectionsElement = document.getElementById('company-connections');
  if (companyConnectionsElement) {
    const embedded = companyConnectionsElement.dataset.embedded;
    const companyConnectionsApp = createApp(CompanyConnections, { embedded });
    companyConnectionsApp.use(i18n);
    companyConnectionsApp.mount(companyConnectionsElement);
  }

  const contractElement = document.getElementById('contracts-list')
  if (contractElement) {
    const contractsListApp = createApp(ContractsList);
    contractsListApp.use(i18n);
    contractsListApp.mount(contractElement);
  }

  const flashMessagesDiv = document.getElementById('flash-messages');
  if (flashMessagesDiv) {
    const flashMessagesApp = createApp(FlashMessages);
    flashMessagesApp.use(i18n);
    flashMessagesApp.mount(flashMessagesDiv);
  }
  
  const activityDashboardElement = document.getElementById('activity-dashboard');
  if (activityDashboardElement) {
    const activityDashboardApp = createApp(ActivityDashboard);
    activityDashboardApp.use(i18n);
    activityDashboardApp.use(store);
    activityDashboardApp.mount(activityDashboardElement);
  }

  document.querySelectorAll('[data-confirm]').forEach(element => {
    element.addEventListener('click', (e) => {
      const message = element.getAttribute('data-confirm');
      if (!window.confirm(message)) {
        e.preventDefault();
      } else {
        const event = new CustomEvent('flashMessage', {
          detail: { text: 'Action confirmed', type: 'success' }
        });
        document.dispatchEvent(event);
      }
    });
  });
  const flashMessages = JSON.parse(document.getElementById('flash-messages')?.dataset.messages || '{}');
  if (flashMessages.analytics_event === "registration_complete") {
    gtag('event', 'conversion', {
      'event_category': 'registration',
      'event_label': 'user_signup',
      'send_to': 'G-HWE817PRNM'
    });
    
    gtag('event', 'user_registration');
  }
  
});

