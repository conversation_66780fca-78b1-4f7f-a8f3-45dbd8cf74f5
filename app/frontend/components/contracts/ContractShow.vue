<template>
  <div class="mb-4">
    <div v-if="loading" class="card-content">{{ $t('loading', 'Načítání...') }}</div>
    <div v-else-if="error" class="card-content text-red-500 text-center">{{ error }}</div>
    <template v-else>
      <div class="card-header">
        <div class="flex flex-col">
          <h2 class="text-xl font-semibold">{{ contract.first_name }} {{ contract.last_name }}</h2>
          <span class="text-sm text-gray-500">{{ contract.job_title || $t('no_job_title', 'Bez pracovního titulu') }}</span>
        </div>
        <div class="flex items-center gap-2">
          <span class="badge" :class="statusBadgeClass">
            {{ translatedStatus }}
          </span>
          <span v-if="isCurrentUser" class="badge badge-warning">{{ $t('contracts.this_is_you', '<PERSON><PERSON><PERSON> jste vy') }}</span>
        </div>
      </div>

      <div class="card-content space-y-3">
        <div class="detail-item">
          <span class="font-medium mr-2 w-24 inline-block text-sm">{{ $t('contracts.contract_type_label', 'Typ kontraktu:') }}</span>
          <span class="text-body">{{ contract.contract_type || $t('unspecified_f', 'Neuvedeno') }}</span>
        </div>
        <div class="detail-item">
          <span class="font-medium mr-2 w-24 inline-block text-sm">{{ $t('email', 'E-mail:') }}</span>
          <a v-if="contract.email" :href="'mailto:' + contract.email" class="text-link">{{ contract.email }}</a>
          <span v-else class="text-gray-500">{{ $t('no_email_present', 'Bez e-mailu') }}</span>
        </div>
        <div class="detail-item">
          <span class="font-medium mr-2 w-24 inline-block text-sm">{{ $t('phone', 'Telefon:') }}</span>
          <a v-if="contract.phone" :href="'tel:' + contract.phone" class="text-link">{{ contract.phone }}</a>
          <span v-else class="text-gray-500">{{ $t('no_phone_present', 'Bez telefonu') }}</span>
        </div>

        <div class="detail-item" v-if="contract.user_id && isOwner">
          <label for="role-select" class="font-medium mr-2 w-24 inline-block text-sm align-top">{{ $t('role_label', 'Role:') }}</label>
          <div class="inline-flex items-center gap-2">
            <select id="role-select" v-model="selectedRole" class="form-select form-select-sm">
              <option v-for="role in availableRoles" :key="role" :value="role">
                {{ roleTranslations[role] || role }}
              </option>
            </select>
            <button @click="updateRole" class="btn btn-sm btn-light">{{ $t('save', 'Uložit') }}</button>
          </div>
        </div>

        <div class="detail-item">
           <span class="font-medium mr-2 w-24 inline-block text-sm">{{ $t('access_label', 'Přístup:') }}</span>
           <div class="inline-flex items-center gap-2">
              <span v-if="contract.user_id" class="badge badge-success">
                {{ $t('connected', 'Připojen') }}
              </span>
              <template v-else>
                <span class="badge" :class="invitationStatusBadgeClass">
                  {{ invitationStatusText }}
                </span>
                <span class="text-xs text-gray-500"> ({{ $t('sent_on', 'Odesláno:') }} {{ invitationDate }})</span>
              </template>
           </div>
        </div>
      </div>

      <div class="card-footer">
         <button @click="$emit('edit')" class="btn btn-outline">{{ $t('edit', 'Upravit') }}</button>

        <button v-if="canResendInvitation"
                @click="resendInvitation"
                class="btn btn-outline">
          {{ $t('contracts.resend_invitation', 'Znovu odeslat pozvání') }}
        </button>

        <template v-if="contract.status === 'active'">
          <button @click="suspendContract" class="btn btn-outline">
            {{ $t('suspend', 'Pozastavit') }}
          </button>
          <button @click="terminateContract" class="btn btn-danger-light">
            {{ $t('terminate', 'Ukončit') }}
          </button>
        </template>

        <template v-if="contract.status === 'suspended'">
          <button @click="reactivateContract" class="btn btn-light">
            {{ $t('activate', 'Aktivovat') }}
          </button>
          <button @click="terminateContract" class="btn btn-danger-light">
            {{ $t('terminate', 'Ukončit') }}
          </button>
        </template>

        <button v-if="isOwner && contract.status !== 'deleted'" @click="confirmDelete" class="btn btn-danger-light">
          {{ $t('contracts.delete_permanently', 'Trvale smazat') }}
        </button>
      </div>

      <!-- Delete Confirmation Modal (basic structure, can be improved) -->
      <div v-if="showDeleteConfirm" class="modal-overlay" @click.self="cancelDelete">
        <div class="modal-container">
           <div class="modal-header">
            <h3>{{ $t('contracts.permanent_delete_warning_title', 'POZOR! Nevratné smazání') }}</h3>
            <button class="close-btn" @click="cancelDelete">&times;</button>
           </div>
          <div class="central-modal-content">
            <p>{{ $t('contracts.permanent_delete_warning_text_1', 'Trvale smažete kontrakt') }} <strong>{{ contract.first_name }} {{ contract.last_name }}</strong> {{ $t('contracts.permanent_delete_warning_text_2', 'včetně všech záznamů, aktivit a událostí!') }}</p>
            <p class="mt-4">{{ $t('to_confirm_enter', 'Pro potvrzení zadejte:') }} <strong>{{ deleteConfirmationCode }}</strong></p>
            <input v-model="confirmationInput" type="text" class="form-input mt-2" :placeholder="$t('enter_confirmation_code', 'Zadejte potvrzovací kód')">

          </div>
          <div class="modal-footer">
             <button @click="cancelDelete" class="btn btn-secondary">{{ $t('cancel', 'Zrušit') }}</button>
            <button @click="executeDelete" class="btn btn-danger" :disabled="confirmationInput !== deleteConfirmationCode">
              {{ $t('contracts.delete_permanently', 'Trvale smazat') }}
            </button>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  props: {
    contractId: {
      type: [Number, String],
      required: true
    },
    currentUserId: {
      type: Number,
      default: null
    },
    userRole: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      contract: {},
      loading: true,
      error: null,
      showDeleteConfirm: false,
      confirmationInput: '',
      deleteConfirmationCode: '',
      selectedRole: '',
      availableRoles: [],
    }
  },
  computed: {
    roleTranslations() {
      return {
        owner: this.$t('roles.owner', 'Vlastník'),
        employee: this.$t('roles.employee', 'Zaměstnanec'),
        admin: this.$t('roles.admin', 'Administrátor'),
        supervisor: this.$t('roles.supervisor', 'Supervizor'),
        manager: this.$t('roles.manager', 'Manažer')
      }
    },
    isCurrentUser() {
      return this.contract.user_id === this.currentUserId
    },
    isOwner() {
      return this.userRole === 'owner';
    },
    canResendInvitation() {
      return this.contract.email && !this.contract.invitation_accepted
    },
    invitationDate() {
      if (!this.contract.invitation_sent_at) return 'N/A';
      return new Date(this.contract.invitation_sent_at).toLocaleString(this.$i18n.locale, {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    translatedStatus() {
       const statusTranslations = {
        active: this.$t('statuses.active_m', 'Aktívny'),
        suspended: this.$t('statuses.suspended_m', 'Pozastavený'),
        terminated: this.$t('statuses.terminated_m', 'Ukončený'),
        deleted: this.$t('statuses.deleted_m', 'Smazaný'),
        pending_invitation: this.$t('statuses.pending_invitation', 'Čaká na pozvání') 
      };
      return statusTranslations[this.contract.status] || this.contract.status;
    },
    statusBadgeClass() {
      const statusClasses = {
        active: 'badge-success',
        suspended: 'badge-warning',
        terminated: 'badge-danger',
        deleted: 'badge-danger',
        pending_invitation: 'badge-gray'
      };
       return statusClasses[this.contract.status] || 'badge-gray';
    },
     invitationStatusText() {
      return this.contract.user_id ? this.$t('connected', 'Připojen') : this.$t('waiting_for_confirmation', 'Čeká na potvrzení');
    },
    invitationStatusBadgeClass() {
      return this.contract.user_id ? 'badge-success' : 'badge-gray';
    }
  },
  mounted() {
    this.fetchContract()
  },
  methods: {
    fetchContract() {
      this.loading = true
      
      axios.get(`/contracts/${this.contractId}`, {
        headers: { 'Accept': 'application/json' }
      })
      .then(response => {
        this.contract = response.data
        this.selectedRole = response.data.current_role || ''
        this.availableRoles = response.data.available_roles || []
        this.loading = false
      })
      .catch(error => {
        console.error('Error fetching contract:', error)
        this.error = this.$t('contracts.error_loading_data', 'Nepodařilo se načíst data o kontraktu')
        this.loading = false
      })
    },
    resendInvitation() {
      if (!confirm(this.$t('contracts.confirm_resend_invitation', 'Potvrďte opětovné odeslání pozvánky'))) return
      
      axios.post(`/contracts/${this.contractId}/resend_invitation`)
        .then(() => {
          this.$emit('message', { type: 'success', text: this.$t('contracts.invitation_resent', 'Pozvánka odeslána znovu') })
        })
        .catch(error => {
          console.error('Error resending invitation:', error)
          this.$emit('message', { type: 'error', text: this.$t('contracts.error_resending_invitation', 'Chyba při odesílání pozvánky') })
        })
    },
    updateRole() {
      if (!this.selectedRole) return
      
      axios.post(`/contracts/${this.contractId}/update_role`, {
        role_name: this.selectedRole
      })
      .then(response => {
        this.$emit('message', { type: 'success', text: response.data.message })
      })
      .catch(error => {
        console.error('Error updating role:', error)
        this.$emit('message', { 
          type: 'error', 
          text: error.response?.data?.message || this.$t('contracts.error_updating_role', 'Chyba při aktualizaci role') 
        })
      })
    },
    suspendContract() {
      if (!confirm(this.$t('contracts.confirm_suspend', 'Opravdu chcete pozastavit tento kontrakt? Zaměstnanec nebude mít přístup do pracovního prostoru.'))) return
      
      axios.post(`/contracts/${this.contractId}/suspend`)
        .then(response => {
          this.contract.status = 'suspended';
          this.$emit('message', { type: 'success', text: response.data.message || this.$t('contracts.contract_suspended', 'Kontrakt byl pozastaven') })
        })
        .catch(error => {
          console.error('Error suspending contract:', error)
          this.$emit('message', { type: 'error', text: this.$t('contracts.error_suspending_contract', 'Chyba při pozastavení kontraktu') })
        })
    },
    
    reactivateContract() {
      axios.post(`/contracts/${this.contractId}/reactivate`)
        .then(response => {
          this.contract.status = 'active';
          this.$emit('message', { type: 'success', text: response.data.message || this.$t('contracts.contract_reactivated', 'Kontrakt byl znovu aktivován') })
        })
        .catch(error => {
          console.error('Error reactivating contract:', error)
          this.$emit('message', { type: 'error', text: this.$t('contracts.error_reactivating_contract', 'Chyba při aktivaci kontraktu') })
        })
    },
    
    terminateContract() {
      if (!confirm(this.$t('contracts.confirm_terminate', 'POZOR: Opravdu chcete UKONČIT tento kontrakt? Tato akce je nevratná!'))) return
      
      axios.post(`/contracts/${this.contractId}/terminate`)
        .then(response => {
          this.contract.status = 'terminated';
          this.$emit('message', { type: 'success', text: response.data.message || this.$t('contracts.contract_terminated', 'Kontrakt byl ukončen') })
        })
        .catch(error => {
          console.error('Error terminating contract:', error)
          this.$emit('message', { type: 'error', text: this.$t('contracts.error_terminating_contract', 'Chyba při ukončení kontraktu') })
        })
    },
    confirmDelete() {
      this.deleteConfirmationCode = `SMAZAT-${this.contractId}`;
      this.showDeleteConfirm = true;
    },
    
    cancelDelete() {
      this.showDeleteConfirm = false;
      this.confirmationInput = '';
    },
    
    executeDelete() {
      if (this.confirmationInput !== this.deleteConfirmationCode) return;
      
      axios.delete(`/contracts/${this.contractId}`, {
        data: { confirmation_code: this.confirmationInput }
      })
        .then(() => {
          this.$emit('deleted')
          this.$emit('message', { type: 'success', text: this.$t('contracts.contract_deleted_permanently', 'Kontrakt byl trvale smazán') })
          this.showDeleteConfirm = false;
        })
        .catch(error => {
          console.error('Error deleting contract:', error)
          this.$emit('message', { type: 'error', text: error.response?.data?.errors?.[0] || this.$t('contracts.error_deleting_contract', 'Chyba při mazání kontraktu') })
        })
    }
  }
}
</script>