<template>
  <div>
    <div class="section-header mb-4">
      <h2>{{ companyName }}</h2>
    </div>

    <div v-if="loading" class="loading text-center p-4">
      {{ $t('loading', 'Načítání...') }}
    </div>
    <div v-else-if="error" class="text-muted text-center p-4">
      {{ error }}
    </div>
    
    <div v-else class="content-panel p-0 w-full max-w-[950px] mx-auto">
      <!-- Contract List Items -->
      <div v-for="contract in contracts" 
           :key="contract.id" 
           class="flex flex-col p-3 border-b border-gray-200 last:border-b-0 hover:bg-gray-50">
        
        <!-- Top Row: Info + Toggle + Actions -->
        <div class="flex justify-between items-start w-full gap-2">
          <!-- Left Side: Name, Title, Status -->
          <div class="flex-1">
            <div>
              <h2 class="text-base font-semibold text-gray-800">
                {{ contract.prefix}} {{ contract.first_name }} {{ contract.last_name }} {{ contract.suffix }}
              </h2>
              <span class="text-sm text-gray-600">
                {{ contract.job_title || $t('unspecified_f', 'Neuvedeno') }}
              </span>
            </div>
            <div class="mt-1 flex flex-wrap gap-1 items-center">
              <div v-if="contract.status == 'terminated'">
                <span class="badge badge-gray"> 
                  {{ contract.translated_status }}
                </span>
              </div>
              <div v-else class="flex flex-wrap gap-1 items-center">
                <span :class="'badge badge-' + contract.status">
                  {{ contract.translated_status }}
                </span>
                <span v-if="contract.user_id === currentUserId" 
                      class="badge badge-warning"> 
                  {{ $t('contracts.this_is_you', 'Tohle jste vy') }}
                </span>
                <span v-else-if="!contract.user_id" 
                      class="badge badge-danger">
                  {{ $t('contracts.no_connection', 'Bez přepojení') }}
                </span>
              </div>
            </div>
          </div>

          <!-- Right Side: Actions and Toggle -->
          <div class="flex items-start gap-x-4 flex-shrink-0">
            <!-- Always visible action buttons -->
            <button @click="openEditForm(contract.id)" class="text-link text-sm">
              {{ $t('edit', 'Upravit') }}
            </button>
            <button @click="openContractDetail(contract.id)" class="text-link text-sm">
              {{ $t('show_detail', 'Zobrazit detail') }}
            </button>
             <!-- Toggle Button -->
            <!-- <button @click="toggleItem(contract.id)" class="text-link p-1">
              <ChevronDown v-if="expandedItem !== contract.id" :size="18" />
              <ChevronUp v-else :size="18" />
            </button> -->
          </div>
        </div>

        <!-- Middle Row: Monthly Summary -->
        <div class="mt-2 flex flex-wrap items-center gap-x-3 gap-y-1 text-sm text-gray-600">
          <span class="font-medium whitespace-nowrap">{{ currentMonth.charAt(0).toUpperCase() + currentMonth.slice(1) }}:</span>
          <span class="whitespace-nowrap">{{ $t('contracts.worked_hours', 'Odpracované hodiny') }}: {{ formatHours(contract.total_hours) }}</span>
          <span class="whitespace-nowrap">{{ $t('contracts.worked_days', 'Odpracované dny') }}: {{ contract.days_worked || 0 }}</span>
          <span v-if="Object.keys(contract.event_counts).length > 0" class="whitespace-nowrap">
             {{ getTranslatedEventCounts(contract.event_counts) }}
          </span>
        </div>
        <div class="mt-1 text-xs text-gray-500">
          {{ $t('contracts.last_activity', 'Poslední aktivita') }}: {{ formatDate(contract.last_active) }}
        </div>
          
        <!-- Expanded Details (No actions here anymore) -->
        <div v-if="expandedItem === contract.id" class="mt-3 pt-3 border-t border-gray-100 flex flex-col gap-y-1 text-sm text-gray-700">
        </div>
      </div>
    </div>
    
    <!-- Add Colleague Button -->
    <div class="mt-4 flex justify-end">
      <button @click="openNewForm()" class="btn btn-primary">{{ $t('add_colleague', 'Přidat kolegu') }}</button>
    </div>
    
    <!-- Modal -->
    <div v-if="showModal" class="modal-overlay">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h3>{{ modalTitleComputed }}</h3>
          <button @click="closeModal" class="close-btn">&times;</button>
        </div>
        <div class="central-modal-content"> 
          <ContractShow 
            v-if="activeModal === 'show'" 
            :contract-id="activeContractId"
            :current-user-id="currentUserId"
            :user-role="userRole"
            @edit="openEditForm(activeContractId)"
            @deleted="handleDeleted"
            @message="handleMessage"
          />
          <ContractForm 
            v-if="activeModal === 'form'" 
            :contract-id="activeContractId"
            @saved="handleSaved"
            @cancel="closeModal"
            @message="handleMessage"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { ChevronDown, ChevronUp, Edit2, ArrowRight, Clock } from 'lucide-vue-next'
import ContractShow from './ContractShow.vue'
import ContractForm from './ContractForm.vue'

export default {
  components: {
    ChevronDown,
    ChevronUp,
    Edit2, 
    ArrowRight,
    Clock, 
    ContractShow,
    ContractForm
  },
  data() {
    return {
      contracts: [],
      companyName: '',
      currentUserId: null,
      userRole: null,
      loading: true,
      error: null,
      expandedItem: null,
      showModal: false,
      activeModal: null,
      activeContractId: null,
      flashMessage: null
    }
  },
  computed: {
    currentMonth() {
      return new Date().toLocaleDateString('cs-CZ', { 
        month: 'long', 
        year: 'numeric' 
      })
    },
    modalTitleComputed() {
      if (this.activeModal === 'show') {
        return this.$t('contracts.contract_detail_title', 'Detail kontraktu')
      }
      return this.activeContractId ? this.$t('edit_contract', 'Upravit kontrakt') : this.$t('new_contract', 'Nový kontrakt')
    }
  },
  mounted() {
    this.currentUserId = parseInt(this.$el.dataset.currentUserId)
    this.fetchContracts()
  },
  methods: {
    toggleItem(id) {
      this.expandedItem = this.expandedItem === id ? null : id
    },
    fetchContracts() {
      this.loading = true
      this.error = null

      axios.get('/contracts/fetch', {
        headers: { 'Accept': 'application/json' }
      })
      .then(response => {
        const data = response.data
        this.contracts = data.contracts
        this.companyName = data.contracts.length > 0 ? data.contracts[0].company_name : ''
        this.currentUserId = data.current_user
        this.userRole = data.user_role
        this.loading = false
      })
      .catch(error => {
        console.error('Error:', error)
        this.error = this.$t('contracts.error_loading_contracts_data', 'Nepodařilo se načíst data')
        this.loading = false
      })
    },
    formatHours(hours) {
      return hours ? `${hours.toFixed(2)} ` : '0'
    },
    formatDate(date) {
      if (!date) return this.$t('none_f', 'žádná')
      return new Date(date).toLocaleDateString('cs-CZ', {
        year: 'numeric',
        day: 'numeric',
        month: 'numeric',
        hour: 'numeric',
        minute: 'numeric'
      })
    },
    openContractDetail(id) {
      this.activeContractId = id
      this.activeModal = 'show'
      this.showModal = true
    },
    openEditForm(id) {
      this.activeContractId = id
      this.activeModal = 'form'
      this.showModal = true
    },
    openNewForm() {
      this.activeContractId = null
      this.activeModal = 'form'
      this.showModal = true
    },
    closeModal() {
      this.showModal = false
      this.activeModal = null
      setTimeout(() => {
        this.activeContractId = null
      }, 300)
    },
    handleSaved() {
      this.fetchContracts()
      this.closeModal()
    },
    handleDeleted() {
      this.fetchContracts()
      this.closeModal()
    },
    handleMessage(message) {
      this.flashMessage = message
      document.dispatchEvent(new CustomEvent('flashMessage', { 
        detail: { text: this.$t(message.text, message.text), type: message.type } 
      }));
      setTimeout(() => {
        this.flashMessage = null
      }, 3000)
    },
    getEventTypeDescription(eventType) {
      const eventTypeMap = {
        'vacation': this.$t('event_type.vacation', 'Dovolená'),
        'illness': this.$t('event_type.illness', 'Nemocenská'),
        'day_care': this.$t('event_type.day_care', 'Návštěva lékaře'),
        'family_sick': this.$t('event_type.family_sick', 'OČR'),
        'travel': this.$t('event_type.travel', 'Cesta'),
        'other': this.$t('event_type.other', 'Jiná absence')
      };
      return eventTypeMap[eventType] || eventType;
    },
    getTranslatedEventCounts(eventCounts) {
      return Object.entries(eventCounts).map(([eventType, count]) => {
        return `${this.getEventTypeDescription(eventType)}: ${count}`;
      }).join(', ');
    }
  }
}
</script>
