<template>
  <div class="card flex flex-col p-4">
    <div class="card-header flex justify-between items-start mb-3 p-0 border-b-0">
      <div class="flex items-center gap-2">
        <h3 class="text-base font-semibold m-0">{{ bookingLink.name }}</h3>
      </div>
      <div class="relative" ref="dropdown">
        <button class="p-1 text-gray-500 hover:text-gray-700" @click="toggleDropdown">
          <MoreVertical :size="18" />
        </button>
        <div
          v-if="showDropdown"
          class="absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-10"
        >
          <a :href="publicBookingUrl" target="_blank" rel="noopener noreferrer"
             class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
             @click="showDropdown = false">
            {{ $t('preview', 'Náhled') }}
          </a>
          <a href="#"
             class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
             @click.prevent="editLink">
            {{ $t('edit', 'Upravit') }}
          </a>
        </div>
      </div>
    </div>

    <div class="flex flex-wrap gap-2 mb-3">
      <span class="badge" :class="bookingLink.active ? 'badge-success' : 'badge-gray'">
        {{ bookingLink.active ? $t('active', 'Aktivní') : $t('inactive', 'Neaktivní') }}
      </span>
    </div>

    <p v-if="bookingLink.description" class="text-sm text-gray-600 mb-3 break-words">
      {{ bookingLink.description }}
    </p>

    <div class="flex flex-col gap-1.5 text-sm text-gray-700 mb-3">
      <div class="flex items-center gap-1.5">
        <span>{{ $t('booking.expected_duration', 'Předpokládané trvaní') }}: {{ bookingLink.duration }} {{ $t('min', 'min.') }}</span>
      </div>

      <div v-if="bookingLink.preferred_days && bookingLink.preferred_days.length" class="flex items-center gap-1.5">
        <span>{{ $t('booking.available_days', 'Dostupné dny') }}: {{ bookingLink.preferred_days.join(', ') }}</span>
      </div>

      <div v-if="bookingLink.morning_limit" class="flex items-center gap-1.5">
        <span>{{ $t('booking.morning_limit', 'Max. dopoledne') }}: {{ bookingLink.morning_limit }}x</span>
      </div>

      <div v-if="bookingLink.afternoon_limit" class="flex items-center gap-1.5">
        <span>{{ $t('booking.afternoon_limit', 'Max. odpoledne') }}: {{ bookingLink.afternoon_limit }}x</span>
      </div>

      <div v-if="bookingLink.daily_limit" class="flex items-center gap-1.5">
        <span>{{ $t('booking.daily_limit', 'Max. denně') }}: {{ bookingLink.daily_limit }}x</span>
      </div>

      <div v-if="bookingLink.book_holidays" class="flex items-center gap-1.5">
        <span>{{ $t('booking.book_holidays', 'Rezervace i na svátky') }}: {{ bookingLink.book_holidays ? $t('yes_', 'Áno') : $t('no_', 'Nie') }}</span>
      </div>

       <div v-if="bookingLink.include_works_in_count" class="flex items-center gap-1.5">
         <span>{{ $t('booking.include_works', 'Zahrnout zakázky do limitu') }}: {{ bookingLink.include_works_in_count ? $t('yes_', 'Áno') : $t('no_', 'Nie') }}</span>
       </div>
    </div>

    <div class="flex flex-col gap-1.5 text-sm text-gray-700 mb-3">
       <div class="flex items-center gap-1.5">
         <a :href="publicBookingUrl" target="_blank" class="text-blue-600 hover:underline break-all">{{ publicBookingUrl }}</a>
         <button class="url-copy-btn" @click="copyUrl(publicBookingUrl)" title="Copy URL">
            <Copy :size="14" />
         </button>
       </div>
    </div>

    <div class="flex flex-col gap-1.5 text-sm text-gray-700">
      <div class="flex items-center gap-1.5">
        <span>{{ $t('updated', 'Aktualizováno') }}: {{ new Date(bookingLink.updated_at).toLocaleString('cs-CZ') }}</span>
      </div>
    </div>

  </div>
</template>

<script>
import { Link, MoreVertical, Clock, CalendarDays, Users, CalendarClock, Copy } from 'lucide-vue-next';

export default {
  components: {
    Link,
    MoreVertical,
    Clock,
    CalendarDays,
    Users,
    CalendarClock,
    Copy
  },
  props: {
    bookingLink: {
      type: Object,
      required: true
    },
    compact: {
      type: Boolean,
      default: false
    },
    companySlug: {
      type: String,
      required: true
    }
  },
  emits: ['edit', 'delete', 'view'],
  data() {
    return {
      showDropdown: false
    };
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  computed: {
    publicBookingUrl() {
      return `${window.location.origin}/r/${this.companySlug}/${this.bookingLink.slug || this.bookingLink.id}`;
    },
  },
  methods: {
    copyUrl(url) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(url)
          .then(() => {
            const event = new CustomEvent('flashMessage', {
              detail: { text: this.$t('url_copied', 'Adresa skopírovaná'), type: 'notice' }
            });
            document.dispatchEvent(event);
          })
          .catch(err => {
            console.error('Could not copy text: ', err);
          });
      } else {
        console.error('Clipboard API not supported');
      }
    },
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
    },
    handleClickOutside(event) {
      if (this.$refs.dropdown && !this.$refs.dropdown.contains(event.target)) {
        this.showDropdown = false;
      }
    },
    editLink() {
      this.showDropdown = false;
      this.$emit('edit', this.bookingLink);
    }
  }
};
</script>

<style scoped>
.booking-link-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.booking-link-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.booking-link-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 500;
}

.booking-link-body {
  padding: 1rem;
}

.description {
  margin-bottom: 1rem;
  color: #666;
}

.booking-link-details {
  margin-bottom: 1rem;
}

.detail-item {
  margin-bottom: 0.5rem;
}

.detail-label {
  font-weight: 500;
  margin-right: 0.5rem;
}

.detail-value {
  color: #666;
}

.booking-link-urls {
  background-color: #0066cc;
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.url-section {
  margin-bottom: 0.75rem;
}

.url-section:last-child {
  margin-bottom: 0;
}

.url-label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.url-display {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.5rem;
}

.url-text {
  flex: 1;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.url-copy-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.url-copy-btn:hover {
  color: #22C55E;
}

.booking-link-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  align-content: center;
  padding: 1rem;
  border-top: 1px solid #dee2e6;
  gap: 0.5rem;
}

@media (max-width: 768px) {
  .booking-link-actions {
    flex-direction: row;
  }
  
  .booking-link-actions .btn,
  .booking-link-actions .btn-secondary {
    width: 100%;
  }
}
</style>