<template>
  <div class="form-section">

    <form @submit.prevent="submitForm">
      <div class="form-group">
        <label for="name" class="form-label sr-only">{{ $t('name', 'Název') }}</label>
        <input type="text" id="name" v-model="form.name" class="form-input" :placeholder="$t('booking.page_name', 'Název rezervační stránky')" required>
      </div>
      
      <div class="form-group">
        <label for="description" class="form-label sr-only">{{ $t('details', 'Detaily') }}</label>
        <textarea id="description" v-model="form.description" :placeholder="$t('details', 'Detaily')" class="form-textarea"></textarea>
      </div>
      
      <div class="form-group">
        <label for="duration" class="form-label sr-only">{{ $t('booking.duration_minutes', '<PERSON><PERSON><PERSON><PERSON> (v minutách)') }}</label>
        <input type="number" id="duration" v-model.number="form.duration" class="form-input" :placeholder="$t('booking.duration_minutes', '<PERSON><PERSON><PERSON><PERSON> (v minutách)')" min="5" step="5">
      </div>

      <div class="form-group">
        <label class="form-label">{{ $t('booking.type', 'Typ rezervace') }}</label>
        <div class="flex flex-col sm:flex-row gap-x-6 gap-y-2 mt-2">
          <div class="form-radio-group">
            <input type="radio" id="location_normal" name="location_type" :value="false" v-model="form.location_required" @change="setLocationTypeNormal" class="form-radio">
            <label for="location_normal" class="form-radio-label">{{ $t('booking.standard_location', 'Standardní lokace') }}</label>
          </div>
          <div class="form-radio-group">
            <input type="radio" id="location_required" name="location_type" :value="true" v-model="form.location_required" @change="setLocationTypeRequired" class="form-radio">
            <label for="location_required" class="form-radio-label">{{ $t('booking.location_required', 'Lokace vyžadována') }}</label>
          </div>
          <div class="form-radio-group">
            <input type="radio" id="location_remote" name="location_type" :value="true" v-model="form.is_remote" @change="setLocationTypeRemote" class="form-radio">
            <label for="location_remote" class="form-radio-label">{{ $t('booking.no_location', 'Bez lokace (online)') }}</label>
          </div>
        </div>
      </div>
    
      <div class="form-group" v-if="!form.location_required && !form.is_remote">
        <label for="location" class="form-label sr-only">{{ $t('location', 'Místo') }}</label>
        <input type="text" id="location" v-model="form.location" class="form-input" :placeholder="$t('booking.location_optional', 'Místo konání (nepovinné)')">
      </div>
        
      <div class="form-group">
        <label class="form-label">{{ $t('booking.available_days', 'Dostupné dny') }}</label>
        <div class="flex flex-wrap gap-x-6 gap-y-2 mt-2">
          <div v-for="day in days" :key="day" class="form-checkbox-group">
            <input type="checkbox" class="form-checkbox" :id="'day-' + day" :value="day" v-model="form.preferred_days" />
            <label :for="'day-' + day" class="form-checkbox-label">{{ day }}</label>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-x-4 gap-y-4">
        <div class="form-group">
          <label for="morning_limit" class="form-label">{{ $t('booking.morning_limit', 'Ranní limit') }}</label>
          <input type="number" id="morning_limit" v-model.number="form.morning_limit" class="form-input" :placeholder="$t('booking.count_unlimited', 'Počet (neomezeně)')">
        </div>
        <div class="form-group">
          <label for="afternoon_limit" class="form-label">{{ $t('booking.afternoon_limit', 'Odpolední limit') }}</label>
          <input type="number" id="afternoon_limit" v-model.number="form.afternoon_limit" class="form-input" :placeholder="$t('booking.count_unlimited', 'Počet (neomezeně)')">
        </div>
        <div class="form-group">
          <label for="daily_limit" class="form-label">{{ $t('booking.daily_limit', 'Denní limit') }}</label>
          <input type="number" id="daily_limit" v-model.number="form.daily_limit" class="form-input" :placeholder="$t('booking.count_unlimited', 'Počet (neomezeně)')">
        </div>
      </div>
      
      <div class="form-group">
        <div class="form-checkbox-group">
          <input type="checkbox" id="include_works_in_count" class="form-checkbox" v-model="form.include_works_in_count">
          <label for="include_works_in_count" class="form-checkbox-label">{{ $t('booking.include_works', 'Zahrnout práce do limitu') }}</label>
        </div>
      </div>
      
      <div class="form-group">
        <div class="form-checkbox-group">
          <input type="checkbox" id="book_holidays" class="form-checkbox" v-model="form.book_holidays">
          <label for="book_holidays" class="form-checkbox-label">{{ $t('booking.allow_holidays', 'Povolit rezervace na svátky') }}</label>
        </div>
      </div>
      
      <div class="form-group">
        <div class="form-checkbox-group">
          <input type="checkbox" id="active" class="form-checkbox" v-model="form.active">
          <label for="active" class="form-checkbox-label">{{ $t('active', 'Aktivní') }}</label>
        </div>
      </div>

      <div class="mt-6 flex flex-col sm:flex-row justify-end gap-3">
        <button type="button" class="form-button-secondary w-full sm:w-auto" @click="$emit('cancel')">
          {{ $t('cancel', 'Zrušit') }}
        </button>
        <button type="submit" class="form-button w-full sm:w-auto">
          {{ isEdit ? $t('booking.update_page', 'Aktualizovat') : $t('booking.create_page', 'Vytvořit') }} {{ $t('booking.page', 'rezervační stránku') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
export default {
  props: {
    bookingLink: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      days: [
        this.$t('days.monday', 'Pondělí'),
        this.$t('days.tuesday', 'Úterý'),
        this.$t('days.wednesday', 'Středa'),
        this.$t('days.thursday', 'Čtvrtek'),
        this.$t('days.friday', 'Pátek'),
        this.$t('days.saturday', 'Sobota'),
        this.$t('days.sunday', 'Neděle')
      ],
      form: {
        name: '',
        description: '',
        duration: '',
        preferred_days: [],
        active: true, 
        is_remote: false,
        location: '',
        location_required: false,
        morning_limit: '',
        afternoon_limit: '',
        daily_limit: '',
        include_works_in_count: true,
        book_holidays: false
      }
    };
  },
  emits: ['submit', 'cancel'],
  methods: {
    submitForm() {
      this.$emit('submit', {...this.form});
    },
    setLocationTypeNormal() {
      this.form.is_remote = false;
      this.form.location_required = false;
      this.form.location = '';
    },
    setLocationTypeRequired() {
      this.form.is_remote = false;
      this.form.location_required = true;
    },
    setLocationTypeRemote() {
      this.form.is_remote = true;
      this.form.location_required = false;
      this.form.location = this.$t('booking.call_online', "Telefonát/Online");
    }
  },
  created() {
    if (this.isEdit && this.bookingLink) {
      this.form = {
        name: this.bookingLink.name || '',
        description: this.bookingLink.description || '',
        duration: this.bookingLink.duration || '',
        preferred_days: this.bookingLink.preferred_days || [],
        active: this.bookingLink.active !== undefined ? this.bookingLink.active : true,
        is_remote: this.bookingLink.is_remote || false,
        location: this.bookingLink.location || '',
        location_required: this.bookingLink.location_required || false,
        morning_limit: this.bookingLink.morning_limit || '',
        afternoon_limit: this.bookingLink.afternoon_limit || '',
        daily_limit: this.bookingLink.daily_limit || '',
        include_works_in_count: this.bookingLink.include_works_in_count || true,
        book_holidays: this.bookingLink.book_holidays || false
      };
    }
  }
};
</script>