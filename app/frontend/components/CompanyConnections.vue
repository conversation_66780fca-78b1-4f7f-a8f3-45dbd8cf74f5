<template>
  <div class="connections" :class="{ 'connections-embedded': embedded }">
    <div class="section-header" v-if="!embedded">
      <h2>{{ $t('company_connections.title', '<PERSON>zvání ke spolupr<PERSON>ci') }}</h2>
    </div>
    
    <div class="connections-list">
      <template v-if="pendingContracts.length">
        <div v-for="contract in pendingContracts" 
             :key="contract.id" 
             class="connections-item">
          <div class="connections-item-content">
            <div class="connections-item-main">
              <div class="connections-item-head">
                {{ $t('company_connections.new', 'Nové pozvání') }}
              </div>
              <div class="connections-item-head">
                  {{ new Date(contract.created_at).toLocaleDateString('cs-CZ') }}
              </div>
            </div>
            <div class="connections-item-main">
              <div>
                <h3 class="connections-item-title">
                  {{ contract.company.name }}
                </h3>
                <p v-if="contract.job_title" class="connections-item-position">
                  {{ contract.job_title }}
                </p>
              </div>
            </div>

            <div class="connections-item-actions">
              <button @click="acceptConnection(contract)" 
                      class="btn btn-primary">
                {{ $t('company_connections.connect', 'Připojit se k firmě') }}
                <Check class="btn-icon" :size="16"/>
              </button>
            </div>
          </div>
        </div>
      </template>
      
      <div v-else-if="!embedded" class="connections-empty">
        <p class="text-muted">{{ $t('company_connections.no_invitations', 'Momentálně nemáte žádná pozvání.') }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { Check  } from 'lucide-vue-next'
import axios from 'axios'

export default {
  name: 'CompanyConnections',
  
  components: {
    Check
  },

  props: {
    embedded: {
      type: Boolean,
      default: false
    },
    pendingContracts: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      pendingContracts: [],
      loading: false,
      error: null
    }
  },

  created() {
    this.fetchPendingContracts()
  },

  methods: {
    async fetchPendingContracts() {
      try {
        this.loading = true
        const response = await axios.get('/company_connections/fetch')
        this.pendingContracts = response.data
      } catch (err) {
        this.error = this.$t('company_connections.error', 'Nelze načíst pozvání')
        console.error('Error fetching connections:', err)
      } finally {
        this.loading = false
      }
    },

    async acceptConnection(contract) {
      if (!confirm(`${this.$t('company_connections.confirm_accept', 'Opravdu chcete přijmout pozvání od')} ${contract.company.name}?`)) {
        return
      }

      try {
        await axios.post(`/company_connections/${contract.id}/accept`, {
          authenticity_token: document.querySelector('[name="csrf-token"]').content
        })
        
        this.pendingContracts = this.pendingContracts.filter(c => c.id !== contract.id)
        
      } catch (err) {
        console.error('Error accepting connection:', err)
        alert(this.$t('company_connections.error_accepting', 'Nelze zpracovat pozvání. Zkuste to prosím později.'))
      }
    }
  }
}
</script>

<style>
.connections {
  max-width: 600px;
  margin: 0 auto;
}

.connections-list {
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-radius: 12px;
}

.connections-item {
  border-bottom: 1px solid #dee2e6;
}

.connections-item:last-child {
  border-bottom: none;
}

.connections-item-content {
  padding: 1rem;
}

.connections-item-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.connections-item-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.connections-item-head {
  display: flex;
  align-items: center;
  gap: 5px;
  padding-top: 4px;
  color: #666;
  font-size: 0.875rem;
}

.connections-item-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.connections-empty {
  padding: 1.5rem;
  text-align: center;
}

.btn-icon {
  margin-left: 0.5rem;
}

/* Embedded variant */
.connections-embedded .section-header {
  display: none;
}
</style>
