<template>
  <div class="daily-logs-box">
    <div class="daily-logs">
      <!-- Dynamic component with props and events -->
      <component
        v-if="isToday"
        :is="currentComponent"
        :has-record-for-today="dailyLogs.length > 0"
        :logs="dailyLogs"
        :buttonLabel="'Začínám'"
        :showDelete="true"
        @log-added="addLog"
        @switch-to-edit="switchToEdit"
        @switch-to-form="switchToForm"
        @log-updated="fetchLogs_"
        class="log-form-box"
      />

      <daily-log-list :logs="dailyLogs" @delete-log="handleDeleteLog" @log-updated="fetchLogs_"></daily-log-list>


    </div>
  </div>
</template>
  
<script>
import DailyLogForm from './DailyLogForm.vue';
import DailyLogList from './DailyLogList.vue';
import EditDailyLog from './EditDailyLog.vue';
import axios from 'axios';

export default {
  props: {
    selectedDate: {
      type: Date,
      required: true,
    }
  },
  components: { DailyLogForm, DailyLogList, EditDailyLog },
  data() {
    return {
      logs: [], 
      currentComponent: 'DailyLogForm', 
      dailyLogs: [],
    };
  },
  computed: {
    isToday() {
      const today = new Date();
      return (
        this.selectedDate.getDate() === today.getDate() &&
        this.selectedDate.getMonth() === today.getMonth() &&
        this.selectedDate.getFullYear() === today.getFullYear()
      );
    },
  }   ,
  watch: {
    selectedDate: 'fetchLogs_', // Fetch logs when the selected date changes
  },
  methods: {
    async fetchLogs_() {
      try {
        const response = await axios.get('/daily_logs/fetch', {
          params: { date: this.selectedDate.toISOString().split('T')[0] },
        });
        this.dailyLogs = response.data;
        this.determineInitialComponent();
      } catch (error) {
        if (error.response && error.response.status === 404) {
          this.errorMessage = 'Not found.';
        } else {
          this.errorMessage = 'An error occurred while fetching the log.';
        }
        console.error('Error fetching last daily log:', error);
        this.loading = false;
      }
    },
    determineInitialComponent() {
      if (this.dailyLogs.length > 0) {
        const lastLog = this.dailyLogs[0];
        if (lastLog.end_time) {
          this.currentComponent = 'DailyLogForm';
        } else {
          this.currentComponent = 'EditDailyLog';
        }
      } else {
        this.currentComponent = 'DailyLogForm';
      }
    },
    addLog(log) {
      this.dailyLogs.unshift(log);
      this.switchToEdit(); 
    },
    switchToEdit() {
      this.currentComponent = 'EditDailyLog';
    },
    switchToForm() {
      this.currentComponent = 'DailyLogForm';
    },
    handleDeleteLog(logId) {
      this.dailyLogs = this.dailyLogs.filter(log => log.id !== logId);
    },
  },
  mounted() {
    this.fetchLogs_();
  }
};
</script>
  
  <style scoped>
    .daily-logs-box {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      min-width: 400px;
    }
    .log-form-box {
      margin-bottom: 16px;
    }
    .daily-logs {
      display: flex;
      justify-content: center;
      align-items: normal;
      flex-direction: column;
      min-width: 400px;
    }
  </style>