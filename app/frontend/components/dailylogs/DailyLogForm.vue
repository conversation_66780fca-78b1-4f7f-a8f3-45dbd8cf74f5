<template>
  <div>
    <form @submit.prevent="submitForm">
      
      <button type="submit" class="btn submit cta">{{ buttonLabel }}</button>

    </form>


  </div>
</template>

<script>
import axios from 'axios';

export default {
  props: {
    buttonLabel: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      description: '' // Form input
    };
  },
  methods: {
    submitForm() {
      axios
        .post('/daily_logs', { daily_log: { description: this.description } })
        .then(response => {
          this.$emit('log-added', response.data); // Notify parent about the new log
          this.description = ''; // Clear the form
        })
        .catch(error => {
          console.error('Error creating log:', error);
        });
    }
  }
};
</script>

<style scoped>
  form {
    width: 100%;
    max-width: 600px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-self: center ;
  }

  input[type='text'] {
    width: 100%;
    padding: 15px; /* Increased padding to match the button height */
    border: 1px solid black;
    border-radius: 5px;
    background-color: #fff;
    color: #333;
    outline: none;
    transition: border-color 0.3s;
    margin-top: 15px;
  }

  input[type='text']:focus {
    border-color: #22C55E;
    box-shadow: 0 0 5px rgba(252, 103, 34, 0.3);
  }

  button[type='submit'] {
    background-color: #22C55E;
    color: #fff;
  }

  input::placeholder {
    text-align: right;
  }


</style>