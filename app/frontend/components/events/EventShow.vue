<template>
  <div class="detail-card event-group" v-if="!isEditing">
    <div class="flex justify-space align-center">
      <div class="event-header">
        <CalendarDays :size="16" class="event-icon" />
        <h3 class="event-title">{{ $t('event_type.' + event.event_type) }}</h3>
      </div>
      <div v-if="event.event_type === 'vacation'" class="vacation-status">
        <span v-if="event.status === 'pending'" class="badge" :class="event.status">{{ getStatusText(event.status) }}</span>
        <span v-else class="event-icon-status">
          <CheckCheck class="icon" size="16"/>
        </span>
      </div>
      <div class="dropdown">
        <button class="dropdown-toggle p-1 text-gray-500 hover:text-gray-700" @click="toggleDropdown">
          <MoreVertical :size="18" />
        </button>
        <div class="dropdown-menu" v-if="showDropdown">
          <a href="#" class="dropdown-item" @click.prevent="confirmDelete">{{ $t('delete', 'Smazat') }}</a>
        </div>
      </div>
    </div>
    
    <p v-if="event.place" class="location-display">
      <MapPin :size="20" />
      <span>{{ event.place }}</span>
    </p>

    
    <!-- 
    <div v-if="event.description" class="detail-content">
      <Clipboard :size="20" />
      {{ event.description }}
    </div> -->
    
    <!-- <div>
      <span class="detail-content">
        <Calendar :size="20" />
        {{ formatDate(event.start_time) }}
        <span v-if="formatDate(event.start_time) !== formatDate(event.end_time)">
          - {{ formatDate(event.end_time) }}
        </span>
      </span>
    </div>
    
    <div v-if="event.event_type === 'travel' && formatTime(event.start_time)" class="detail-content">
      <Clock :size="20" />
      {{ formatTime(event.start_time) }}
    </div> -->
  </div>
</template>

<script>
import { Calendar, Clipboard, MapPin, MoreVertical, HelpCircle, CheckCheck, Clock, CalendarDays } from 'lucide-vue-next';
import { getLocaleString } from '../../utils/dateFormatter';

export default {
  components: {
    MapPin, Clipboard, MoreVertical, Calendar, HelpCircle, CheckCheck, Clock, CalendarDays
  },
  props: {
    event: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      showDropdown: false, 
      isEditing: false
    };
  },
  methods: {
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
    },
    getStatusText(status) {
      switch(status) {
        case 'pending': return this.$t('statuses.pending_approval', 'Čeká na schválení');
        case 'approved': return this.$t('statuses.approved', 'Schváleno');
        case 'cancelled': return this.$t('statuses.cancelled', 'Zrušeno');
        default: return status;
      }
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      const locale = getLocaleString(this.$i18n.locale);
      return date.toLocaleDateString(locale);
    },
    formatTime(timeString) {
      const date = new Date(timeString);
      const hours = date.getHours();
      const minutes = date.getMinutes();
      if (hours === 0 && minutes === 0) {
        return '';
      }
      const locale = getLocaleString(this.$i18n.locale);
      return date.toLocaleTimeString(locale, {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    },
    confirmDelete() {
      this.$emit('delete-requested', this.event.id);
      this.showDropdown = false;
    }
  }
};
</script>

<style scoped>
.detail-card {
  width: 90%;
  min-width: 350px;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  background-color: white;
  /* border-radius: 0.5rem; */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 0.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 5px;
}

.event-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.event-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.vacation-status {
  margin-left: auto;
  padding-left: 0.5rem;
  display: flex;
  align-items: center;
}

.location-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.detail-content {
  display: flex;
  align-items: center;
  color: inherit;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.dropdown {
  position: relative;
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  color: inherit
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  z-index: 10;
  min-width: 120px;
}

.dropdown-item {
  display: block;
  padding: 0.5rem 1rem;
  text-decoration: none;
  color: #333;
}

.dropdown-item:hover {
  background: #f5f5f5;
}

.icon {
  width: 1rem;
  height: 1rem;
}
</style>