<template>
  <div class="calendar-wrapper">
    <div v-if="showDeleteConfirm" class="delete-modal">
      <div class="delete-modal-content">
        <h3 class="delete-modal-title">{{ $t('delete_record', 'Smazat záznam') }}</h3>
        <p>{{ $t('confirm_delete_record', 'Opravdu chcete smazat tento záznam?') }}</p>
        <div class="delete-modal-actions">
          <button @click="showDeleteConfirm = false" class="btn btn-outline">
            {{ $t('cancel', 'Zrušit') }}
          </button>
          <button @click="deleteEvent" class="btn btn-danger">
            {{ $t('delete', 'Smazat') }}
          </button>
        </div>
      </div>
    </div>
    <div class="calendar">
      <div class="header">
        <div class="nav-control">
          <button @click="prevMonth" class="text-link">
            <ChevronLeft :size="20" />
          </button>
          <h2>{{ capitalize(monthYear) }}</h2>
          <button @click="nextMonth" class="text-link">
            <ChevronRight :size="20" />
          </button>
        </div>
      </div>
      <div class="header">
        <div class="filter-controls mt-4">
          <button @click="toggleFilter" :class="['text-link-action']">
            {{ showOnlyEvents ? $t('show_all', 'Zobrazit vše') : $t('events.show_only_current', 'Jenom aktuální') }}
          </button>
          <button v-if="hasOlderDays" @click="toggleShowAllDays" :class="['text-link-action']">
            {{ showAllDays ? $t('events.hide_older_days', 'Skrýt starší dny') : $t('events.show_older_days', 'Zobrazit starší dny') }}
          </button>
          <LocalizedLink :to="'/holidays'" class="text-link-action" :use-anchor="true">
            {{ $t('holidays', 'Svátky') }}
          </LocalizedLink>
        </div>
      </div>

      <div class="day-list">
        <div v-for="day in filteredDays"
            :key="day.date"
            :class="['day-row', { 
              'weekend': isWeekend(day.date), 
              'holiday': isHoliday(day.date),
              'today': isToday(day.date),
            }]">
          <div class="day-header" :class="{ 
                    'weekend-header': isWeekend(day.date),
                    'holiday-header': isHoliday(day.date) 
                    }">
            <div class="day-number">
              <div :class="['day-date', { 'today': isToday(day.date) }]">
                {{ formatDay(day.date) }}
              </div>
              <div class="day-weekday">
                {{ formatWeekday(day.date) }}
              </div>
            </div>
          </div>

          <div class="day-content">
            <!-- Events section -->
            <div v-if="hasEvent(day.date)" class="">
              <event-show 
                v-for="event in getEvents(day.date)"
                :key="'event-' + event.id"
                :event="event"
                @delete-requested="openDeleteConfirm"
              />
            </div>
            
            <!-- Works section -->
            <work-show 
              v-for="work in getWorks(day.date)"
              :key="'work-' + work.id"
              :work="work"
              @updated="$emit('work-updated', $event)"
              @deleted="$emit('work-deleted', $event)"
            />
            
            <!-- Meetings section -->
            <meeting-card
              v-for="meeting in getMeetings(day.date)"
              :key="'meeting-' + meeting.id"
              :meeting="meeting"
              @updated="$emit('meeting-updated', $event)"
              @deleted="$emit('meeting-deleted', $event)"
            />
            
            <div class="action-buttons">
              <button @click="emitShowAddEventForm(day.date, $event)" class="add-btn" :title="$t('add_event', 'Přidat událost')">
                <CalendarPlus :size="14" />
              </button>
              <button @click="$emit('show-add-work-form', day.date)" class="add-btn" :title="$t('add_work', 'Přidat práci')">
                <FolderPlus :size="14" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { HelpCircle, CheckCheck, ChevronLeft, ChevronRight, Calendar, FolderPlus, CalendarPlus } from 'lucide-vue-next';
import WorkShow from '../works/WorkShow.vue';
import EventShow from '../events/EventShow.vue';
import MeetingCard from '../meetings/MeetingCard.vue';
import { sendFlashMessage } from '/utils/flashMessage';
import LocalizedLink from '../LocalizedLink.vue';
// import store from '../../store';

export default {
  components: {
    HelpCircle, 
    CheckCheck, 
    ChevronLeft, 
    ChevronRight, 
    Calendar, 
    FolderPlus,
    CalendarPlus, 
    WorkShow,
    EventShow,
    MeetingCard,
    LocalizedLink
  },
  props: {
    events: {
      type: Array,
      required: true
    },
    works: {
      type: Array,
      required: true
    },
    meetings: {
      type: Array,
      required: true
    },
    holidays: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      currentDate: new Date(),
      showOnlyEvents: false,
      showConfirmDialog: false,
      pendingDeleteId: null,
      showDeleteConfirm: false,
      deleteEventId: null,
      showAllDays: false,
      today: new Date(),
    };
  },
  created() {
    // Load saved states from localStorage
    const savedShowOnlyEvents = localStorage.getItem('showOnlyEvents');
    const savedShowAllDays = localStorage.getItem('showAllDays');
    
    if (savedShowOnlyEvents !== null) {
      this.showOnlyEvents = JSON.parse(savedShowOnlyEvents);
    }
    if (savedShowAllDays !== null) {
      this.showAllDays = JSON.parse(savedShowAllDays);
    }
  },
  computed: {
    monthYear() {
      return this.currentDate.toLocaleDateString(this.$i18n.locale, { month: 'long', year: 'numeric' });
    },
    daysInMonth() {
      const year = this.currentDate.getFullYear();
      const month = this.currentDate.getMonth();
      const days = new Date(year, month + 1, 0).getDate();

      const currentMonthDays = Array.from({ length: days }, (_, i) => {
        const date = new Date(year, month, i + 1);
        return { date };
      });
      
      // Switched off for now
      const nextMonthDays = Array.from({ length: 0 }, (_, i) => {
        const date = new Date(year, month + 1, i + 1);
        return { date };
      });

      return [...currentMonthDays, ...nextMonthDays];

    },
    filteredDays() {
      let days = this.daysInMonth;
      
      if (this.showOnlyEvents) {
        return days.filter(day => this.hasEvent(day.date) || this.hasWork(day.date) || this.hasMeeting(day.date));
      }
      
      if (!this.showAllDays) {
        const threeDaysAgo = new Date();
        threeDaysAgo.setDate(threeDaysAgo.getDate() - 1);
        
        days = days.filter(day => day.date >= threeDaysAgo);
      }
      
      return days;
    },
    hasOlderDays() {
      const threeDaysAgo = new Date();
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 1);
      
      return this.daysInMonth.some(day => day.date < threeDaysAgo);
    }
  },
  methods: {
    capitalize(str) {
      return str[0].toLocaleUpperCase() + str.slice(1)
    },
    
    getWorkStatusClass(status) {
      switch(status) {
        case 'scheduled': return 'work-scheduled';
        case 'in_progress': return 'work-progress';
        case 'completed': return 'work-completed';
        case 'cancelled': return 'work-cancelled';
        default: return '';
      }
    },
    
    openDeleteConfirm(eventId) {
      this.deleteEventId = eventId;
      this.showDeleteConfirm = true;
    },

    async deleteEvent() {
      const eventId = this.deleteEventId;

      try {
        await axios.delete(`/events/${eventId}`);
        this.$emit('event-deleted', eventId);
        sendFlashMessage(this.$t('record_deleted', 'Záznam byl smazán'), 'success');
      } catch (error) {
        console.error('Error deleting log:', error);
        sendFlashMessage(this.$t('cannot_delete_record', 'Nepodařilo se smazat záznam'), 'error');
      }

      this.showDeleteConfirm = false;
      this.deleteEventId = null;
    },
    
    toggleFilter() {
      this.showOnlyEvents = !this.showOnlyEvents;
      localStorage.setItem('showOnlyEvents', JSON.stringify(this.showOnlyEvents));
    },

    toggleShowAllDays() {
      this.showAllDays = !this.showAllDays;
      localStorage.setItem('showAllDays', JSON.stringify(this.showAllDays));
    },

    isWeekend(date) {
      const day = date.getDay();
      return day === 0 || day === 6; 
    },
    
    isHoliday(date) {
      const dateString = date.toLocaleDateString('en-CA');
      return this.holidays.includes(dateString);
    },
    
    hasEvent(date) {
      const dateString = date.toLocaleDateString('en-CA');
      return this.events.some(event => {
        const eventStartDateString = new Date(event.start_time).toLocaleDateString('en-CA');
        const eventEndDateString = new Date(event.end_time).toLocaleDateString('en-CA');
        return dateString >= eventStartDateString && dateString <= eventEndDateString;
      });
    },
    
    hasWork(date) {
      const dateString = date.toLocaleDateString('en-CA');
      return this.works.some(work => {
        const workStartDateString = new Date(work.scheduled_start_date).toLocaleDateString('en-CA');
        const workEndDateString = work.scheduled_end_date ? 
          new Date(work.scheduled_end_date).toLocaleDateString('en-CA') : 
          workStartDateString;
        
        return dateString >= workStartDateString && dateString <= workEndDateString;
      });
    },
    
    hasMeeting(date) {
      const dateString = date.toLocaleDateString('en-CA');
      return this.meetings.some(meeting => {
        const meetingDate = new Date(meeting.scheduled_date || meeting.confirmed_date).toLocaleDateString('en-CA');
        return dateString === meetingDate;
      });
    },
    
    getEvents(date) {
      const dateString = date.toLocaleDateString('en-CA');
      return this.events.filter(event => {
        const eventStartDateString = new Date(event.start_time).toLocaleDateString('en-CA');
        const eventEndDateString = new Date(event.end_time).toLocaleDateString('en-CA');
        return dateString >= eventStartDateString && dateString <= eventEndDateString;
      });
    },
    
    getWorks(date) {
      const dateString = date.toLocaleDateString('en-CA');
      return this.works.filter(work => {
        const workStartDateString = new Date(work.scheduled_start_date).toLocaleDateString('en-CA');
        const workEndDateString = work.scheduled_end_date ? 
          new Date(work.scheduled_end_date).toLocaleDateString('en-CA') : 
          workStartDateString;
        
        return dateString >= workStartDateString && dateString <= workEndDateString;
      });
    },
    
    getMeetings(date) {
      const dateString = date.toLocaleDateString('en-CA');
      return this.meetings.filter(meeting => {
        const meetingDate = new Date(meeting.scheduled_date || meeting.confirmed_date).toLocaleDateString('en-CA');
        return dateString === meetingDate;
      });
    },
    
    prevMonth() {
      this.currentDate.setMonth(this.currentDate.getMonth() - 1);
      this.currentDate = new Date(this.currentDate); 
      this.$emit('month-changed', this.currentDate);
    },
    
    nextMonth() {
      this.currentDate.setMonth(this.currentDate.getMonth() + 1);
      this.currentDate = new Date(this.currentDate); 
      this.$emit('month-changed', this.currentDate);
    },
    
    formatDay(date) {
      return date.toLocaleDateString('cs-CZ', {
        day: '2-digit',
      }).replace('.', '');
    },
    
    formatWeekday(date) {
      return date.toLocaleDateString('cs-CZ', {
        weekday: 'short',
      });
    },
    
    formatTime(startTime) {
      const date = new Date(startTime);
      const hours = date.getHours();
      const minutes = date.getMinutes();
      if (hours === 0 && minutes === 0) {
        return '';
      }
      return new Date(startTime).toLocaleTimeString('cs-CZ', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    },
    emitShowAddEventForm(date, event) {
      this.$emit('show-add-event-form', date, event);
    },
    isToday(date) {
      const today = new Date();
      return date.getDate() === today.getDate() && 
            date.getMonth() === today.getMonth() && 
            date.getFullYear() === today.getFullYear();
    },
  },
// provide: {
  //   store
  // },
};
</script>

<style scoped>
.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0rem;
  &.stick {
    top: 0px;
    position: sticky;
    z-index: 10;
  }
}

.calendar-wrapper {
  position: relative;
}

.calendar {
  margin: 0 auto;
}

.nav-control {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.today {
  /* color: #3b82f6;  */
  background-color: #dbeafe;
  /* font-weight: 700; */
}

.filter-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.day-list {
  margin-top: 1rem;
}

.day-row {
  display: flex;
  flex-direction: row;
  gap: 10px;
  padding: 0.25rem;
  border-top: 1px solid #ababab;
}
.weekend {
  background: none;
}

.weekend-header {
  color: rgb(197, 34, 34);
}

.holiday-header {
  color: rgb(197, 34, 34);
}

.day-header {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.day-date {
  font-size: 1.5rem;
  font-weight: 600;
}
.day-weekday {
  font-size: 0.875rem;
  font-weight: 500;
}

.day-content {
  display: flex;
  width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.card {
  width: 90%;
  min-width:350px;
}

.delete-btn {
  padding: 0;
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  line-height: 1;
  cursor: pointer;
}

.delete-btn:hover {
  color: #dc3545;
}

.action-buttons {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.add-btn {
  align-self: flex-start;
  padding: 0.25rem 0.75rem;
  background: none;
  border: 1px dashed #dee2e6;
  color: #494949;
  border-radius: 9999px;
  cursor: pointer;
  font-size: 1.25rem;
  line-height: 1;
}

.add-btn:hover {
  border-color: #22C55E;
  color: #22C55E;
}

.add-work-btn {
  align-self: flex-start;
  padding: 0.25rem 0.75rem;
  background: none;
  border: 1px dashed #dee2e6;
  color: #494949;
  border-radius: 9999px;
  cursor: pointer;
  line-height: 1;
  display: flex;
  align-items: center;
}

.add-work-btn:hover {
  border-color: #4a6fa5;
  color: #4a6fa5;
}

.event-icon {
  line-height: 1;
}

.icon {
  width: 1rem;
  height: 1rem;
}

.delete-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 900;
}

.delete-modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
}

.delete-modal-title {
  margin: 0 0 16px 0;
  font-size: 1.2rem;
}

.delete-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .day-date {
    font-size: 1.2rem;
  }
  .day-row {
    padding: 0.25rem;
    gap: 8px;
  }

  .event-group {
    gap: 0.25rem;
  }
  
  .event-tag {
    display: inline-flex;
    width: auto;
  }
}

</style>