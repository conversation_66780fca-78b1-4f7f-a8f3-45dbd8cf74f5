<template>
  <a v-if="useAnchor" :href="localizedPath" @click="handleClick" v-bind="$attrs" data-localized-link-anchor><slot /></a>
  <!-- <router-link v-else :to="localizedPath" v-bind="$attrs"><slot /></router-link> -->
</template>

<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  to: {
    type: String,
    required: true
  },
  useAnchor: {
    type: Boolean,
    default: false
  }
});

const i18n = useI18n();

const handleClick = (event) => {
  if (props.useAnchor && !props.to.startsWith('http') && !props.to.startsWith('#')) {
    event.preventDefault();
    window.location.href = localizedPath.value;
  }
};

const localizedPath = computed(() => {
  // Skip localization for external URLs, anchors, or already localized paths
  if (props.to.startsWith('http') || 
      props.to.startsWith('#') || 
      props.to.startsWith('/cs/') || 
      props.to.startsWith('/sk/')) {
    return props.to;
  }
  
  // Add locale prefix to the path and remove any query parameters
  let path = props.to.startsWith('/') ? props.to : `/${props.to}`;
  
  // Remove any existing query parameters
  path = path.split('?')[0];
  
  const locale = i18n.locale.value || 'cs';
  return `/${locale}${path}`;
});
</script>
