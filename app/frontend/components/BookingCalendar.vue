<template>
  <div class="language-selector">
    <LanguageSelector :current-locale="currentLocale" :use-path-prefix="false" />
  </div>
  <div class="booking-container">

    <div class="booking-header">
      <div class="header-content">
        <h2 class="booking-title">{{ calendarData?.company_name }}</h2>
        <p class="booking-message">
          <strong></strong> {{ $t('online_bookings', 'Online rezervace') }}
        </p>
        <h3 class="booking-service-title">{{ calendarData?.name }}</h3>
        <div class="service-meta">
          <div v-if="calendarData?.duration" class="meta-item">
            <Clock size="16" class="icon" />
            <span>{{ calendarData.duration }} min</span>
          </div>
          <div v-if="calendarData?.location_required" class="meta-item">
            <MapPin size="16" class="icon" />
            <span>{{ $t('booking.provide_location_on_booking', 'Uveďte místo při rezervaci') }}</span>
          </div>
          <div v-else-if="calendarData?.location" class="meta-item">
            <MapPin size="16" class="icon" />
            <span>{{ calendarData.location }}</span>
          </div>
        </div>
        <p v-if="calendarData?.description" class="booking-service-description">{{ calendarData?.description }}</p>
      </div>
    </div>
    
    <div v-if="!existingBooking || existingBooking.status !== 'cancelled'" class="calendar-section">
      <div class="calendar-header">
        <button class="nav-button" @click="prevMonth">
          <ChevronLeft size="20" />
        </button>
        <h3>{{ monthName }}</h3>
        <button class="nav-button" @click="nextMonth">
          <ChevronRight size="20" />
        </button>
      </div>
      
      <div class="weekdays">
        <div v-for="(day, index) in weekdays" :key="day" class="weekday">{{ $t('weekdays_short[' + index + ']', day) }}</div>
      </div>
      
      <div class="calendar-grid">
        <div 
          v-for="n in firstDayOffset" 
          :key="`empty-${n}`" 
          class="day empty"
        ></div>
        
        <div 
          v-for="day in calendarDays" 
          :key="day.date"
          class="day"
          :class="{
            'today': day.is_today,
            'weekend': day.is_weekend,
            'unavailable': day.availability === 'unavailable',
            'available': day.availability === 'available',
            'limited': day.availability === 'limited',
            'busy': day.availability === 'busy',
            'daily-limit-reached': day.periods && 
                           (!day.periods.morning.available && !day.periods.afternoon.available),
            'selected': selectedDay && day.date === selectedDay.date
          }"
          @click="selectDay(day)"
        >
          <div class="day-number">{{ day.day }}</div>
          <div v-if="day.is_holiday" class="limit-indicator">{{ $t('holiday', 'Svátek') }}</div>
          <div v-if="day.availability !== 'unavailable'" class="availability-dot"></div>
          <div v-if="day.periods && !day.is_holiday && (!day.periods.morning.available && !day.periods.afternoon.available)" 
                    class="limit-indicator">{{ $t('full', 'Plno') }}</div>
        </div>
      </div>
    </div>
  </div>

  <div class="booking-container">  
    <div v-if="selectedDay && !showBookingForm" class="time-selection" ref="timeSelection">
      <h3>{{ formatDate(selectedDay.date).charAt(0).toUpperCase() + formatDate(selectedDay.date).slice(1) }}</h3>
      <p>{{ $t('booking.select_time_period_prompt', 'Vyberte časové období:') }}</p>
      
      <div class="time-options">
        <button 
          class="time-option" 
          :class="{ 
            'busy': selectedDay.periods?.morning?.count > 0,
            'limit-reached': selectedDay.periods?.morning && !selectedDay.periods.morning.available 
          }"
          @click="selectPeriod('morning')"
          :disabled="selectedDay.periods?.morning && !selectedDay.periods.morning.available"
        >
          <div class="time-label">{{ $t('morning', 'Dopoledne') }}</div>
          <div class="time-range">8:00 - 11:59</div>
          <span v-if="selectedDay.periods?.morning?.count > 0" class="booking-count">
            {{ $t('booking.booked_times_count', { count: selectedDay.periods.morning.count }, { default: `Rezervováno ${selectedDay.periods.morning.count}-krát` }) }}
          </span>
          <span v-if="selectedDay.periods?.morning && !selectedDay.periods.morning.available" class="limit-message">
            {{ $t('booking.booking_limit_reached', 'Dosažen limit rezervací') }}
          </span>
        </button>
        
        <button 
          class="time-option" 
          :class="{ 
            'busy': selectedDay.periods?.afternoon?.count > 0,
            'limit-reached': selectedDay.periods?.afternoon && !selectedDay.periods.afternoon.available 
          }"
          @click="selectPeriod('afternoon')"
          :disabled="selectedDay.periods?.afternoon && !selectedDay.periods.afternoon.available"
        >
          <div class="time-label">{{ $t('afternoon', 'Odpoledne') }}</div>
          <div class="time-range">12:00 - 17:00</div>
          <span v-if="selectedDay.periods?.afternoon?.count > 0" class="booking-count">
            {{ $t('booking.booked_times_count', { count: selectedDay.periods.afternoon.count }, { default: `Rezervováno ${selectedDay.periods.afternoon.count}-krát` }) }}

          </span>
          <span v-if="selectedDay.periods?.afternoon && !selectedDay.periods.afternoon.available" class="limit-message">
            {{ $t('booking.booking_limit_reached', 'Dosažen limit rezervací') }}
          </span>
        </button>
        
      </div>
    </div>
    
    <div v-if="showBookingForm" class="booking-form" ref="bookingForm">
      <div class="form-header ">
        <h3>
          {{ $t('booking.booking_for_date_period', 'Rezervace na ') }} 
          {{ formatDate(selectedDay.date) }} {{ translatePeriod(selectedPeriod) }}
        </h3>
        <div v-if="existingBooking?.status === 'cancelled'" class="booking-note cancelled">
          <p>{{ $t('booking.booking_is_cancelled', 'Rezervace je zrušená') }}</p>
        </div>
      </div>
      
      <form @submit.prevent="submitForm">
        <div class="form-group">
          <input 
            type="text" 
            id="client_name" 
            v-model="bookingForm.client_name" 
            :placeholder="$t('booking.your_full_name_placeholder', 'Vaše jméno a příjmení')" 
            required 
            :disabled="isManageMode || existingBooking?.status === 'cancelled'" 
          />
          <div v-if="isManageMode" class="field-note">{{ $t('booking.name_cannot_be_edited', 'Jméno nelze upravit.') }}</div>
        </div>
        
        <div class="form-group" v-if="calendarData?.location_required">
          <input 
            type="text" 
            id="location" 
            v-model="bookingForm.location" 
            :placeholder="$t('location', 'Místo')" 
            required
            :disabled="isManageMode || existingBooking?.status === 'cancelled'"
          >
          <div v-if="isManageMode" class="field-note">{{ $t('booking.location_cannot_be_edited', 'Místo nelze upravit.') }}</div>
        </div>

        <div class="form-group">
          <input 
            type="email" 
            id="client_email" 
            v-model="bookingForm.client_email" 
            :placeholder="$t('booking.your_email_placeholder', 'email')"
            :disabled="isManageMode || existingBooking?.status === 'cancelled'"
          >
          <div v-if="isManageMode" class="field-note">{{ $t('booking.email_cannot_be_edited', 'Email nelze upravit.') }}</div>
        </div>

       
        <div v-if="existingBooking?.status !== 'cancelled'" class="booking-note warning">
          <TriangleAlert size="16" />
          <p>
            {{ $t('booking.if_no_email_check_phone', 'Jestli e-mail nezadáte, zkontrolujte si telefonní číslo, aby Vás poskytovatel mohl kontaktovat telefonicky.') }}
          </p>
        </div>
        
        <div class="form-group">
          <input 
            type="tel" 
            id="client_phone" 
            v-model="bookingForm.client_phone" 
            :placeholder="$t('booking.phone_placeholder_cz', 'Telefon +420 XXX XXX XXX')" 
            :disabled="isManageMode || existingBooking?.status === 'cancelled'"
          />
          <div v-if="isManageMode" class="field-note">{{ $t('booking.phone_cannot_be_edited', 'Telefon nelze upravit.') }}</div>
        </div>

        <!-- Specific Time Selection - Replaced with TimePicker component -->
        <div class="form-group">
          <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('booking.preferred_time_optional_label', 'Preferovaný čas (nepovinné):') }}</label>
          <TimePicker 
              v-model="specificTime" 
              :disabled="existingBooking?.status === 'cancelled'"
          />
        </div>

        <div v-if="existingBooking?.status !== 'cancelled'" class="booking-note info">
          <Info size="16" />
          <p>
            {{ $t('booking.after_submit_email_info', 'Po odeslání rezervace vám zašleme informaci na e-mail. Termín a čas rezervace Vám potvrdí poskytovatel služby e-mailem nebo telefonicky.') }}
          </p>
        </div>
        
        <div class="form-group">
          <label for="message">{{ $t('booking.booking_note_optional_label', 'Poznámka k rezervaci (nepovinné)') }}</label>
          <textarea 
            id="message" 
            v-model="bookingForm.message"
            rows="3"
            :placeholder="$t('booking.booking_note_placeholder', 'Uveďte prosím požadované detaily k rezervaci nebo vlastní poznámky.')"
            :disabled="isManageMode || existingBooking?.status === 'cancelled'"
          ></textarea>
          <div v-if="isManageMode" class="field-note">{{ $t('booking.note_cannot_be_edited', 'Poznámku nelze upravit.') }}</div>
        </div>

        <div v-if="existingBooking?.status === 'cancelled'">
          <div class="booking-note cancelled">
            {{ $t('booking.cancelled_booking_cannot_be_edited', 'Zrušenou rezervaci nelze upravovat.') }}
          </div>
        </div>

        <div v-else-if="isManageMode" class="booking-note info">
          {{ $t('booking.only_date_time_can_be_edited', 'Lze upravit pouze datum a čas rezervace.') }}
        </div>
        

        <div class="form-actions">
          <button 
            type="button" 
            class="btn-secondary"
            @click="cancelBooking"
          >
            {{ $t('back', 'Zpět') }}
          </button>
          <a v-if="existingBooking?.status === 'cancelled'" :href="`/r/${companySlug}/${bookingLinkId}`" class="btn-primary">
              {{ $t('booking.new_reservation', 'Nová rezervace') }}
            </a>
          <button type="submit" class="btn-primary" :disabled="existingBooking?.status === 'cancelled'">
            {{ $t('booking.submit_booking', 'Odeslat rezervaci') }}
          </button>
          
          <button 
              v-if="isManageMode && existingBooking?.status !== 'cancelled'" 
              type="button" 
              class="btn-danger"
              @click="showCancelModal = true"
            >
            {{ $t('booking.cancel_booking', 'Zrušit rezervaci') }}
          </button>
        </div>
      </form>
    </div>
    
    <!-- Cancel Confirmation Modal -->
    <div v-if="showCancelModal" class="modal-overlay">
      <div class="modal-content">
        <h3>{{ $t('booking.confirm_cancel_booking_q', 'Opravdu chcete zrušit rezervaci?') }}</h3>
        <p>{{ $t('action_irreversible', 'Tato akce je nevratná.') }}</p>
        <div class="modal-actions">
          <button class="btn-secondary" @click="showCancelModal = false">{{ $t('no_keep_it', 'Ne, zachovat') }}</button>
          <button class="btn-danger" @click="cancelExistingBooking">{{ $t('booking.yes_cancel_booking', 'Ano, zrušit rezervaci') }}</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { Calendar, CalendarPlus, ChevronLeft, ChevronRight, Clock, MapPin, Info, ExternalLinkIcon, TriangleAlert } from 'lucide-vue-next';
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import dayjs from 'dayjs';
import TimePicker from './TimePicker.vue';
import LanguageSelector from './LanguageSelector.vue';

export default {
  props: {
    bookingLinkId: {
      type: String,
      required: true
    },
    companySlug: {
      type: String,
      required: true
    },
    bookingToken: {
      type: String,
      default: null
    }
  },
  components: {
    VueDatePicker,
    ChevronLeft,
    ChevronRight,
    Calendar,
    CalendarPlus,
    Clock,
    MapPin,
    Info,
    ExternalLinkIcon,
    TimePicker,
    TriangleAlert,
    LanguageSelector
  },
  data() {
    return {
      currentMonth: new Date(),
      calendarData: null,
      selectedDay: null,
      selectedPeriod: null,
      showBookingForm: false,
      bookingForm: {
        client_name: '',
        client_email: '',
        client_phone: '',
        message: '',
        location: '',
        duration: null
      },
      specificTime: { hour: null, minute: null },
      weekdays: ['Po', 'Út', 'St', 'Čt', 'Pá', 'So', 'Ne'],
      existingBooking: null,
      showCancelModal: false,
      isLoading: false,
      errorMessage: null,
      currentLocale: this.getCurrentLocale()
    };
  },
  
  computed: {
    monthName() {
      if (!this.calendarData || !this.calendarData.month) return '';
      const date = new Date(this.calendarData.month.start_date);
      return date.toLocaleDateString(this.$i18n.locale, {
        month: 'long',
        year: 'numeric'
      }).replace(/(^|\s)\S/g, l => l.toUpperCase());
    },
    
    calendarDays() {
      return this.calendarData ? this.calendarData.days : [];
    },
    
    firstDayOffset() {
      if (!this.calendarData || this.calendarData.days.length === 0) return 0;
      const firstDay = new Date(this.calendarData.days[0].date);
      return (firstDay.getDay() + 6) % 7;
    },
    
    isManageMode() {
      return !!this.bookingToken;
    }
  },
  
  mounted() {
    if (this.isManageMode) {
      console.log('Fetching existing booking bookingToken:', this.bookingToken);
      this.fetchBookingByToken();
    } else {
      console.log('Fetching calendar data for bookingLink:', this.bookingLinkId);
      this.fetchCalendarData();
    }
  },
  
  methods: {
    async fetchBookingByToken() {
      this.isLoading = true;
      try {
        console.log('Fetching booking:', this.bookingToken);
        const response = await axios.get(`/r/${this.companySlug}/bookings/${this.bookingToken}`);
        this.existingBooking = response.data.booking;
        console.log('Existing booking:', this.existingBooking);

        
        // Set the current month to the booking's month
        if (this.existingBooking.preferred_date) {
          this.currentMonth = new Date(this.existingBooking.preferred_date);
        }
        
        // Fetch calendar data for this month
        await this.fetchCalendarData();
        
        // Pre-populate form with existing booking data
        this.populateFormWithBookingData();
        
        // Set selected day and period
        this.selectDayFromExistingBooking();
        
      } catch (error) {
        console.error('Error fetching booking:', error);
        this.errorMessage = this.$t('booking.error_loading_booking_token_invalid', 'Nepodařilo se načíst rezervaci. Token je neplatný nebo vypršel.');
      } finally {
        this.isLoading = false;
      }
    },
    
    populateFormWithBookingData() {
      if (!this.existingBooking) return;
      
      // Changed specific time handling due to bad picker display
      // Decomposing and composing specific time to minutes and hours
      this.bookingForm = {
        client_name: this.existingBooking.client_name || '',
        client_email: this.existingBooking.client_email || '',
        client_phone: this.existingBooking.client_phone || '',
        message: this.existingBooking.message || '',
        // specific_time: this.existingBooking.specific_time ? new Date(this.existingBooking.specific_time).getTime() : null,
        location: this.existingBooking.location || '',
        duration: this.existingBooking.duration || this.calendarData?.duration || null
      };

      // Setting the separate hour/minute state
      if (this.existingBooking.specific_time && typeof this.existingBooking.specific_time === 'string') {
        // Parse the full timestamp string received from Rails
        // dayjs() handles various ISO-like formats automatically
        const parsedDateTime = dayjs(this.existingBooking.specific_time);

        if (parsedDateTime.isValid()) {
          // Extract hour and minute from the parsed datetime object
          this.specificTime.hour = parsedDateTime.hour(); 
          this.specificTime.minute = parsedDateTime.minute();
        } else {
          console.error("Failed to parse existing specific_time:", this.existingBooking.specific_time);
        }
      }

    },
    
    selectDayFromExistingBooking() {
      if (!this.existingBooking || !this.existingBooking.preferred_date || !this.calendarDays) return;
      
      const preferredDate = this.existingBooking.preferred_date;
      const day = this.calendarDays.find(d => d.date === preferredDate);
      
      if (day) {
        this.selectedDay = day;
        this.selectedPeriod = this.existingBooking.preferred_period;
        this.showBookingForm = true;
      }
    },
    
    async fetchCalendarData() {
      try {
        const monthStr = `${this.currentMonth.getFullYear()}-${String(this.currentMonth.getMonth() + 1).padStart(2, '0')}-01`;
        const response = await axios.get(`/r/${this.companySlug}/${this.bookingLinkId}/calendar?month=${monthStr}`);
        this.calendarData = response.data;
        console.log('Calendar data:', this.calendarData);
      } catch (error) {
        console.error('Error fetching calendar data:', error);
      }
    },
    
    prevMonth() {
      const newDate = new Date(this.currentMonth);
      newDate.setMonth(newDate.getMonth() - 1);
      this.currentMonth = newDate;
      this.fetchCalendarData();
      if (!this.isManageMode) {
        this.resetSelection();
      }
    },
    
    nextMonth() {
      const newDate = new Date(this.currentMonth);
      newDate.setMonth(newDate.getMonth() + 1);
      this.currentMonth = newDate;
      this.fetchCalendarData();
      if (!this.isManageMode) {
        this.resetSelection();
      }
    },
    
    resetSelection() {
      this.selectedDay = null;
      this.selectedPeriod = null;
      this.showBookingForm = false;
    },
    
    selectDay(day) {
      if (day.availability !== 'unavailable') {
        const bothPeriodsUnavailable = 
          day.periods && 
          (!day.periods.morning.available && !day.periods.afternoon.available);
          
        if (bothPeriodsUnavailable) {
          return;
        }
        
        this.selectedDay = day;
        this.selectedPeriod = null;
        this.showBookingForm = false;

        // Add scroll behavior
        this.$nextTick(() => {
          if (this.$refs.timeSelection) {
            this.$refs.timeSelection.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        });
      }
    },
    
    selectPeriod(period) {
      if (this.selectedDay.periods && 
          period === 'morning' && 
          !this.selectedDay.periods.morning.available) {
        return; 
      }
      
      if (this.selectedDay.periods && 
          period === 'afternoon' && 
          !this.selectedDay.periods.afternoon.available) {
        return; 
      }
      
      this.selectedPeriod = period;
      this.showBookingForm = true;

      // Add scroll behavior
      this.$nextTick(() => {
        if (this.$refs.bookingForm) {
          this.$refs.bookingForm.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      });
    },
    
    cancelBooking() {
      if (this.isManageMode) {
        this.showBookingForm = false;
      } else {
        this.showBookingForm = false;
        this.bookingForm = {
          client_name: '',
          client_email: '',
          client_phone: '',
          message: '',
          specific_time: null,
          location: '',
          duration: null
        };
      }
    },
    
    async submitForm() {
      if (!this.validateForm()) {
        return;
      }
      
      try {
        // Compose the full specific_time in datetime format by combining date and time
        const preferredDateStr = this.selectedDay.date;
        // Get selected hour and minute from component state
        const hour = this.specificTime.hour;
        const minute = this.specificTime.minute;

        let specificTimeString = null;
        // Assumes hour is 0-23, minute is 0-59. Add validation if needed.
        if (preferredDateStr && hour !== null && minute !== null) {
          // Format hour and minute with leading zeros if necessary
          const formattedHour = String(hour).padStart(2, '0');
          const formattedMinute = String(minute).padStart(2, '0');
          // Combine into the required timezone-naive format YYYY-MM-DDTHH:MM:00
          specificTimeString = `${preferredDateStr}T${formattedHour}:${formattedMinute}:00`;
        } else if (hour !== null || minute !== null) {
          console.warn("Incomplete specific time selection.");
          specificTimeString = null;
        }

        const payload = {
          ...this.bookingForm,
          preferred_date: this.selectedDay.date,
          preferred_period: this.selectedPeriod,
          specific_time: specificTimeString
        };

        // if (payload.specific_time) {
        //   const date = new Date(payload.specific_time);
        //   payload.specific_time = date.toISOString();
        // }
        
        if (this.isManageMode) {
          await this.updateExistingBooking(payload);
        } else {
          await this.createNewBooking(payload);
        }
      } catch (error) {
        console.error('Error submitting booking:', error);
      }
    },
    
    async createNewBooking(payload) {
      const response = await axios.post(`/r/${this.companySlug}/${this.bookingLinkId}/bookings`, { booking: payload });
      
      if (response.data.success) {
        this.cancelBooking();
        this.selectedDay = null;
        this.fetchCalendarData();
        // Redirect or show success message
      }
    },
    
    async updateExistingBooking(payload) {
      const updatePayload = {
        preferred_date: payload.preferred_date,
        preferred_period: payload.preferred_period,
        specific_time: payload.specific_time
      };
      const response = await axios.patch(`/r/${this.companySlug}/bookings/${this.bookingToken}`, { booking: updatePayload });

      if (response.data.success) {
        this.existingBooking = response.data.booking;
        this.fetchCalendarData();
        // Show success message
      }
    },
    
    async cancelExistingBooking() {
      try {
        const response = await axios.delete(`/r/${this.companySlug}/bookings/${this.bookingToken}`);
        
        if (response.data.success) {
          this.showCancelModal = false;
          // Redirect to confirmation page or show success message
          // window.location.href = `/r/${this.companySlug}/booking-cancelled`;
        }
      } catch (error) {
        console.error('Error cancelling booking:', error);
      }
    },
    
    formatDate(dateStr) {
      const date = new Date(dateStr);
      return date.toLocaleDateString(this.$i18n.locale, { 
        weekday: 'long', 
        month: 'long', 
        day: 'numeric' 
      });
    },
    
    translatePeriod(period) {
      const translations = {
        'morning': this.$t('morning', 'dopoledne'),
        'afternoon': this.$t('afternoon', 'odpoledne')
      };
      return translations[period] || period;
    },
    
    validateForm() {
      if (!this.bookingForm.client_email && !this.bookingForm.client_phone) {
        alert(this.$t('booking.error_fill_email_or_phone', 'Prosím vyplňte e-mail nebo telefon.'));
        return false;
      }
      return true;
    },

    selectTime(time) {
      if (time === null) {
        this.specificTime = { hour: null, minute: null };
      } else {
        this.specificTime.hour = time;
        this.specificTime.minute = 0;
      }
    },
    
    getCurrentLocale() {
      const availableLocales = ['cs', 'sk'];
      
      // Check locale from cookie
      const cookies = document.cookie.split(';');
      for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'locale' && availableLocales.includes(value)) {
          return value;
        }
      }
      
      // Fallback to default locale
      return 'cs';
    }
  }
};
</script>

<style scoped>
.language-selector {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-top: -10px;
  @media (min-width: 768px) {
    margin-top: -30px;
  }
}

.booking-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 1rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 5px;
  font-family: "Inter" !important;
  color: #333;
  &.top {
    margin-top: -30px;
  }
}

.booking-container > * {
  flex: 1 1 350px;
  min-width: 350px;
  max-width: 550px;
  width: 100%;
}

/* Calendar section styling */
.calendar-section {
  border: 1px solid #005FA5;
  border-radius: 12px;
  overflow: hidden;
  flex: 1;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: linear-gradient(135deg, #0078C8, #004A87);
  border-bottom: 1px solid #005FA5;
  color:white;
}

.calendar-header h3 {
  font-size: 16px;
  font-weight: 500;
}

.nav-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-weight: bold;
  padding: 8px 0;
  font-size: 14px;
  border-bottom: 1px solid #e0e0e0;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  padding: 8px;
}

.day {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 14px;
  cursor: pointer;
}

.day-number {
  z-index: 1;
}

.day.empty {
  cursor: default;
}

.day.weekend {
  color: #999;
}

.day.today {
  font-weight: bold;
  color: #007BFF;
}

.day.unavailable {
  color: #999;
  cursor: not-allowed;
  background-color: #eee;
}

.day.available {
  font-weight: bold;
  background-color: #B7EECB;
}

.day.limited {
  font-weight: bold;
  background: #fff7e6;
}

.day.busy {
  background: #ffe6e6;
}

.availability-indicator {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.day.available .availability-dot {
  background: #22C55E;
}

.day.limited .availability-dot {
  background: #ff9800;
}

.day.busy .availability-dot {
  background: #f44336;
}

.availability-dot {
  position: absolute;
  bottom: 4px;
  width: 6px;
  height: 6px;
  background-color: #22C55E;
  border-radius: 50%;
}

/* Time selection styling */
.time-selection {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #005FA5;
}

.time-selection h3 {
  font-size: 18px;
  margin-bottom: 12px;
  color: #333;
}

.time-selection p {
  margin-bottom: 16px;
  color: #555;
}

.time-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.time-option {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #ddd;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
}

.time-option:hover {
  border-color: #007bff;
  background-color: #f0f7ff;
}

.time-label {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.time-range {
  font-size: 14px;
  color: #666;
}

.booking-count {
  font-size: 13px;
  color: #ff6b6b;
  margin-top: 4px;
}

/* Booking form styling */
.booking-form {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #005FA5;
}

.form-header {
  margin-bottom: 20px;
}

.form-header h3 {
  font-size: 18px;
  margin-bottom: 12px;
  color: #333;
}

.booking-note {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-top: 12px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
  
  p {
    margin: 0;
    line-height: 1.25;
  }
  
  &.cancelled {
    background-color: #f8d7da;
    color: #d32f2f;
  }
  &.info {
    background-color: #e2f0ff;
    color: #004085;
  }
  &.warning {
    background-color: #fef9c3;
    color: #ca8a04; 
  }
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 15px;
  font-weight: 500;
  color: #444;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #007bff;
  outline: none;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  margin-top: 24px;
}

.btn-secondary {
  padding: 12px 20px;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 8px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  color: #555;
  transition: all 0.2s ease;
}

.btn-primary {
  padding: 12px 20px;
  border: none;
  background-color: #007bff;
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.2s ease;
  &:disabled {
    background-color: #ddd;
    color: #666
  }
}

a.btn-primary {
  text-decoration: none;
}

.btn-danger {
  padding: 12px 20px;
  border: none;
  background-color: #d32f2f;
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.field-note {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  font-style: italic;
}

.alert {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.alert-warning {
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  color: #856404;
}

.alert-info {
  background-color: #e2f0ff;
  border: 1px solid #b8daff;
  color: #004085;
}



/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 900;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 12px;
  min-width: 300px;
  max-width: 90%;
}

.modal-content h3 {
  font-size: 18px;
  margin-bottom: 12px;
}

.modal-content p {
  margin-bottom: 20px;
  color: #666;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
.booking-header {
  background: linear-gradient(135deg, #004A87, #0078C8);
  color: #ffffff;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.header-content {
  max-width: 650px;
  margin: 0 auto;
}

.booking-title {
  font-size: 2.2rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #f8f9fa;
}

.booking-message {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  color: #e9ecef;
}

.booking-service-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #ffffff;
}

.service-meta {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #98cbff;
}

.booking-service-description {
  font-size: 1rem;
  font-style: italic;
  margin-bottom: 0.5rem;
  color: #dee2e6;
}

.booking-header .icon {
  color: #98cbff;
}

.limit-indicator {
  position: absolute;
  top: 2px;
  font-size: 10px;
  color: #ff6b6b;
  font-weight: bold;
}

.time-option.limit-reached {
  opacity: 0.6;
  border-color: #ffcdd2;
  cursor: not-allowed;
}

.time-option:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.limit-message {
  font-size: 13px;
  color: #ff6b6b;
  font-weight: bold;
  margin-top: 4px;
}

.holiday-text {
  position: absolute;
  top: 2px;
  font-size: 10px;
  color: #cc0000;
  font-weight: bold;
  text-align: center;
  width: 100%;
  padding-top: 2px;
}

/* Style for selected day */
.day.selected {
  border: 2px solid #007bff;
}

</style>