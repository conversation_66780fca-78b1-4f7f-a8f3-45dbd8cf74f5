<template>
  <div>
    <!-- Mobile menu overlay -->
    <div 
      v-if="isMobileMenuOpen" 
      @click="closeMobileMenu" 
      class="mobile-overlay" 
      :class="{ 'active': isMobileMenuOpen }"
      aria-hidden="true">
    </div>

    <!-- Sidebar -->
    <div 
      class="sidebar" 
      :class="{ 
        'open': isMobileMenuOpen,
        'collapsed': isCollapsed 
      }">
      
      <div class="sidebar-header">
        <div class="flex items-center gap-2">
          <LocalizedLink :to="rootPath" :use-anchor="true">
            <img :src="logoPath" alt="Týmbox Logo" :class="{ 'h-8': !isCollapsed, 'hidden': isCollapsed }" />
          </LocalizedLink>
        </div>
        <button @click="closeMobileMenu" class="p-1 rounded-md text-gray-500 md:hidden">
          <X :size="20" />
        </button>
        <button @click="toggleSidebar" class="p-1 rounded-md hover:bg-gray-100 text-gray-500 hidden md:block">
          <ChevronLeft v-if="!isCollapsed" :size="16" />
          <ChevronRight v-else :size="16" />
        </button>
      </div>
      
      <!-- Time tracking quick access -->
      <div class="time-tracker">
        <div class="flex flex-col sidebar-expanded">
          <span class="text-sm font-medium text-gray-800">{{ $t('work_status', 'Pracovní stav') }}</span>
          <span class="text-xs text-gray-500">
            {{ isWorking ? $t('at_work', 'V práci') : $t('out_of_office', 'Mimo práci') }}
          </span>
        </div>
        <span :class="['time-toggle', isWorking ? 'time-toggle-active' : 'time-toggle-inactive']" @click="toggleWorkStatus">
          <Clock :size="20" />
        </span>
      </div>
      
      <!-- Navigation -->
      <div class="sidebar-content">
        <!-- Main section -->
        <div class="nav-section">
          <div class="nav-section-title sidebar-expanded">{{ $t('main_section', 'HLAVNÍ') }}</div>
          
          <LocalizedLink :to="rootPath" :class="['nav-item', { 'nav-item-active': isCurrentPage(rootPath) }]" :use-anchor="true">
            <div class="flex items-center">
              <LayoutDashboard :size="20" class="nav-item-icon" />
              <span class="nav-item-text sidebar-expanded">{{ $t('overview', 'Titulka') }}</span>
            </div>
          </LocalizedLink>
          
        </div>
        
        <!-- Organization section -->
        <div class="nav-section">
          <div class="nav-section-title sidebar-expanded">{{ $t('your_business', 'VAŠE PODNIKÁNÍ') }}</div>
          
          <a :href="eventsPath" :class="['nav-item', { 'nav-item-active': isCurrentController('events') }]">
            <div class="flex items-center">
              <CalendarDays :size="20" class="nav-item-icon" />
              <span class="nav-item-text sidebar-expanded">{{ $t('agenda', 'Kalendář') }}</span>
            </div>
          </a>
          
          <a :href="worksPath" :class="['nav-item', { 'nav-item-active': isCurrentController('works') }]">
            <div class="flex items-center">
              <FolderKanban :size="20" class="nav-item-icon" />
              <span class="nav-item-text sidebar-expanded">{{ $t('works.title', 'Zakázky') }}</span>
            </div>
            <div class="sidebar-expanded">
              <ChevronRight :size="16" class="text-gray-400" />
            </div>
          </a>

          <a :href="bookingsPath" :class="['nav-item', { 'nav-item-active': isCurrentController('bookings') }]">
            <div class="flex items-center">
              <CalendarCheck :size="20" class="nav-item-icon" />
              <span class="nav-item-text sidebar-expanded">{{ $t('online_bookings', 'Online rezervace') }}</span>
            </div>
            <div class="sidebar-expanded">
              <ChevronRight :size="16" class="text-gray-400" />
            </div>
          </a>

          <a :href="meetingsPath" :class="['nav-item', { 'nav-item-active': isCurrentController('meetings') }]">
            <div class="flex items-center">
              <Users :size="20" class="nav-item-icon" />
              <span class="nav-item-text sidebar-expanded">{{ $t('meeting_assistant', 'Plánovač schůzek') }}</span>
            </div>
            <div class="sidebar-expanded">
              <ChevronRight :size="16" class="text-gray-400" />
            </div>
          </a>


        </div>
        
        <!-- Attendance section -->
        <div class="nav-section">
          <div class="nav-section-title sidebar-expanded">{{ $t('reports', 'REPORTY') }}</div>
          
          <a :href="reportDailyLogsPath" :class="['nav-item', { 'nav-item-active': isCurrentController('daily_logs') && isCurrentAction('report') }]">
            <div class="flex items-center">
              <Clock :size="20" class="nav-item-icon" />
              <span class="nav-item-text sidebar-expanded">{{ $t('monthly_report', 'Měsíční výkaz') }}</span>
            </div>
          </a>

          <a v-if="isOwner" :href="ownerReportsPath" :class="['nav-item', { 'nav-item-active': isCurrentController('daily_logs') && isCurrentAction('owner_reports') }]">
            <div class="flex items-center">
              <Users :size="20" class="nav-item-icon" />
              <span class="nav-item-text sidebar-expanded">{{ $t('employee_reports', 'Docházka zaměstnanců') }}</span>
            </div>
          </a>
          
          <a :href="reportsActivitiesPath" :class="['nav-item', { 'nav-item-active': isCurrentController('reports') && isCurrentAction('activities') }]">
            <div class="flex items-center">
              <Activity :size="20" class="nav-item-icon" />
              <span class="nav-item-text sidebar-expanded">{{ $t('activities', 'Aktivity') }}</span>
            </div>
          </a>
          
        </div>
        
        <!-- System section -->
        <div class="nav-section">
          <div class="nav-section-title sidebar-expanded">{{ $t('organization', 'ORGANIZACE') }}</div>
          
          <a v-if="isManager" :href="contractsPath" :class="['nav-item', { 'nav-item-active': isCurrentController('contracts') }]">
            <div class="flex items-center">
              <FileUser :size="20" class="nav-item-icon" />
              <span class="nav-item-text sidebar-expanded">{{ $t('team', 'Tým') }}</span>
            </div>
          </a>
          
          <a :href="companyConnectionsPath" :class="['nav-item', { 'nav-item-active': isCurrentController('company_connections') }]">
            <div class="flex items-center">
              <Bell :size="20" class="nav-item-icon" />
              <span class="nav-item-text sidebar-expanded">{{ $t('invitations', 'Pozvání') }}</span>
            </div>
          </a>
          
          <a :href="userSettingsPath" :class="['nav-item', { 'nav-item-active': isCurrentController('user_settings') }]">
            <div class="flex items-center">
              <Settings :size="20" class="nav-item-icon" />
              <span class="nav-item-text sidebar-expanded">{{ $t('personal_settings', 'Osobní nastavení') }}</span>
            </div>
            <div class="sidebar-expanded">
              <ChevronRight :size="16" class="text-gray-400" />
            </div>
          </a>
        </div>
      </div>
      
      <!-- User account info -->
      <div class="sidebar-footer">
        <div class="flex items-center">
          <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium">
            {{ userInitials }}
          </div>
          <div class="ml-3 flex-grow sidebar-expanded">
            <div class="text-sm font-small text-gray-800 trunc-email">{{ userEmail }}</div>
          </div>
          <form :action="destroyUserSessionPath" method="post" class="sidebar-expanded">
             <input type="hidden" name="_method" value="delete">
             <input type="hidden" name="authenticity_token" :value="csrfToken">
             <button type="submit" class="cursor-pointer p-0 border-none bg-transparent">
                <LogOut :size="18" class="text-gray-400 hover:text-gray-600" />
             </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { X, ChevronLeft, ChevronRight, Clock, LayoutDashboard, CalendarDays, FileText, CalendarCheck, FileBarChart2, LineChart, Users, Building2, Settings, LogOut, Bell, FileUser, FolderKanban, Activity } from 'lucide-vue-next';
import authorizationMixin from '../mixins/authorizationMixin';
import axios from 'axios';
import { sendFlashMessage } from '@/utils/flashMessage';
import LocalizedLink from './LocalizedLink.vue';

export default {
  name: 'Sidebar',
  mixins: [authorizationMixin],
  components: {
    X, ChevronLeft, ChevronRight, Clock, LayoutDashboard, CalendarDays, FileText, CalendarCheck, FileBarChart2, LineChart, Users, Building2, Settings, LogOut, Bell, FileUser, FolderKanban, Activity,
    LocalizedLink
  },
  props: {
    rootPath: String,
    logoPath: String,
    dailyLogsPath: String,
    eventsPath: String,
    worksPath: String,
    bookingsPath: String,
    meetingsPath: String,
    reportDailyLogsPath: String,
    ownerReportsPath: String,
    teamSummaryDailyLogsPath: String,
    contractsPath: String,
    companyConnectionsPath: String,
    userSettingsPath: String,
    reportsActivitiesPath: String,
    destroyUserSessionPath: String,
    currentPagePath: String,
    currentControllerName: String,
    currentActionName: String,
    userEmail: String,
    userRole: String,
    currentPlanName: String,
    csrfToken: String
  },
  data() {
    return {
      isCollapsed: false,
      isMobileMenuOpen: false,
      isWorking: false,
      currentDailyLog: null,
    };
  },
  computed: {
    userInitials() {
      return this.userEmail ? this.userEmail.substring(0, 2).toUpperCase() : '';
    },
    isOwner() {
      return this.userRole === 'owner';
    },
  },
  methods: {
    toggleSidebar() {
      this.isCollapsed = !this.isCollapsed;
      localStorage.setItem('sidebarCollapsed', this.isCollapsed);
      this.updateExpandedElements();
    },
    async toggleWorkStatus() {
      if (this.isWorking) {
        await this.endWork();
      } else {
        await this.startWork();
      }
    },
    async startWork() {
      try {
        const createResponse = await axios.post('/daily_logs');
        const { daily_log, notifications } = createResponse.data;
        this.currentDailyLog = daily_log; 
        this.isWorking = true;
        if (notifications?.length) {
          notifications.forEach(notification => {
            sendFlashMessage(this.$t(notification.message, notification.message), notification.type);
          });
        } else {
           sendFlashMessage(this.$t('work_started_message', 'Práce byla zahájena.'), 'info');
        }
        // Optionally reload or emit event to sync other components
        // window.location.reload(); // Simple but disruptive
      } catch (error) {
        this.handleError(error, this.$t('start_work_error', 'Nepodařilo se zahájit práci'));
      }
    },
    async endWork() {
      if (!this.currentDailyLog?.id) {
        console.error('No current daily log ID available to end work.');
        // Attempt to fetch current status if ID is missing
        await this.checkCurrentStatus();
        if (!this.currentDailyLog?.id) {
           sendFlashMessage(this.$t('cannot_end_work_no_active_log', 'Nelze ukončit práci, aktivní záznam nenalezen.'), 'alert');
           this.isWorking = false; // Assume not working if no active log found
           return;
        }
      }

      try {
        const endResponse = await axios.put(`/daily_logs/${this.currentDailyLog.id}`, {
          daily_log: { end_time: new Date().toISOString() }
        });
        this.isWorking = false;
        this.currentDailyLog = null;
        if (endResponse.data.notifications?.length) {
          endResponse.data.notifications.forEach(notification => {
            sendFlashMessage(this.$t(notification.message, notification.message), notification.type);
          });
        } else {
          sendFlashMessage(this.$t('work_ended_message', 'Práce byla ukončena.'), 'info');
        }
         // Optionally reload or emit event to sync other components
        // window.location.reload(); // Simple but disruptive
      } catch (error) {
        this.handleError(error, this.$t('end_work_error', 'Nepodařilo se ukončit práci'));
      }
    },
    async checkCurrentStatus() {
      try {
        const response = await axios.get('/daily_logs/current_status');
        const { last_log } = response.data;
        if (last_log && !last_log.end_time) {
          this.currentDailyLog = last_log;
          this.isWorking = true;
        } else {
          this.isWorking = false;
          this.currentDailyLog = null;
        }
      } catch (error) {
        console.error('Error checking sidebar work status:', error);
        // Don't show error to user, might be transient network issue
        this.isWorking = false; // Default to not working on error
        this.currentDailyLog = null;
      }
    },
    handleError(error, defaultMessage) {
      const errorMessage = error.response?.data?.errors?.join('\\n') || defaultMessage;
      sendFlashMessage(errorMessage, 'alert');
    },
    openMobileMenu() {
      console.log('Sidebar: openMobileMenu called, setting isMobileMenuOpen to true.');
      this.isMobileMenuOpen = true;
    },
    closeMobileMenu() {
      console.log('Sidebar: closeMobileMenu called, setting isMobileMenuOpen to false.');
      this.isMobileMenuOpen = false;
    },
    handleToggleMobileSidebar() {
      console.log('Sidebar: Received toggle event, opening mobile menu.');
      this.openMobileMenu();
    },
    updateExpandedElements() {
        this.$nextTick(() => {
             document.querySelectorAll('.sidebar-expanded').forEach(el => {
               el.style.display = this.isCollapsed ? 'none' : '';
             });
        });
    },
    isCurrentPage(path) {
      return this.currentPagePath === path;
    },
    isCurrentController(controllerName) {
      return this.currentControllerName === controllerName;
    },
    isCurrentAction(actionName) {
      return this.currentActionName === actionName;
    }
  },
  mounted() {
    this.isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    this.updateExpandedElements();
    document.addEventListener('toggle-mobile-sidebar', this.handleToggleMobileSidebar);
    window.addEventListener('keydown', this.handleEscapeKey);
    this.checkCurrentStatus();
  },
  beforeUnmount() {
    document.removeEventListener('toggle-mobile-sidebar', this.handleToggleMobileSidebar);
    window.removeEventListener('keydown', this.handleEscapeKey);
  },
  created() {
    this.handleEscapeKey = (e) => {
      if (e.key === 'Escape' && this.isMobileMenuOpen) {
        this.closeMobileMenu();
      }
    };
  }
};
</script>

<style scoped>
.trunc-email {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160px;
}

.sidebar.collapsed {
  width: 75px; 
}

.sidebar.collapsed .sidebar-header {
   padding-left: 1rem; 
   padding-right: 1rem; 
}
</style> 