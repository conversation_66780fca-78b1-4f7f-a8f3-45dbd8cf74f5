<template>
  <div v-if="show" class="modal-overlay" @click.self="close">
    <div class="modal-container">
      <div class="modal-header">
        <h2>{{ title }}</h2>
        <button @click="close" class="modal-close">&times;</button>
      </div>
      <div class="modal-content" v-html="content"></div>
      <div class="modal-footer">
        <button @click="close" class="btn btn-outline">Zavřít</button>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data() {
    return {
      show: false,
      title: '',
      content: '',
      locale: this.detectLocale()
    }
  },
  mounted() {
    document.addEventListener('openTerms', this.handleTerms)
    document.addEventListener('keydown', this.handleEscKey)
    console.log('Terms component initialized with locale:', this.locale)
  },
  beforeUnmount() {
    document.removeEventListener('openTerms', this.handleTerms)
    document.removeEventListener('keydown', this.handleEscKey)
  },
  methods: {
    detectLocale() {
      // Try multiple ways to detect the locale
      const htmlLang = document.documentElement.lang
      const pathLocale = window.location.pathname.split('/')[1]
      const metaLocale = document.querySelector('meta[name="locale"]')?.getAttribute('content')
      
      console.log('Locale detection:', { 
        htmlLang, 
        pathLocale, 
        metaLocale,
        isLocaleInPath: /^\/(en|sk|cs)\//.test(window.location.pathname)
      })
      
      // If URL path starts with a locale like /sk/, use that
      if (pathLocale && ['en', 'sk', 'cs'].includes(pathLocale)) {
        return pathLocale
      }
      
      // Otherwise try HTML lang attribute
      if (htmlLang) {
        return htmlLang
      }
      
      // Or meta tag if available
      if (metaLocale) {
        return metaLocale
      }
      
      // Fallback to default
      return 'en'
    },
    handleEscKey(event) {
      if (event.key === 'Escape' && this.show) {
        this.close()
      }
    },
    async handleTerms(event) {
      const type = event.detail
      try {
        console.log(`Loading terms of type '${type}' with locale '${this.locale}'`)
        const response = await axios.get(`/terms/${type}`, {
          params: { locale: this.locale }
        })
        console.log('Terms loaded successfully')
        this.title = type === 'tos' ? 'Podmínky poskytování služby Týmbox' : 'Zásady ochrany osobních údajů služby Týmbox'
        this.content = response.data
        this.show = true
      } catch (error) {
        console.error('Error loading terms:', error)
      }
    },
    close() {
      this.show = false
    }
  }
}
</script>

<style scoped>
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 900;
  }

  .modal-container {
    background-color: white;
    padding: 20px;
    border-radius: 12px;
    min-width: 380px;
    max-width: 600px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }

  .modal-header h2 {
    margin: 0;
    font-size: 24px;
  }

  .modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
  }

  .modal-content {
    max-height: 60vh;
    overflow-y: auto;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 10px;
    border-top: 1px solid #e5e5e5;
  }

  .btn-outline {
    background: none;
    border: 1px solid #ccc;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
  }

  .btn-outline:hover {
    background-color: #f0f0f0;
  }
</style>