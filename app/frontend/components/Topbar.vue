<template>
  <header class="topbar">
    <!-- Left side -->
    <div class="flex items-center">
      <button 
        @click="emitMobileMenuToggle" 
        id="mobile-menu-toggle" 
        class="mobile-menu-toggle md:hidden"
      >
        <Menu :size="24" />
      </button>
      
      <template v-if="companyName">
        <div class="company-selector mr-6">
          <LocalizedLink :to="'/companies'" class="font-medium text-gray-700" :use-anchor="true">{{ companyName }}</LocalizedLink>
          <span v-if="currentPlanName && currentPlanName !== 'free'" class="plan-badge">
            <Crown :size="12" class="mr-1" />
            {{ capitalizedPlanName }}
          </span>
        </div>
      </template>
    </div>

    <!-- Right side -->
    <template v-if="companyName">
      <div class="topbar-actions">
        <div class="inline md:hidden">
          <LocalizedLink :to="'/'" class="topbar-btn" :use-anchor="true">
            <LayoutDashboard :size="20" />
          </LocalizedLink>
        </div>
        <button 
          v-if="currentPlanName && currentPlanName !== 'free'"
          @click="openNewMeetingModal" 
          class="!hidden md:!inline-flex btn btn-primary"
          >
          {{ $t('new_meeting', 'Nová schůzka') }}
        </button>
      </div>
    </template>
  </header>
</template>

<script>
import { Menu, Crown, Bell, LayoutDashboard } from 'lucide-vue-next';
import LocalizedLink from './LocalizedLink.vue';

export default {
  name: 'Topbar',
  components: {
    Menu, 
    Crown, 
    Bell, 
    LayoutDashboard,
    LocalizedLink
  },
  props: {
    companyName: String,
    currentPlanName: String,
    userRole: String,
    companyConnectionsPath: String,
    teamSummaryPath: String,
    newWorkPath: String,
    rootPath: String,
  },
  data() {
    return {};
  },
  computed: {
    capitalizedPlanName() {
      if (!this.currentPlanName) return '';
      return this.currentPlanName.charAt(0).toUpperCase() + this.currentPlanName.slice(1);
    },
  },
  methods: {
    emitMobileMenuToggle() {
      console.log('Topbar: Mobile menu toggle clicked, emitting event.');
      document.dispatchEvent(new CustomEvent('toggle-mobile-sidebar'));
    },
    openNewMeetingModal() {
      const eventDetail = {
        detail: {
          componentName: 'MeetingForm',
          title: this.$t('new_meeting', 'Nová schůzka'),
          props: {} 
        }
      };
      document.dispatchEvent(new CustomEvent('open-central-modal', eventDetail));
    },

  }
};
</script>

<style scoped>
.topbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px; 
    padding: 0 0.5rem;
    background-color: white;
    border-bottom: 1px solid #e5e7eb;

    @media (min-width: 768px) {
     position: relative;
     padding: 0 1.5rem;
   }

}
.mobile-menu-toggle {
    padding: 0.5rem;
    margin-right: 0.5rem;
    color: #6b7280; 
}
.company-selector {
    display: flex;
    align-items: center;
}
.plan-badge {
    display: inline-flex;
    align-items: center;
    margin-left: 0.5rem;
    padding: 0.125rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: #eef2ff;
    color: #4338ca;
    border-radius: 9999px;
}
.topbar-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.topbar-btn {
    padding: 0.5rem;
    color: #6b7280;
    border-radius: 0.375rem;
}
.topbar-btn:hover {
    background-color: #f3f4f6;
    color: #1f2937;
}
.lang-switch {
    font-size: 0.75rem;
    font-weight: 500;
}
.lang-link {
    cursor: pointer;
    color: #6b7280;
}
.lang-link:hover {
    color: #1f2937;
}
</style> 