<template>
  <div class="modal-overlay">
    <div class="modal-container">
      <div class="modal-header">
        <h3 class="text-lg font-semibold">
          {{ $t('new_meeting', 'Nová schůzka') }}
        </h3>
        <button class="close-btn" @click="$emit('close')">&times;</button>
      </div>

      <div class="central-modal-content">
        <!-- Step 1: Participants -->
        <div v-if="currentStep === 1">
          <div class="form-group">
            <h4 class="form-label font-medium mb-1">
              {{ $t('meetings.select_colleague_participants', 'Vyberte účastníky mezi kolegy') }}
            </h4>
            <div v-if="contracts.length === 0" class="text-gray-500 text-sm mt-1 mb-1">
              {{ $t('meetings.no_active_colleagues_found', '<PERSON>ebyli nalezeni žádní aktivní kolegové') }}
            </div>
            <div v-else class="border rounded p-2 max-h-[250px] overflow-y-auto contracts-list">
              <div v-for="contract in contracts" :key="contract.id" class="form-checkbox-group">
                <input 
                  type="checkbox" 
                  :id="`contract-${contract.id}`" 
                  v-model="selectedContracts" 
                  :value="contract.id" 
                  class="form-checkbox"
                >
                <label :for="`contract-${contract.id}`" class="form-checkbox-label">
                  {{ contract.first_name }} {{ contract.last_name }} ({{ contract.email }})
                </label>
              </div>
            </div>
          </div>

          <div class="form-group mt-4">
            <h4 class="form-label font-medium mb-2">
              {{ $t('meetings.external_participants', 'Externí účastníci') }}
            </h4>
            <div v-for="(email, index) in additionalEmails" :key="index" class="flex items-center mb-1">
              <input 
                type="email" 
                v-model="additionalEmails[index]" 
                placeholder="E-mail externího účastníka" 
                class="form-input flex-grow mr-2" 
                autocomplete="email"
              >
              <button 
                v-if="additionalEmails[index]" 
                class="btn btn-danger-light text-xs" 
                @click="removeEmail(index)"
              >
                {{ $t('remove_email', 'Odebrat') }}
              </button>
            </div>
             <button class="btn btn-secondary btn-sm mt-1" @click="addEmailInput">
              {{ $t('add_email', 'Přidat email') }}
             </button>
          </div>
        </div>

        <!-- Step 2: Schedule -->
        <div v-else-if="currentStep === 2">
          <h4 class="form-label font-medium mb-2">
            {{ $t('meetings.select_max_2_days', 'Vyberte nejvíc 2 dny') }}
          </h4>
          <div class="schedule-presets mb-4" v-if="!showCustomDates">
            <div class="grid sm:grid-cols-2 gap-2 mb-2">
              <button class="btn btn-outline w-full" @click="selectNextWorkingDays">
                {{ $t('meetings.next_2_working_days', 'Následující 2 pracovní dny') }}
              </button>
              <button class="btn btn-outline w-full" @click="selectNextMidweek">
                {{ $t('meetings.next_tue_wed_thu', 'Následující Út/St/Čt') }}
              </button>
              <button class="btn btn-outline w-full" @click="selectSafeBet">
                {{ $t('meetings.not_earlier_than_10_days', 'Ne dříve než za 10 dní') }}
              </button>
            </div>
            <a href="#" @click.prevent="showCustomDates = true" class="text-sm text-blue-600 hover:underline">
              {{ $t('meetings.select_other_day', 'Vybrat jiný den') }}
            </a>
          </div>

          <div v-else class="date-selection">
            <button class="btn btn-secondary btn-sm mb-2" @click="showCustomDates = false">
              {{ $t('meetings.back_to_presets', 'Zpět na předvolby') }}
            </button>

            <div class="calendar mb-3">
               <div class="grid grid-cols-7 gap-1 text-xs font-semibold text-center text-gray-500 mb-1">
              </div>
              <div class="grid grid-cols-7 gap-1">
                <div 
                  v-for="date in availableDates" 
                  :key="date.toISOString()"
                  @click="toggleDateSelection(date)"
                  :class="[
                    'border rounded p-1 text-center cursor-pointer relative min-h-[50px] flex flex-col items-center justify-start',
                    { 
                      'bg-blue-100 border-blue-500': isDateSelected(date), 
                      'opacity-50 cursor-not-allowed': selectedDates.length >= 2 && !isDateSelected(date),
                      'bg-gray-100 text-gray-400': date.getDay() === 0 || date.getDay() === 6 || isHoliday(date),
                      'bg-red-50 border-red-300': hasAllDayConflicts(date) && !isDateSelected(date),
                      'hover:bg-gray-50': !isDateSelected(date) && !(selectedDates.length >= 2 && !isDateSelected(date))
                    }
                  ]"
                >
                  <div class="text-sm font-medium">{{ date.getDate() }}</div>
                  <div class="text-xs text-gray-500">{{ formatDayName(date) }}</div>
                  <!-- Indicators -->
                  <div class="absolute top-1 right-1 flex flex-col space-y-0.5">
                     <div v-if="hasAllDayConflicts(date)"
                          class="w-1.5 h-1.5 rounded-full bg-red-500 indicator conflict" 
                          :title="$t('meetings.all_day_conflict_title', 'Celodenní konflikt')"
                        ></div>
                     <div v-if="hasTimeSpecificConflicts(date)" 
                          class="w-1.5 h-1.5 rounded-full bg-blue-500 indicator time-conflict" 
                          :title="$t('meetings.time_specific_conflict_title', 'Časový konflikt')"
                        ></div>
                     <div v-if="isHoliday(date)" 
                          class="w-1.5 h-1.5 rounded-full bg-purple-500 indicator holiday" 
                          :title="$t('holiday', 'Svátek')"
                        ></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Holiday Info -->
            <div v-if="selectedDates.length > 0 && hasAnyHolidays()" class="border border-purple-200 bg-purple-50 rounded p-2 text-xs mb-3 holidays-container">
              <div v-for="date in selectedDates" :key="date.toISOString()">
                <div v-if="isHoliday(date)" class="mb-1 date-holidays">
                  <span class="font-bold">{{ formatDate(date) }}:</span> 
                  <span class="holiday-item">{{ $t('public_holiday', 'Státní svátek') }}</span>
                </div>
              </div>
            </div>
            
            <!-- Conflict Info -->
            <div v-if="selectedDates.length > 0 && hasAnyConflicts()" class="border border-red-200 bg-red-50 rounded p-2 text-xs mb-3 conflicts-container">
              <div v-for="date in selectedDates" :key="date.toISOString()">
                <div v-if="hasConflicts(date)" class="mb-1 last:mb-0 date-conflicts">
                  <div class="font-bold mb-0.5">{{ formatDate(date) }}:</div>
                  <ul class="list-disc list-inside pl-1 conflict-list">
                    <li v-for="(conflict, i) in getConflictsForDate(date)" :key="i" class="conflict-item" 
                        :class="{
                          'event-conflict': conflict.type === 'event',
                          'work-conflict': conflict.type === 'work',
                          'meeting-conflict': conflict.type === 'meeting'
                        }">
                      <span v-if="conflict.type === 'event'">
                        {{ conflict.title }} ({{ conflict.contract_name || $t('meetings.unknown_colleague', 'Neznámý kolega') }})
                      </span>
                      <span v-else-if="conflict.type === 'work'">
                         {{ conflict.title }} {{ conflict.confirmed_time ? `(${conflict.confirmed_time})` : '' }}
                      </span>
                      <span v-else> <!-- meeting -->
                        {{ $t('meeting', 'Schůzka') }}: {{ conflict.title }} {{ conflict.confirmed_date ? `(${formatTime(conflict.confirmed_date)})` : '' }}
                      </span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Time Slots -->
            <div v-if="selectedDates.length > 0" class="time-slots mt-4">
              <h4 class="form-label font-medium mb-2">
                <span class="text-red-600">
                  {{ $t('meetings.unselected_times', 'Odznačte nevyhovující') }}
                </span> 
                {{ $t('meetings.times', 'časy') }}
              </h4>
              <div v-for="date in selectedDates" :key="date.toISOString()" class="mb-3 date-slots">
                <div class="text-sm font-semibold mb-1">
                  {{ formatDate(date) }}
                </div>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-1 slots-grid">
                  <div 
                    v-for="slot in generateTimeSlots(date)" 
                    :key="`${date.toISOString()}-${slot.time}`"
                    @click="toggleTimeSlot(date, slot.time)"
                    :class="[
                      'border rounded p-1.5 cursor-pointer text-center text-xs time-slot',
                      { 'selected': isTimeSlotSelected(date, slot.time) },
                      { 'hover:bg-gray-50': !isTimeSlotSelected(date, slot.time) }
                    ]"
                  >
                    {{ slot.label }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 3: Details -->
        <div v-else-if="currentStep === 3">
          <div class="form-group">
            <label for="title" class="form-label">{{ $t('meetings.title_label', 'Titulek schůzky') }}:</label>
            <input type="text" id="title" v-model="meeting.title" class="form-input" required>
          </div>

          <div class="form-group">
            <label for="description" class="form-label">{{ $t('meetings.description_label', 'Napište text pozvánky - správa do e-mailu') }}:</label>
            <textarea id="description" v-model="meeting.description" rows="4" class="form-textarea"></textarea>
          </div>

          <div class="form-group">
            <label for="place" class="form-label">{{ $t('meetings.place_label', 'Místo schůzky') }}:</label>
            <input type="text" id="place" v-model="meeting.place" class="form-input">
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button v-if="currentStep > 1" class="btn btn-outline" @click="currentStep--">
          {{ $t('back', 'Zpět') }}
        </button>
        <button 
          v-if="currentStep < 3" 
          class="btn btn-primary" 
          @click="nextStep" 
          :disabled="!canProceed"
        >
          {{ $t('next', 'Další') }}
        </button>
        <button 
          v-else 
          class="btn btn-primary" 
          @click="submitMeeting"
          :disabled="!meeting.title || loading"
        >
          {{ loading ? $t('meetings.creating', 'Vytváření...') : $t('meetings.create', 'Vytvořit schůzku') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import dayjs from 'dayjs'

export default {
  data() {
    return {
      currentStep: 1,
      contracts: [],
      selectedContracts: [],
      additionalEmails: [''],
      showCustomDates: false,
      availableDates: [],
      selectedDates: [],
      selectedTimeSlots: {},
      meeting: {
        title: '',
        description: '',
        place: '',
        day_options: {}
      },
      loading: false,
      dayConflicts: {},
      isLoadingConflicts: false,
      contractEvents: [],
      contractWorks: [],
      contractMeetings: [],
      holidays: [],
    };
  },
  computed: {
    canProceed() {
      if (this.currentStep === 1) {
        return this.selectedContracts.length > 0 || this.validAdditionalEmails.length > 0;
      } else if (this.currentStep === 2) {
        return Object.keys(this.selectedTimeSlots).length > 0;
      }
      return true;
    },
    validAdditionalEmails() {
      return this.additionalEmails.filter(email => 
        email && /^[^'s@]+@[^'s@]+\.[^'s@]+$/.test(email)
      );
    }
  },
  mounted() {
    this.fetchContracts();
    this.generateAvailableDates();
  },
  methods: {
    async fetchContracts() {
      try {
        const response = await axios.get('/contracts/colleagues');
        this.contracts = response.data;
        console.log('Fetched colleagues:', this.contracts);
      } catch (error) {
        console.error('Error fetching colleagues:', error);
      }
    },
    addEmailInput() {
      this.additionalEmails.push('');
    },
    removeEmail(index) {
      this.additionalEmails.splice(index, 1);
      if (this.additionalEmails.length === 0) {
        this.additionalEmails = [''];
      }
    },
    async nextStep() {
      if (this.currentStep === 1 && this.currentStep < 3) {
        await this.fetchEventsForContracts();
        this.currentStep++;
      } else if (this.currentStep < 3) {
        this.currentStep++;
      }
    },
    hasConflicts(date) {
      const dateKey = this.formatDateKey(date);
      return this.dayConflicts[dateKey] && this.dayConflicts[dateKey].length > 0;
    },
    hasAllDayConflicts(date) {
      const dateKey = this.formatDateKey(date);
      return this.dayConflicts[dateKey] && 
             this.dayConflicts[dateKey].some(conflict => conflict.type === 'event');
    },
    hasTimeSpecificConflicts(date) {
      const dateKey = this.formatDateKey(date);
      return this.dayConflicts[dateKey] && 
             this.dayConflicts[dateKey].some(conflict => conflict.type !== 'event');
    },
    hasAnyConflicts() {
      return this.selectedDates.some(date => this.hasConflicts(date));
    },
    hasAnyHolidays() {
      return this.selectedDates.some(date => this.isHoliday(date));
    },
    getConflictsForDate(date) {
      const dateKey = this.formatDateKey(date);
      return this.dayConflicts[dateKey] || [];
    },
    generateAvailableDates() {
      const dates = [];
      const startDate = new Date();
      
      for (let i = 1; i <= 21; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);
        dates.push(date);
      }
      
      this.availableDates = dates;
    },

    selectNextWorkingDays() {
      this.selectedDates = [];
      const workingDays = this.availableDates
        .filter(date => date.getDay() !== 0 && date.getDay() !== 6)
        .slice(0, 2);
      
      if (workingDays.length > 0) {
        this.selectedDates = [...workingDays];
        this.initializeTimeSlots();
        this.showCustomDates = true
      }
    },

    selectNextMidweek() {
      this.selectedDates = [];
      
      const midweekDays = this.availableDates
        .filter(date => date.getDay() >= 2 && date.getDay() <= 4)
        .slice(0, 2);
      
      if (midweekDays.length > 0) {
        this.selectedDates = [...midweekDays];
        this.initializeTimeSlots();
        this.showCustomDates = true
      }
    },

    selectSafeBet() {
      this.selectedDates = [];
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 10);
      const futureDays = this.availableDates.filter(date => date > futureDate);
      let workingDays = [];
      for (let i = 0; i < futureDays.length; i++) {
        const day = futureDays[i].getDay();
        
        if (day !== 0 && day !== 6) {
          workingDays.push(futureDays[i]);
        }
        
        if (workingDays.length === 2) {
          break;
        }
      }
      
      this.selectedDates = workingDays;
      this.initializeTimeSlots();
      this.showCustomDates = true
    },

    async fetchEventsForContracts() {
      this.isLoadingConflicts = true;
      try {
        // Get date range for the request
        const startDate = this.availableDates[0].toISOString().split('T')[0];
        const endDate = this.availableDates[this.availableDates.length - 1].toISOString().split('T')[0];
        
        const response = await axios.get('/meetings/conflicts', {
          params: {
            contract_ids: this.selectedContracts,
            start_date: startDate,
            end_date: endDate
          }
        });
        
        this.contractEvents = response.data.events || [];
        this.contractWorks = response.data.works || [];
        this.contractMeetings = response.data.meetings || [];
        this.holidays = response.data.holidays || [];
        
        // Process conflicts into a lookup map by date
        this.processDayConflicts();
      } catch (error) {
        console.error('Error fetching conflicts:', error);
      } finally {
        this.isLoadingConflicts = false;
      }
    },

    processDayConflicts() {
      this.dayConflicts = {};

      const processRange = (startStr, endStr, conflictData) => {
        let current = dayjs(startStr);
        const end = dayjs(endStr);

        if (!current.isValid() || !end.isValid()) {
          console.warn("Invalid date range in processRange:", startStr, endStr);
          return;
        }

        while (current.isBefore(end) || current.isSame(end, 'day')) {
          const dateKey = current.format('YYYY-MM-DD');
          if (!this.dayConflicts[dateKey]) {
              this.dayConflicts[dateKey] = [];
          }
          if (!this.dayConflicts[dateKey].some(c => c.title === conflictData.title && c.type === conflictData.type)) {
              this.dayConflicts[dateKey].push(conflictData);
          }
          current = current.add(1, 'day');
        }
      };
      
      // Process events (like vacations, sick days, etc.)
      // this.contractEvents.forEach(event => {
      //   const start = new Date(event.start_time);
      //   const end = new Date(event.end_time);
        
      //   // For each day in the event range
      //   for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      //     const dateKey = d.toISOString().split('T')[0];
      //     if (!this.dayConflicts[dateKey]) {
      //       this.dayConflicts[dateKey] = [];
      //     }
      //     this.dayConflicts[dateKey].push({
      //       type: 'event',
      //       title: event.title || this.getEventTypeLabel(event.event_type),
      //       event_type: event.event_type
      //     });
      //   }
      //   console.log(this.dayConflicts)
      // });

      this.contractEvents.forEach(event => {
        processRange(event.start_time, event.end_time, {
          type: 'event',
          title: event.title || this.getEventTypeLabel(event.event_type),
          event_type: event.event_type,
          contract_name: event.contract_name
        });
      });

      this.contractWorks.forEach(work => {
        processRange(work.scheduled_start_date, work.scheduled_end_date, {
          type: 'work',
          title: work.title
        });
      });
      
      // Process works
      // this.contractWorks.forEach(work => {
      //   const start = new Date(work.scheduled_start_date);
      //   const end = new Date(work.scheduled_end_date);
        
      //   for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      //     const dateKey = d.toISOString().split('T')[0];
      //     if (!this.dayConflicts[dateKey]) {
      //       this.dayConflicts[dateKey] = [];
      //     }
      //     this.dayConflicts[dateKey].push({
      //       type: 'work',
      //       title: work.title
      //       confirmed_time: work.confirmed_time 
      //     });
      //   }
      // });
      
      // Process meetings
      this.contractMeetings.forEach(meeting => {
        if (meeting.confirmed_date) {
          const meetingDate = dayjs(meeting.confirmed_date);
          if(meetingDate.isValid()) {
            const dateKey = meetingDate.format('YYYY-MM-DD');
            if (!this.dayConflicts[dateKey]) {
              this.dayConflicts[dateKey] = [];
            }
            if (!this.dayConflicts[dateKey].some(c => c.title === meeting.title && c.type === 'meeting')) {
              this.dayConflicts[dateKey].push({
                  type: 'meeting',
                  title: meeting.title,
                  confirmed_date: meeting.confirmed_date
              });
            }
          } else {
            console.warn("Invalid meeting date:", meeting.confirmed_date);
          }
        }
      });

    },

    getEventTypeLabel(eventType) {
      const types = {
        'illness': 'Nemocenská',
        'day_care': 'Návštěva lékaře',
        'family_sick': 'Ošetřování člena rodiny',
        'other': 'Jiná absence',
        'vacation': 'Dovolená',
        'travel': 'Cesta'
      };
      return types[eventType] || eventType;
    },

    toggleDateSelection(date) {
      if (this.isDateSelected(date)) {
        this.selectedDates = this.selectedDates.filter(d => 
          d.toDateString() !== date.toDateString()
        );
        
        // Remove time slots for this date
        const dateKey = this.formatDateKey(date);
        delete this.selectedTimeSlots[dateKey];
      } else if (this.selectedDates.length < 2) {
        this.selectedDates.push(date);
        this.initializeTimeSlot(date);
      }
    },
    isDateSelected(date) {
      return this.selectedDates.some(d => d.toDateString() === date.toDateString());
    },
    initializeTimeSlots() {
      this.selectedTimeSlots = {};
      this.selectedDates.forEach(date => {
        this.initializeTimeSlot(date);
      });
    },
    initializeTimeSlot(date) {
      const dateKey = this.formatDateKey(date);
      this.selectedTimeSlots[dateKey] = {};
      
      // Initialize all time slots as selected
      this.generateTimeSlots(date).forEach(slot => {
        this.selectedTimeSlots[dateKey][slot.time] = true;
      });
    },
    generateTimeSlots(date) {
      const slots = [];
      for (let hour = 7; hour < 20; hour++) {
        // Skip lunch hour
        if (hour !== 12) {
          const time = `${hour}:00-${hour+1}:00`;
          slots.push({
            time,
            label: `${hour}:00 - ${hour+1}:00`
          });
        }
      }
      return slots;
    },
    toggleTimeSlot(date, time) {
      const dateKey = this.formatDateKey(date);
      if (!this.selectedTimeSlots[dateKey]) {
        this.selectedTimeSlots[dateKey] = {};
      }
      
      this.selectedTimeSlots[dateKey][time] = !this.selectedTimeSlots[dateKey][time];
    },
    isTimeSlotSelected(date, time) {
      const dateKey = this.formatDateKey(date);
      return this.selectedTimeSlots[dateKey] && this.selectedTimeSlots[dateKey][time];
    },
    formatDateKey(date) {
      // return date.toISOString().split('T')[0];
      return dayjs(date).format('YYYY-MM-DD');
    },
    formatDate(date) {
      return date.toLocaleDateString(this.$i18n.locale, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    },
    formatDayName(date) {
      return date.toLocaleDateString(this.$i18n.locale, { weekday: 'short' });
    },
    formatTime(date) {
      return dayjs(date).format('HH:mm');
    },
    prepareMeetingData() {
      // Format day options
      const dayOptions = {};
      
      Object.entries(this.selectedTimeSlots).forEach(([dateKey, timeSlots]) => {
        Object.entries(timeSlots).forEach(([timeSlot, isSelected]) => {
          if (isSelected) {
            const [startTime] = timeSlot.split('-');
            const [hour, minute] = startTime.split(':');
            
            // Create datetime string
            const dateTimeStr = `${dateKey}T${hour.padStart(2, '0')}:${minute.padStart(2, '0')}:00`;
            dayOptions[dateTimeStr] = true;
          }
        });
      });
      
      return {
        title: this.meeting.title,
        description: this.meeting.description,
        place: this.meeting.place,
        day_options: dayOptions,
        contract_ids: this.selectedContracts,
        additional_emails: this.validAdditionalEmails
      };
    },
    async submitMeeting() {
      if (!this.meeting.title) return;
      
      try {
        this.loading = true;
        const meetingData = this.prepareMeetingData();
        
        const response = await axios.post('/meetings', { meeting: meetingData });
        
        if (response.data.success) {
          this.$emit('meeting-created');
          this.$emit('close');
        }
      } catch (error) {
        console.error('Error creating meeting:', error);
      } finally {
        this.loading = false;
      }
    },
    isHoliday(date) {
      const dateStr = this.formatDateKey(date);
      return this.holidays.includes(dateStr);
    },
  }
};
</script>

<style scoped>
.modal-container {
  max-width: 700px;
  min-height: 500px;
  max-height: 90vh;
}

.central-modal-content {
  padding: 1rem 1.5rem;
}

/* Custom scrollbar for contract list */
.contracts-list::-webkit-scrollbar {
  width: 6px;
}
.contracts-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
.contracts-list::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}
.contracts-list::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

/* Specific styling for selected time slots */
.time-slot.selected {
  background-color: #dbeafe;
  border-color: #3b82f6;
  font-weight: 500;
}

/* Conflict/Holiday text colors within the info boxes */
.event-conflict {
  color: #c53030;
}
.work-conflict,
.meeting-conflict {
  color: #2b6cb0;
}
.holiday-item {
  color: #805ad5;
}

</style>