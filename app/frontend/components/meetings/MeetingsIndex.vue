<template>
  <div class="mt-4 md:mt-0"> 
    <advanced-feature 
      feature="meeting" 
      :title="$t('meeting_assistant', '<PERSON><PERSON><PERSON><PERSON><PERSON> schůzek')"
      @access-determined="handleAccessDetermined" 
      >
      <div class="flex flex-wrap justify-between items-center mb-4 md:mb-6 px-4 md:px-0">
        <h2 class="text-xl md:text-2xl font-semibold text-gray-800">
          {{ $t('meeting_assistant', '<PERSON><PERSON><PERSON><PERSON><PERSON> schůzek') }}
        </h2>
        <button class="btn btn-primary" @click="openNewMeetingModal">
          {{ $t('new_meeting', 'Nová schůzka') }}
        </button>
      </div>

      <!-- Placeholder Filtering Section -->
      <!-- <div class="content-panel mb-4 md:mb-6">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex-grow">
            Hidden Search Placeholder
            <label for="searchMeeting" class="form-label hidden">Hledat</label>
            <input type="text" id="searchMeeting" placeholder="Hledat ..." class="form-input hidden">
          </div>
          <div class="flex-shrink-0">
            <label for="statusFilterMeeting" class="form-label hidden">Stav</label>
            <select id="statusFilterMeeting" class="form-select">
              <option value="">Všechny stavy</option>
            </select>
          </div>
          <button class="btn btn-secondary">Filtrovat</button> 
        </div>
      </div> -->

      <!-- Loading state - using original text as Clock icon requires script change -->
      <div v-if="loading" class="flex justify-center items-center p-8">
        <span class="text-gray-500">{{ $t('loading', 'Načítání...') }}</span> 
      </div>
      
      <!-- Empty state -->
      <div v-else-if="meetings.length === 0" class="card"> 
        <div class="card-content"> 
          <p class="text-gray-600">{{ $t('no_meetings_found_placeholder', 'Nebyly nalezeny žádné schůzky. Klikněte na "Nová schůzka" pro vytvoření.') }}</p>
        </div>
      </div>
      
      <!-- Meetings list - Flex wrapped -->
      <div v-else class="flex flex-wrap gap-4 md:gap-6">
        <div v-for="meeting in meetings" :key="meeting.id" class="w-full sm:w-[calc(50%-0.75rem)] lg:w-[calc(33.333%-1rem)] min-w-[380px] max-w-[500px]">
          <ShowMeeting 
            :meeting="meeting"
            @delete="deleteMeeting"
          />
        </div>
      </div>
      
      <!-- Meeting Form Modal -->
      <MeetingForm 
        v-if="showMeetingForm"
        @close="showMeetingForm = false" 
        @meeting-created="fetchMeetings" 
      />
  
    </advanced-feature>
  </div>
</template>

<script>
import axios from 'axios';
import ShowMeeting from './ShowMeeting.vue';
import MeetingForm from './MeetingForm.vue';
import AdvancedFeature from '../AdvancedFeature.vue'; 

export default {
  components: {
    ShowMeeting,
    MeetingForm, 
    AdvancedFeature
  },
  data() {
    return {
      meetings: [],
      loading: true,
      showMeetingForm: false
    };
  },
  mounted() {
    this.fetchMeetings();
  },
  methods: {
    handleAccessDetermined(hasAccess) {
      if (hasAccess) {
        this.fetchMeetings();
      }
    },
    async fetchMeetings() {
      try {
        this.loading = true;
        const response = await axios.get('/meetings/fetch');
        this.meetings = response.data;
      } catch (error) {
        console.error('Error fetching meetings:', error);
      } finally {
        this.loading = false;
      }
    },
    openNewMeetingModal() {
      this.showMeetingForm = true;
    },
    async deleteMeeting(meetingId) {
    try {
        const confirmed = confirm('Opravdu chcete smazat tuto schůzku?');
        if (!confirmed) return;
        
        await axios.delete(`/meetings/${meetingId}`);
        this.meetings = this.meetings.filter(m => m.id !== meetingId);
      } catch (error) {
        console.error('Error deleting meeting:', error);
      }
    }
  }
};
</script>
