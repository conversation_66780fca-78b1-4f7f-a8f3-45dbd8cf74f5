<template>
  <div class="card flex flex-col p-4" v-if="!isEditing">
    <div class="card-header flex justify-between items-start mb-0 p-0 border-b-0">
      <div class="flex items-center gap-2">
        <Users :size="16" class="text-blue-500 flex-shrink-0" />
        <h3 class="text-base font-semibold m-0 text-blue-900">{{ meeting.title }}</h3>
      </div>
      <div class="relative" ref="dropdown" v-if="isManager">
        <button class="p-1 text-gray-500 hover:text-gray-700" @click="toggleDropdown">
          <MoreVertical :size="18" />
        </button>
        <div
          v-if="showDropdown"
          class="absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-10"
        >
          <a href="#"
             class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
             @click.prevent="editMeeting">
            {{ $t('edit', 'Upravit') }}
          </a>
          <a href="#"
             class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
             @click.prevent="confirmDelete">
            {{ $t('delete', 'Smazat') }}
          </a>
        </div>
      </div>
    </div>
    <div class="flex items-center gap-2">
      <Clock :size="14" class="text-gray-500 flex-shrink-0" />
      <h3 class="text-base font-semibold m-0" v-if="meeting.confirmed_date">
        <span>{{ formatTime(meeting.confirmed_date) }}</span>
      </h3>
      
    </div>
    <div class="flex items-center gap-2">
      <MapPin :size="14" class="text-gray-500 flex-shrink-0" />
      <h3 class="text-base font-semibold m-0" v-if="meeting.place">
        <span>{{ meeting.place }}</span>
      </h3>
    </div>

    <p v-if="meeting.description" class="text-sm text-gray-600 mt-3 break-words">
      {{ meeting.description }}
    </p>

    <div class="flex flex-col gap-1.5 text-sm text-gray-700">
      <div class="flex items-center gap-1.5" v-if="meeting.confirmed_date">
        <Calendar :size="14" class="text-gray-500 flex-shrink-0" />
        <span>{{ formatDate(meeting.confirmed_date) }}</span>
      </div>

      <div v-if="meeting.meeting_users && meeting.meeting_users.length > 0" class="mt-1">
        <div class="flex flex-wrap gap-2 items-center w-full">
          <div v-if="meeting.created_by" class="inline-flex items-center gap-1 p-1 bg-blue-100 rounded text-xs max-w-[150px]">
            <CircleUserRound :size="14" class="text-blue-500 flex-shrink-0" />
            <span class="truncate">{{ meeting.created_by.email }}</span>
          </div>
          <div v-for="user in meeting.meeting_users" :key="user.id" class="inline-flex items-center gap-1 p-1 bg-gray-100 rounded text-xs max-w-[150px]">
            <CheckCircle2 v-if="user.status === 'replied' || 'confirmed'" :size="14" class="text-green-600" />
            <Clock v-else :size="14" class="text-yellow-600" />
            <span class="truncate">{{ user.email }}</span>
          </div>
        </div>
      </div>


      <div class="flex flex-wrap gap-2 mb-2 justify-end">
        <span v-if="meeting.status" class="badge" :class="getStatusClass(meeting.status)">
          {{ getStatusText(meeting.status) }}
        </span>
      </div>


    </div>
  </div>
</template>

<script>
import authorizationMixin from '../../mixins/authorizationMixin';
import { Calendar, MapPin, MoreVertical, CircleUserRound, Clock, CheckCircle2, Users } from 'lucide-vue-next';

export default {
  mixins: [authorizationMixin],
  components: {
    MapPin, MoreVertical, Calendar, CircleUserRound, Clock, CheckCircle2, Users
  },
  props: {
    meeting: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isEditing: false,
      showDropdown: false
    };
  },
  computed: {
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
    },
    handleClickOutside(event) {
      if (this.showDropdown && this.$refs.dropdown && !this.$refs.dropdown.contains(event.target)) {
        this.showDropdown = false;
      }
    },
    getStatusClass(status) {
      console.log(status);
      switch(status) {
        case 'pending': return 'pending';
        case 'confirmed': return 'confirmed';
        case 'cancelled': return 'cancelled';
        default: return 'inactive';
      }
    },
    getStatusText(status) {
      switch(status) {
        case 'pending': return this.$t('meetings.pending_meeting', 'Nepotvrzená schůzka');
        case 'confirmed': return this.$t('meetings.confirmed_meeting', 'Potvrzená schůzka');
        case 'cancelled': return this.$t('meetings.cancelled_meeting', 'Zrušená schůzka');
        default: return status;
      }
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleString('cs-CZ', {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
      });
    },
    formatTime(dateString) {
      const date = new Date(dateString);
      return date.toLocaleTimeString('cs-CZ', {
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    editMeeting() {
      this.isEditing = true;
      this.showDropdown = false;
      this.$emit('editing', this.meeting.id);
    },
    confirmDelete() {
      if (confirm(this.$t('meetings.confirm_delete_meeting_q', 'Opravdu chcete smazat tuto schůzku?'))) {
        this.$emit('delete', this.meeting.id);
      }
      this.showDropdown = false;
    }
  }
};
</script>
