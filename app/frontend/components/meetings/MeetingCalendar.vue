<template>
  <div class="svg-background">
  </div>
  <div class="meeting-container">
    <div class="flex flex-row justify-end mb-2">
      <LanguageSelector :current-locale="currentLocale" :use-path-prefix="false" />
    </div>
    <div v-if="!meeting" class="completed-notice">
      <div class="alert">
        <CheckCheck size="16" />
        <strong>{{ $t('meetings.meeting_scheduled_title', 'Schůzka je naplánovaná') }}</strong>
        <p>{{ $t('meetings.optional_slots_unavailable', 'Volitelné termíny již nejsou dostupné.') }}</p>
      </div>
    </div>

    <div v-else class="header invitation-header">
      <div class="header-content">
        <p class="invitation-message">
          <strong>{{ $t('meetings.planned_meeting', 'Plánovaná schůzka') }}</strong>
        </p>
        <h2 class="invitation-title">{{ companyName }}</h2>
        <h3 class="meeting-title">{{ meeting.title }}</h3>
        <p v-if="meeting.description" class="meeting-description">{{ meeting.description }}</p>
        <p v-if="meeting.place" class="meeting-place">
          <MapPinIcon size="16" class="icon" />{{ $t('location', 'Místo') }}: {{ meeting.place }}
        </p>
      </div>
    </div>
    
    <div v-if="meeting && (meeting.confirmed_date || isReadOnly)" class="confirmed-notice">
      <div class="alert">
        <CheckCheck size="16" />
        <strong v-if="meeting.confirmed_date">{{ $t('meetings.meeting_confirmed_title', 'Schůzka potvrzena!') }}</strong>
        <strong v-else>{{ $t('meetings.meeting_scheduled_title', 'Schůzka naplánována!') }}</strong>
        <p v-if="meeting.confirmed_date">{{ $t('meetings.meeting_scheduled_for_date', 'Tato schůzka byla naplánována na {date}', { date: formatDate(meeting.confirmed_date) }) }}</p>
        <p v-else>{{ $t('meetings.meeting_slots_set', 'Termíny schůzky byly stanoveny.') }}</p>
      </div>
    </div>

    <div v-else-if="hasUserResponded" class="responded-notice">
      <div class="alert alert-success">
        <CheckCheck size="16" />
        <strong>{{ $t('meetings.preferences_sent_title', 'Vaše preference byly odeslány!') }}</strong>
        <p>{{ $t('meetings.email_confirmation_text', 'Na váš e-mail přijde zpráva s potvrzením termínu schůzky po zodpovězení všemi účastníky.') }}</p>
      </div>
      
      <h3 class="mt-3 mb-2">{{ $t('meetings.your_available_times', 'Vaše dostupné časy:') }}</h3>
      <div v-for="(slots, dateStr) in formattedOptions" :key="dateStr" class="date-block">
        <div class="mb-1">{{ formatDayLabel(dateStr) }}</div>
        <div class="time-grid">
          <div v-for="(slot, timeStr) in slots" 
              :key="`${dateStr}-${timeStr}`"
              :class="[
                'time-slot', 
                { 'selected-readonly': slot.selected }
              ]">
            {{ timeStr }}
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="availability-selection">
      <h2 v-if="meeting" class="mb-4">
        {{ $t('meetings.unselect', 'Odznačte') }}
        <span class="unselect">
          {{ $t('meetings.unavailable', 'nevyhovující') }}
        </span>
        {{ $t('meetings.times', 'časy') }}
      </h2>
      
      <div v-if="loading" class="center">{{ $t('meetings.loading_calendar', 'Načítání vašeho kalendáře...') }}</div>
      
      <div v-else>
        <div v-for="(slots, dateStr) in formattedOptions" :key="dateStr" class="date-block">
          <h3 class="mb-2">{{ formatDayLabel(dateStr) }}</h3>

          <div v-if="isHoliday(dateStr)" class="day-notice holiday-notice">
            <AlertCircle size="14" /> {{ $t('meetings.public_holiday', 'Státní svátek') }}
          </div>

          <div v-if="hasDateConflict(dateStr)" class="day-notice event-notice">
            <AlertCircle size="14" /> {{ $t('meetings.event_on_this_day', 'Máte událost tento den') }}
          </div>

          <div class="time-grid">
            <button v-for="(slot, timeStr) in slots" 
                    :key="`${dateStr}-${timeStr}`"
                    :class="[
                    'time-slot',
                    {
                      'selected': slot.selected,
                      'conflict': hasTimeConflict(dateStr, timeStr)
                    }
                  ]"
                  @click="toggleSlot(dateStr, timeStr)"
                  >
              {{ timeStr }}
            </button>
          </div>
        </div>
      </div>

      <div class="actions">
        <button 
          v-if="meeting"
          class="btn-primary" 
          @click="submitAvailability" 
          :disabled="Object.keys(selectedSlots).length === 0"
        >
          {{ $t('meetings.confirm_availability', 'Potvrdit dostupnost') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { CheckCheck, MapPinIcon, AlertCircle } from 'lucide-vue-next';
import LanguageSelector from '../LanguageSelector.vue';

export default {
  components: {
    CheckCheck,
    MapPinIcon,
    AlertCircle,
    LanguageSelector
  },
  props: {
    token: {
      type: String,
      required: true
    },
    companyName: {
      type: String,
      required: true
    },
    isReadOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      meeting: {},
      user: {},
      events: [],
      works: [],
      meetings: [],
      holidays: [],
      selectedSlots: {},
      loading: true,
      currentLocale: this.getCurrentLocale()
    };
  },
  computed: {
    timeLabels() {
      return Array.from({ length: 12 }, (_, i) => {
        const hour = i + 7;
        if (hour === 12) return null; 
        return `${hour}:00`;
      }).filter(Boolean);
    },
    hasUserResponded() {
      return this.user && 
            this.user.status == 1; 
            //  this.user.selected_dates && 
            //  Object.keys(this.user.selected_dates).length > 0;
    },
    formattedOptions() {
      const options = {};
    
      if (!this.meeting || !this.meeting.day_options) return options;
      
      let selectedDates = {};
      
      if (this.hasUserResponded) {
        selectedDates = this.user.selected_dates;
      } else {
        selectedDates = this.selectedSlots;
      }
      
      const selectedSlotKeys = Object.keys(selectedDates);
      
      Object.keys(this.meeting.day_options).forEach(dateTimeStr => {
        const [date, time] = dateTimeStr.split('T');
        const timeKey = time.substr(0, 5);
        
        if (!options[date]) {
          options[date] = {};
        }
        
        options[date][timeKey] = {
          available: true,
          selected: selectedSlotKeys.includes(dateTimeStr)
        };
      });
      
      return options;
    }  
  },
  mounted() {
    this.fetchMeetingData();
  },
  methods: {
    async fetchMeetingData() {
      try {
        const response = await axios.get(`/private_meetings/${this.token}/meeting`);
        
        // If we got status completed, show completed state without any data
        if (response.data.status === 'completed') {
          this.loading = false;
          this.meeting = null;
          this.user = null;
          return;
        }
        
        this.meeting = response.data.meeting;
        this.user = response.data.user;
        this.meetings = response.data.meetings || [];
        this.events = response.data.events || [];
        this.works = response.data.works || [];
        this.holidays = response.data.holidays || [];
        console.log('Fetched meetings data:', this.meetings);
        console.log('Fetched events data:', this.events);
        console.log('Fetched works data:', this.works);
        console.log('Fetched holidays data:', this.holidays);
        this.autoSelectAvailableSlots();
        
        this.loading = false;
      } catch (error) {
        console.error('Error fetching meeting data:', error);
      }
    },
    autoSelectAvailableSlots() {

      if (this.hasUserResponded) {
        this.selectedSlots = { ...this.user.selected_dates };
        return;
      }

      this.selectedSlots = {};

      if (!this.meeting.day_options) return;
  
      Object.keys(this.meeting.day_options).forEach(dateTimeStr => {
        const [date, time] = this.parseDateTimeStr(dateTimeStr);
        const timeStr = time.substr(0, 5);
        
        if (!this.hasConflict(date, timeStr) && !this.isHoliday(date)) {
          this.selectedSlots[dateTimeStr] = true;
        }
      });
    },
    hasDateConflict(date) {
      // Check for event conflicts that affect the whole day
      const dayDate = new Date(date);
      
      // Check for work conflicts
      const workConflict = this.works.some(work => {
        const workDate = new Date(work.scheduled_start_date);
        return workDate.toDateString() === dayDate.toDateString();
      });
      
      // Check for full-day events
      const eventConflict = this.events.some(event => {
        const eventStart = new Date(event.start_time);
        const eventEnd = new Date(event.end_time);
        // Consider if the event spans the entire day or significant portion
        return eventStart.toDateString() === dayDate.toDateString() &&
               (eventEnd.toDateString() === dayDate.toDateString() || 
                (eventEnd - eventStart) > 6 * 60 * 60 * 1000); // more than 6 hours
      });
      
      return workConflict || eventConflict;
    },

    hasTimeConflict(date, time) {
      // Parse time to get hour
      const hour = parseInt(time.split(':')[0]);
      const startTime = new Date(`${date}T${time}:00`);
      const endTime = new Date(startTime);
      endTime.setHours(hour + 1);
      
      // Check for specific time conflicts with events
      const eventConflict = this.events.some(event => {
        const eventStart = new Date(event.start_time);
        const eventEnd = new Date(event.end_time);
        return this.datesOverlap(startTime, endTime, eventStart, eventEnd);
      });

      // Check for work conflicts with specific times
      const workConflict = this.works.some(work => {
        const workDate = new Date(work.scheduled_start_date);
        
        // If the work has a confirmed_time, check for time overlap
        if (work.confirmed_time) {
          const [hours, minutes] = work.confirmed_time.split(':');
          const workStartTime = new Date(workDate);
          workStartTime.setHours(parseInt(hours), parseInt(minutes));
          
          const workEndTime = new Date(workStartTime);
          workEndTime.setHours(workStartTime.getHours() + 1); // Assuming 1 hour duration
          
          return this.datesOverlap(startTime, endTime, workStartTime, workEndTime);
        }
        
        // If no specific time, consider as a conflict if it's the same day
        return workDate.toDateString() === startTime.toDateString();
      });
      
      // Check for meeting conflicts
      const meetingConflict = this.meetings.some(meeting => {
        if (!meeting.confirmed_date) return false;
        
        const meetingDate = new Date(meeting.confirmed_date);
        const meetingEnd = new Date(meetingDate);
        meetingEnd.setHours(meetingDate.getHours() + 1);
        
        return this.datesOverlap(startTime, endTime, meetingDate, meetingEnd);
      });
      
      return eventConflict || meetingConflict || workConflict;
    },

    hasConflict(date, time) {
      // Check for holidays
      if (this.isHoliday(date)) return true;
      return this.hasTimeConflict(date, time);
    },

    datesOverlap(start1, end1, start2, end2) {
      return start1 < end2 && start2 < end1;
    },
    isHoliday(dateStr) {
      return this.holidays.includes(dateStr);
    },
    toggleSlot(date, time) {
      const dateTimeStr = `${date}T${time}:00`;
      
      if (this.selectedSlots[dateTimeStr]) {
        delete this.selectedSlots[dateTimeStr];
      } else {
        this.selectedSlots[dateTimeStr] = true;
      }
    },
    parseDateTimeStr(dateTimeStr) {
      const [date, timeWithSeconds] = dateTimeStr.split('T');
      const time = timeWithSeconds.substr(0, 5);
      return [date, time];
    },
    async submitAvailability() {
      if (this.isReadOnly) return;
      try {
        const response = await axios.patch(`/private_meetings/${this.token}`, {
          selected_dates: this.selectedSlots
        });
        
        if (response.data.success) {
          // alert('Your availability has been saved!');
          // this.fetchMeetingData();
          await this.fetchMeetingData(); 
        }
      } catch (error) {
        console.error('Error submitting availability:', error);
      }
    },
    formatDate(dateStr) {
      if (!dateStr) return '';
      
      const date = new Date(dateStr);
      return date.toLocaleString(this.$i18n.locale, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    formatDayLabel(dateStr) {
      const date = new Date(dateStr);
      return date.toLocaleDateString(this.$i18n.locale, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    },
    getCurrentLocale() {
      const availableLocales = ['cs', 'sk'];
      
      // Check locale from cookie
      const cookies = document.cookie.split(';');
      for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'locale' && availableLocales.includes(value)) {
          return value;
        }
      }
      
      // Fallback to default locale
      return 'cs';
    }
  }
};
</script>

<style scoped>
.flex {
  display: flex;
}
.flex-row {
  flex-direction: row;
}
.justify-end {
  justify-content: flex-end;
}
.meeting-container{
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

.confirmed-notice {
      margin: 2rem 0;
}

.btn-secondary {
  padding: 12px 20px;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 8px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  color: #555;
  transition: all 0.2s ease;
}

.btn-primary {
  padding: 12px 20px;
  border: none;
  background-color: #007bff;
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: #f5f5f5;
}

.btn-primary:hover {
  background-color: #0069d9;
}

.alert {
  background: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.responded-notice {
  margin: 2rem 0;
}

.alert-success {
  background: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 2rem;
}

.selected-readonly {
  background-color: #e6f7ff;
  border-color: #1890ff;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
  font-size: 0.9rem;
  opacity: 0.8;
}

.invitation-header {
background: linear-gradient(135deg, #27A844, #1E7544);
color: #ffffff;
padding: 2rem;
margin-top: 0rem;
margin-bottom: 2rem;
border-radius: 8px;
text-align: center;
box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.header-content {
max-width: 650px;
margin: 0 auto;
}

.invitation-title {
font-size: 2.2rem;
font-weight: bold;
margin-bottom: 1rem;
color: #f8f9fa;
}

.invitation-message {
font-size: 1.2rem;
margin-bottom: 1.5rem;
color: #e9ecef;
}

.meeting-title {
font-size: 1.8rem;
font-weight: 600;
margin-bottom: 1rem;
color: #ffffff;
}

.meeting-description {
font-size: 1rem;
font-style: italic;
margin-bottom: 1rem;
color: #dee2e6;
}

.meeting-place {
font-size: 1rem;
display: flex;
align-items: center;
justify-content: center;
gap: 0.5rem;
color: #ffc107;
}

.icon {
color: #ffc107;
}

.date-block {
  margin-bottom: 2rem;
}

.mb-2 {
  margin-bottom: 0.5rem
}

.mb-4 {
  margin-bottom: 1rem
}

.time-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.5rem;
}

.time-slot {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  font-size: 0.9rem;
  color: #999;
}

.unselect {
  color: #999;
}

.time-slot.selected {
  background-color: #e6f7ff;
  border-color: #1890ff;
  color: black;
}

.actions {
  margin-top: 3rem;
  text-align: center;
}

.day-notice {
  margin: 0.5rem 0;
  padding: 0.3rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.holiday-notice {
  background: #f8d7da;
  color: #721c24;
}

.event-notice {
  background: #fff3cd;
  color: #856404;
}

.time-slot.conflict {
  border-color: #ffccc7;
  color: #cf1322;
  &.selected {
    background: #ffe8e6;
    border-color: #ff4d4f;
  }
}


</style>