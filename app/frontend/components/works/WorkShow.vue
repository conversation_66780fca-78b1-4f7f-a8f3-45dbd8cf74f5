<template>
  <div class="card flex flex-col p-4">
    <div class="flex justify-between items-start mb-0 p-0 border-b-0">
      <div class="flex items-center gap-2">
        <FolderKanban :size="16" class="text-green-600 flex-shrink-0" />
        <h3 class="text-base font-semibold m-0 text-green-800">{{ work.title }}</h3>
      </div>
      <div class="relative" ref="dropdown">
        <button class="p-1 text-gray-500 hover:text-gray-700" @click="toggleDropdown">
          <MoreVertical :size="18" />
        </button>
        <div
          v-if="showDropdown"
          class="absolute right-0 p-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10"
        >
          <a href="#" 
            v-if="!isAssigned" 
            @click="takeAssignment"
            class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
            :disabled="takingAssignment"
          >
            {{ $t('works.assignments.take_assignment', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>') }}
          </a>
          <a href="#" 
            v-else-if="canLeaveAssignment" 
            @click="leaveAssignment"
            class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
            :disabled="leavingAssignment"
          >
            {{ $t('works.assignments.leave_assignment', 'Opustit zakázku') }}
          </a>
          <a href="#"
             class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
             @click.prevent="editWork">
            {{ $t('edit', 'Upravit') }}
          </a>
          <a href="#"
             class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
             @click.prevent="confirmDelete">
            {{ $t('delete', 'Smazat') }}
          </a>
        </div>
      </div>
    </div>
    <div class="flex items-center gap-2 mt-2">
      
      <template v-if="work.confirmed_time">
        <Clock :size="14" class="text-gray-500 flex-shrink-0" />
        <h3 class="text-base font-semibold m-0">
          <span>
            {{ formatTime(work.confirmed_time) }}
          </span>
        </h3>
      </template>

      <template v-else-if="work.specific_time || translatedTime">
        <Clock :size="14" class="text-gray-500 flex-shrink-0" />
        <div class="text-base m-0">
          <span class="font-semibold">{{ translatedTime }}</span>
          <span v-if="work.specific_time" class="ml-[22px]">
            {{ $t('preferred_time', 'preferovaný čas') }}: {{ formatTime(work.specific_time) }}
          </span>
        </div>
      </template>
    </div>

    <div class="flex items-center gap-2 mt-1">
      <template v-if="work.location">
        <MapPin :size="14" class="text-gray-500 flex-shrink-0"/>
        <h3 class="text-base font-semibold m-0">
          <span>{{ work.location }}</span>
        </h3>
    </template>
    </div>
  
    <p v-if="work.description" class="mt-1 text-sm text-gray-600 mt-3 break-words">
      {{ work.description }}
    </p>

    <div class="flex flex-col mt-1 gap-1.5 text-sm text-gray-700">
      <div class="flex items-center gap-1.5" v-if="work.scheduled_start_date || work.scheduled_end_date">
        <Calendar :size="14" class="text-gray-500 flex-shrink-0" />
        <span>
          {{ work.scheduled_start_date ? formatDate(work.scheduled_start_date) : '' }}
          {{ work.scheduled_end_date ? '- ' + formatDate(work.scheduled_end_date) : '' }}
        </span>
      </div>

      <div class="mt-1">
        <div v-if="work.work_assignments && work.work_assignments.length > 0">
          <ul class="list-none p-0 m-0 flex flex-col gap-1">
            <li v-for="assignment in work.work_assignments" :key="assignment.id" class="flex items-center gap-1.5">
              <CircleUserRound :size="14" class="text-gray-500 flex-shrink-0" />
              <span class="text-sm">
                {{ assignment.contract.first_name }} {{ assignment.contract.last_name }}
              </span>
            </li>
          </ul>
        </div>
        <div v-else class="flex items-center gap-1.5">
          <Users :size="14" class="text-gray-500 flex-shrink-0" />
          <span class="text-sm text-gray-500">{{ $t('works.assignments.no_assignments', 'Žádná přiřazení') }}</span>
        </div>
        
        <!-- Assignment management buttons -->
        <div class="mt-2 flex gap-2">
     
        </div>
      </div>

      <div class="flex flex-wrap gap-2 mb-2 justify-end">
        <span v-if="work.status" class="badge" :class="getStatusClass(work.status)">
          {{ getStatusText(work.status) }}
        </span>
        <span v-if="work.booking" class="badge" :class="getBookingStatusClass(work.booking.status)">
          {{ getBookingStatusText(work.booking.status) }}
        </span>
        <div v-if="work.booking && work.booking.preferred_date && work.booking.preferred_date !== work.scheduled_start_date" class="flex items-center">
          <span class="badge badge-warning">
            {{ $t('works.change_to', 'Změna na') }}: {{ formatDate(work.booking.preferred_date) }}
          </span>
        </div>
        <div v-if="work.booking && work.booking.preferred_period && work.booking.preferred_period !== work.preferred_period" class="flex items-center">
          <span class="badge badge-warning">
              {{ $t('works.change_to', 'Změna na') }}: {{ getTranslatedBookingPeriod(work.booking.preferred_period) }}
          </span>
        </div>
        <div v-if="work.booking && work.booking.specific_time && work.booking.specific_time !== work.specific_time" class="flex items-center">
          <span class="badge badge-warning">
            {{ $t('works.change_to', 'Změna na') }}: {{ formatTime(work.booking.specific_time) }}
          </span>
        </div>
      </div>


      
    </div>

  </div>
</template>

<script>
import axios from 'axios';
import { Calendar, Clipboard, MapPin, MoreVertical, FolderOpen, CircleUserRound, Clock, FolderKanban, Users, UserPlus, UserMinus } from 'lucide-vue-next';
import { sendFlashMessage } from '/utils/flashMessage';

export default {
  components: {
    MapPin, Clipboard, MoreVertical, Calendar, FolderOpen, CircleUserRound, Clock, FolderKanban, Users, UserPlus, UserMinus
  },
  props: {
    work: {
      type: Object,
      required: true
    },
    inline: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    translatedTime() {
      const timeTranslations = {
        morning: this.$t('morning', 'Dopoledne'),
        afternoon: this.$t('afternoon', 'Odpoledne'),
        allday: this.$t('all_day', 'Celý den')
      };
      return timeTranslations[this.work.preferred_period] || this.work.preferred_period;
    },
    workStatusClass() {
      return this.getStatusClass(this.work.status);
    },
    isAssigned() {
      // Check if current user is assigned to this work
      if (!this.currentUserContractId) return false;
      return this.work.work_assignments?.some(a => a.contract_id === this.currentUserContractId) || false;
    },
    canLeaveAssignment() {
      // Can leave if assigned and there's more than one assignee
      return this.isAssigned && this.work.work_assignments?.length > 1;
    }
  },
  data() {
    return {
      showDropdown: false,
      takingAssignment: false,
      leavingAssignment: false,
      currentUserContractId: null
    };
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
    document.addEventListener('work-updated', this.handleWorkUpdated);
    this.fetchCurrentUserContract();
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
    document.removeEventListener('work-updated', this.handleWorkUpdated);
  },
  methods: {
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
    },
    handleClickOutside(event) {
      if (this.showDropdown && !this.$refs.dropdown.contains(event.target)) {
        this.showDropdown = false;
      }
    },
    handleWorkUpdated(event) {
      // Update this work if it's the one that was updated
      if (event.detail?.work?.id === this.work.id) {
        Object.assign(this.work, event.detail.work);
        this.$emit('updated', this.work);
      }
    },
    getStatusClass(status) {
      switch(status) {
        case 'scheduled': return 'scheduled';
        case 'in_progress': return 'in_progress';
        case 'completed': return 'completed';
        case 'cancelled': return 'cancelled';
        case 'rescheduled': return 'rescheduled';
        default: return 'inactive';
      }
    },
    getStatusText(status) {
      switch(status) {
        case 'scheduled': return this.$t('works.scheduled_work', 'Naplánovaná zakázka');
        case 'in_progress': return this.$t('works.in_progress_work', 'Zakázka probíhá');
        case 'completed': return this.$t('works.completed_work', 'Dokončená zakázka');
        case 'cancelled': return this.$t('works.cancelled_work', 'Zrušená zakázka');
        case 'rescheduled': return this.$t('works.booking_change_notice', 'Pozor, změna rezervace');
        default: return status;
      }
    },
    getBookingStatusClass(status) {
      switch(status) {
        case 'pending': return 'pending';
        case 'confirmed': return 'confirmed';
        case 'completed': return 'completed';
        case 'cancelled': return 'cancelled';
        case 'rescheduled': return 'rescheduled';
        default: return 'inactive';
      }
    },
    getBookingStatusText(status) {
      switch(status) {
        case 'pending': return this.$t('booking.new', 'Nová rezervace');
        case 'confirmed': return this.$t('booking.confirmed', 'Potvrzená rezervace');
        case 'completed': return this.$t('booking.completed', 'Dokončená rezervace');
        case 'cancelled': return this.$t('booking.cancelled', 'Zrušená rezervace');
        case 'rescheduled': return this.$t('booking.rescheduled', 'Přeplánovaná rezervace');
        default: return status;
      }
    },
    getTranslatedBookingPeriod(period) {
      const periodTranslations = {
        morning: this.$t('morning', 'Dopoledne'),
        afternoon: this.$t('afternoon', 'Odpoledne'),
      };
      return periodTranslations[period] || period;
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleString('cs-CZ', {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
      });
    },
    formatTime(dateString) {
      const date = new Date(dateString);
      return date.toLocaleTimeString('cs-CZ', {
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    editWork() {
      this.showDropdown = false;
      const event = new CustomEvent('open-central-modal', {
        detail: {
          componentName: 'WorkForm', 
          title: `${this.$t('works.edit_work', 'Upravit zakázku')}: ${this.work.title}`,
          props: { work: this.work }
        }
      });
      document.dispatchEvent(event);
    },
    confirmDelete() {
      if (confirm(this.$t('works.confirm_delete', 'Opravdu chcete smazat tuto zakázku?'))) {
        this.deleteWork();
      }
      this.showDropdown = false;
    },
    async deleteWork() {
      try {
        await axios.delete(`/works/${this.work.id}.json`);
        this.$emit('deleted', this.work.id);
        sendFlashMessage(this.$t('works.deleted_success', 'Zakázka byla úspěšně smazána'), 'success');
      } catch (error) {
        const errorMsg = error.response?.data?.error || this.$t('works.delete_error', 'Chyba při mazání zakázky');
        sendFlashMessage(errorMsg, 'error');
      }
    },
    async takeAssignment() {
      this.takingAssignment = true;
      try {
        const response = await axios.post(`/works/${this.work.id}/take_assignment.json`);
        // Update the work with new assignment data
        if (response.data.work) {
          Object.assign(this.work, response.data.work);
          this.$emit('updated', this.work);
        }
        sendFlashMessage(response.data.message || this.$t('works.assignments.assignment_taken', 'Přiřazení bylo úspěšné'), 'success');
      } catch (error) {
        const errorMsg = error.response?.data?.error || this.$t('works.assignments.take_error', 'Chyba při přiřazení');
        sendFlashMessage(errorMsg, 'error');
      } finally {
        this.takingAssignment = false;
      }
    },
    async fetchCurrentUserContract() {
      try {
        const response = await axios.get('/contracts/colleagues?include_self=true');
        this.currentUserContractId = response.data.current_user_contract_id;
      } catch (error) {
        console.error('Error fetching current user contract:', error);
      }
    },
    async leaveAssignment() {
      if (!confirm(this.$t('works.assignments.confirm_leave', 'Opravdu chcete opustit tuto zakázku?'))) {
        return;
      }
      
      this.leavingAssignment = true;
      try {
        const response = await axios.delete(`/works/${this.work.id}/leave_assignment.json`);
        // Update the work with new assignment data
        if (response.data.work) {
          Object.assign(this.work, response.data.work);
          this.$emit('updated', this.work);
        }
        sendFlashMessage(response.data.message || this.$t('works.assignments.assignment_left', 'Přiřazení bylo odebráno'), 'success');
      } catch (error) {
        const errorMsg = error.response?.data?.error || this.$t('works.assignments.leave_error', 'Chyba při opuštění zakázky');
        sendFlashMessage(errorMsg, 'error');
      } finally {
        this.leavingAssignment = false;
      }
    }
  }
};
</script>
