<template>
  <div class="form-container">
    <form @submit.prevent="submitForm" class="styled-form">
      <div class="form-group">
        <h3>
          <input 
            id="title" 
            v-model="form.title" 
            class="form-input title-input" 
            type="text" 
            :placeholder="$t('works.title', 'Název zakázky')"
            required 
          />
        </h3>
      </div>
      <div class="form-group">
        <input 
          id="location" 
          v-model="form.location" 
          class="form-input" 
          type="text"
          :placeholder="$t('location', 'Místo')"
        />
      </div>

      <div class="form-group">
        <input 
          id="description" 
          v-model="form.description" 
          class="form-input"
          :placeholder="$t('brief_description', 'Stručný popis')"
        ></input>
      </div>

      <div class="form-group">
        <label class="form-label">{{ $t('time_period', 'Časové období') }}</label>
        <VueDatePicker 
          v-model="form.dateRange"
          :format="formatDateRange"
          :range="{ partialRange: true }"
          :enable-time-picker="false"
          :placeholder="$t('select_time_period', 'Vyberte časové období')"
          week-start="1"
          :locale= "$i18n.locale"
          :cancel-text="$t('cancel', 'Zrušit')" 
          :select-text="$t('select', 'Vybrat')"
          @update:modelValue="updateDateRange"
        />
      </div>

      <div class="form-group">
        <div class="btn-group" role="group" aria-label="Preferred Period">
          <input type="radio" class="btn-check" id="preferred_period-morning" value="morning" v-model="form.preferred_period">
          <label class="btn btn-outline-status" for="preferred_period-morning">{{ $t('morning', 'Dopoledne') }}</label>

          <input type="radio" class="btn-check" id="preferred_period-afternoon" value="afternoon" v-model="form.preferred_period">
          <label class="btn btn-outline-status" for="preferred_period-afternoon">{{ $t('afternoon', 'Odpoledne') }}</label>

          <input type="radio" class="btn-check" id="preferred_period-allday" value="allday" v-model="form.preferred_period">
          <label class="btn btn-outline-status" for="preferred_period-allday">{{ $t('all_day', 'Celý den') }}</label>

          <input type="radio" class="btn-check" id="preferred_period-none" value="" v-model="form.preferred_period">
          <label class="btn btn-outline-status" for="preferred_period-none">{{ $t('none', 'Žádné') }}</label>
        </div>
      </div>
      <div class="form-group">
        <label for="confirmed_time"></label>
        <input
          type="text"
          id="confirmed_time"
          v-model="form.confirmed_time"
          :placeholder="$t('exact_time_placeholder', 'Přesný čas? (např. 8:00)')"
          class="form-input"
        />
      </div>
      
      <!-- <div class="form-group">
        <input 
          id="work_type" 
          v-model="form.work_type" 
          class="form-input" 
          type="text"
          placeholder="Typ práce"
          />
        </div> -->
        
        <div class="form-group">
          <div class="btn-group" role="group" aria-label="Status">
            <input type="radio" class="btn-check" id="status-scheduled" value="scheduled" v-model="form.status">
            <label class="btn btn-outline-status" for="status-scheduled">{{ $t('works.scheduled', 'Naplánována') }}</label>
            
            <input type="radio" class="btn-check" id="status-in-progress" value="in_progress" v-model="form.status">
            <label class="btn btn-outline-status" for="status-in-progress">{{ $t('works.in_progress', 'Probíhá') }}</label>

            <input type="radio" class="btn-check" id="status-completed" value="completed" v-model="form.status">
            <label class="btn btn-outline-status" for="status-completed">{{ $t('works.completed', 'Dokončená') }}</label>
            
            
            <input type="radio" class="btn-check" id="status-cancelled" value="cancelled" v-model="form.status">
            <label class="btn btn-outline-status" for="status-cancelled">{{ $t('cancelled_f', 'Zrušená') }}</label>
            
            <input type="radio" class="btn-check" id="status-rescheduled" value="rescheduled" v-model="form.status" disabled>
            <label class="btn btn-outline-status disabled" for="status-rescheduled">{{ $t('works.booking_change', 'Změna rezervace') }}</label>
        </div>
      </div>
      
      
      <!-- <div class="form-group">
        <div class="form-check">
          <input type="checkbox" id="is_recurring" v-model="form.is_recurring" class="checkmark">
          <label for="is_recurring">Opakující se práce</label>
        </div>
      </div> -->
      
      <div class="form-group" v-if="contracts.length > 0">
        <label class="form-label">{{ $t('works.assignments.title', 'Přiřazení pracovníci') }}</label>
        <div v-for="contract in contracts" :key="contract.id" class="form-check">
          <input 
            type="checkbox" 
            :id="'contract-' + contract.id" 
            :value="contract.id" 
            v-model="selectedContracts"
            class="checkmark"
          >
          <label :for="'contract-' + contract.id">
            {{ contract.first_name }} {{ contract.last_name }}
            <span v-if="isCurrentUser(contract.id)" class="text-gray-500 text-sm ml-1">({{ $t('you', 'Vy') }})</span>
          </label>
        </div>
      </div>
      
      <div class="form-group">
        <div class="action-buttons mt-6">
          <button type="button" @click="$emit('cancel')" class="btn btn-outline">{{ $t('cancel', 'Zrušit') }}</button>
          <button type="submit" class="btn btn-primary" :disabled="submitting">
            {{ submitting ? $t('saving', 'Ukládání...') : (isEditing ? $t('update', 'Aktualizovat') : $t('create', 'Vytvořit')) }}
          </button>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import axios from 'axios';
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';

export default {
  components: {
    VueDatePicker
  },
  props: {
    work: {
      type: Object,
      default: null
    },
    initialDate: {
      type: [String, Date],
      default: null
    },
  },
  data() {
    return {
      form: {
        title: '',
        description: '',
        location: '',
        status: 'scheduled',
        scheduled_start_date: '',
        scheduled_end_date: '',
        preferred_period: '',
        confirmed_time: '',
        dateRange: [],
        work_type: '',
        is_recurring: false, 
        startDate: null,
        endDate: null,
      },
      contracts: [],
      selectedContracts: [],
      currentUserContractId: null,
      submitting: false,
      error: null
    };
  },
  computed: {
    isEditing() {
      return !!this.work;
    }
  },
  created() {
    this.fetchContracts();
    if (this.work) {
      this.initFormWithWork();
    } else if (this.initialDate) {
      if (this.initialDate) {
        let date = this.initialDate;
        if (typeof date === 'string') {
          date = new Date(date);
        }
        this.form.dateRange = [date, null];
        this.form.scheduled_start_date = this.formatDateForSubmission(date);
        this.form.scheduled_end_date = this.formatDateForSubmission(date);
      }
    }
  },
  methods: {
    async fetchContracts() {
      try {
        const response = await axios.get('/contracts/colleagues?include_self=true');
        this.contracts = response.data.colleagues;
        this.currentUserContractId = response.data.current_user_contract_id;
        
        if (this.work) {
          // Editing existing work - use existing assignments
          this.selectedContracts = this.work.work_assignments?.map(wa => wa.contract_id) || [];
        } else if (!this.selectedContracts.length && this.currentUserContractId) {
          // Creating new work - pre-select current user
          this.selectedContracts = [this.currentUserContractId];
        }
      } catch (error) {
        console.error('Error fetching colleagues:', error);
      }
    },
    initFormWithWork() {
      const work = this.work;
      const startDate = work.scheduled_start_date ? new Date(work.scheduled_start_date) : null;
      const endDate = work.scheduled_end_date ? new Date(work.scheduled_end_date) : null;
      
      this.form = {
        title: work.title || '',
        description: work.description || '',
        location: work.location || '',
        status: work.status || 'scheduled',
        scheduled_start_date: work.scheduled_start_date || '',
        scheduled_end_date: work.scheduled_end_date || '',
        dateRange: startDate ? [startDate, endDate] : [],
        work_type: work.work_type || '',
        is_recurring: work.is_recurring || false,
        confirmed_time: work.confirmed_time ? this.formatTimeForDisplay(work.confirmed_time) : ''
      };
    },

    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    },

    formatTimeForDisplay(timeStr) {
      if (!timeStr) return '';
      
      try {
        // If it's an ISO datetime string
        if (timeStr.includes('T') || timeStr.includes('-') || timeStr.includes('+')) {
          const date = new Date(timeStr);
          if (!isNaN(date.getTime())) {
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
          }
        }
        
        // If it's just a time string
        if (timeStr.includes(':')) {
          const parts = timeStr.split(':');
          if (parts.length >= 2) {
            return `${parts[0]}:${parts[1]}`;
          }
        }
        
        return timeStr;
      } catch (e) {
        console.error('Error formatting time:', e);
        return timeStr;
      }
    },

    parseTimeInput(value) {
      if (!value) return null;
      
      const cleaned = value.replace(/[^\d.,]/g, '');
      let hours, minutes;
      
      if (value.includes(':')) {
        // Handle 08:00 format
        [hours, minutes] = value.split(':').map(part => parseInt(part, 10));
      } else if (cleaned.includes('.') || cleaned.includes(',')) {
        // Handle 8.15 or 8,15 format
        const parts = cleaned.split(/[.,]/);
        hours = parseInt(parts[0], 10);
        minutes = parseInt(parts[1], 10);
      } else {
        // Handle 0800 or 800 format
        if (cleaned.length === 3) {
          hours = parseInt(cleaned[0], 10);
          minutes = parseInt(cleaned.slice(1), 10);
        } else if (cleaned.length === 4) {
          hours = parseInt(cleaned.slice(0, 2), 10);
          minutes = parseInt(cleaned.slice(2), 10);
        } else {
          // For simple numbers like "8", assume 8:00
          hours = parseInt(cleaned, 10);
          minutes = 0;
        }
      }
      
      // Validate hours and minutes
      if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
        return null;
      }
      
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    },
    async submitForm() {
      this.submitting = true;
      
      try {
        if (this.form.confirmed_time) {
          this.form.confirmed_time = this.parseTimeInput(this.form.confirmed_time);
        }

        const payload = {
          work: this.form,
          assigned_contracts: this.selectedContracts
        };
        
        let response;
        if (this.isEditing) {
          response = await axios.put(`/works/${this.work.id}`, payload);
        } else {
          response = await axios.post('/works', payload);
        }
        
        this.$emit('saved', response.data);
      } catch (error) {
        // Extract error message from response
        let errorMessage = 'Error saving work';
        if (error.response?.data?.errors) {
          if (Array.isArray(error.response.data.errors)) {
            errorMessage = error.response.data.errors.join(', ');
          } else {
            errorMessage = error.response.data.errors;
          }
        }
        
        this.error = errorMessage;
        const event = new CustomEvent('flashMessage', {
          detail: { text: errorMessage, type: 'error' }
        });
        document.dispatchEvent(event);
        
        // Don't emit saved event on error
        // Don't throw error to keep the modal open
      } finally {
        this.submitting = false;
      }
    },
    updateStartDate(date) {
      this.form.scheduled_start_date = this.formatDateForSubmission(date);
    },
    updateEndDate(date) {
      this.form.scheduled_end_date = this.formatDateForSubmission(date);
    },
    formatDateForSubmission(date) {
      if (!date) return '';
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    formatDateRange(date) {
      const [date0, date1] = date;
      if (!date0) return '';
      const day0 = date0.getDate();
      const month0 = date0.getMonth() + 1;
      if (!date1) return `${day0}.${month0}.`;
      const day1 = date1.getDate();
      const month1 = date1.getMonth() + 1;
      return `${day0}.${month0}.-${day1}.${month1}.`;
    },
    updateDateRange(dateRange) {
      if (dateRange && dateRange.length > 0) {
        this.form.scheduled_start_date = this.formatDateForSubmission(dateRange[0]);
        this.form.scheduled_end_date = dateRange[1] ? 
          this.formatDateForSubmission(dateRange[1]) : 
          this.formatDateForSubmission(dateRange[0]);
      } else {
        this.form.scheduled_start_date = '';
        this.form.scheduled_end_date = '';
      }
    },
    isCurrentUser(contractId) {
      // Check if this contract belongs to the current user
      return contractId === this.currentUserContractId;
    }
  }
};
</script>
<style scoped>
  .title-input {
    font-size: 1.17rem;
    font-weight: 600;
  }
  .btn-outline-status {
    border: 1px solid #007bff;
  }
  .btn-group .btn-check {
    display: none;
  }
  .btn-group .btn {
    margin: 2.5px;
    font-size: 0.875rem;
    padding: 0 0.5rem;
    height: 30px;
  }
  .btn-group .btn-check:checked + .btn {
    background-color: #007bff;
    color: white;
  }
  .btn.disabled {
    cursor: not-allowed;
    border: 1px solid #ccc;
    color: #ccc;
  }

</style>