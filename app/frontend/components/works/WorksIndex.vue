<template>
  <div class="mt-4 md:mt-0">
    <div class="flex flex-wrap justify-between items-center mb-4 md:mb-6 px-4 md:px-0 gap-4">
      <h2 class="text-xl md:text-2xl font-semibold text-gray-800">{{ $t('works.title', 'Zakázky') }}</h2>
      <button @click="showForm = true" class="btn btn-primary">{{ $t('works.new', 'Nová zakázka') }}</button>
    </div>

    <!-- Filtering Section -->
    <div class="content-panel mb-4 md:mb-6">
      <div class="flex flex-wrap items-center gap-4">
        <div class="flex-grow">
          <label for="searchWork" class="form-label hidden">{{ $t('search', 'Hledat') }}</label>
          <input 
            type="text" 
            id="searchWork" 
            v-model="searchQuery"
            @input="applyFilters"
            :placeholder="$t('works.search_placeholder', 'Hledat zakázky...')" 
            class="form-input"
          >
        </div>
        <div class="flex-shrink-0">
          <label for="statusFilter" class="form-label hidden">{{ $t('status', 'Stav') }}</label>
          <select 
            id="statusFilter" 
            v-model="statusFilter"
            @change="applyFilters"
            class="form-select"
          >
            <option value="">{{ $t('works.all_statuses', 'Všechny stavy') }}</option>
            <option value="scheduled">{{ $t('works.scheduled', 'Naplánována') }}</option>
            <option value="in_progress">{{ $t('works.in_progress', 'Probíhá') }}</option>
            <option value="completed">{{ $t('works.completed', 'Dokončená') }}</option>
            <option value="cancelled">{{ $t('works.cancelled_f', 'Zrušená') }}</option>
            <option value="rescheduled">{{ $t('works.rescheduled', 'Přeplánována') }}</option>
          </select>
        </div>
        <div class="flex-shrink-0">
          <label for="assignmentFilter" class="form-label hidden">{{ $t('works.assignments.filter', 'Přiřazení') }}</label>
          <select 
            id="assignmentFilter" 
            v-model="assignmentFilter"
            @change="applyFilters"
            class="form-select"
          >
            <option value="">{{ $t('works.all_assignments', 'Všichni') }}</option>
            <option value="my">{{ $t('works.my_assignments', 'Moje zakázky') }}</option>
            <option value="unassigned">{{ $t('works.unassigned', 'Nepřiřazené') }}</option>
          </select>
        </div>
        <button 
          v-if="hasActiveFilters"
          @click="clearFilters"
          class="btn btn-outline"
        >
          {{ $t('clear_filters', 'Vymazat filtry') }}
        </button>
      </div>
    </div>

    <div v-if="loading" class="flex justify-center items-center p-8">
      <Clock :size="24" class="loading-icon animate-spin text-gray-500" />
    </div>

    <div v-else-if="filteredWorks.length === 0 && works.length > 0" class="card">
      <div class="card-content">
        <p class="text-gray-600">{{ $t('works.no_filtered_results', 'Žádné zakázky neodpovídají vašemu filtru') }}</p>
      </div>
    </div>

    <div v-else-if="works.length === 0" class="card">
      <div class="card-content">
        <p class="text-gray-600">{{ $t('works.no_works', 'Zatím nemáte žádné zakázky') }}</p>
      </div>
    </div>

    <div v-else class="flex flex-wrap gap-4 md:gap-6">
      <div v-for="work in filteredWorks" :key="work.id" class="w-full sm:w-[calc(50%-0.75rem)] lg:w-[calc(33.333%-1rem)] min-w-[380px] max-w-[500px]">
        <work-show
          :work="work"
          @deleted="workDeleted"
          @updated="workUpdated"
        ></work-show>
      </div>
    </div>

    <!-- Work Form Modal using global styles -->
    <div v-if="showForm" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>{{ $t('works.new', 'Nová zakázka') }}</h3>
          <button @click="showForm = false" class="close-btn">×</button>
        </div>
        <div class="central-modal-content">
          <work-form @cancel="showForm = false" @saved="workSaved"></work-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import WorkForm from './WorkForm.vue';
import WorkShow from './WorkShow.vue';
import { Clock } from 'lucide-vue-next';

export default {
  components: {
    WorkForm,
    WorkShow, 
    Clock
  },
  data() {
    return {
      works: [],
      loading: true,
      error: null,
      showForm: false,
      // Filtering
      searchQuery: '',
      statusFilter: '',
      assignmentFilter: ''
    };
  },
  mounted() {
    this.fetchWorks();
  },
  computed: {
    filteredWorks() {
      let filtered = [...this.works];
      
      // Search filter
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(work => 
          work.title?.toLowerCase().includes(query) ||
          work.description?.toLowerCase().includes(query) ||
          work.location?.toLowerCase().includes(query)
        );
      }
      
      // Status filter
      if (this.statusFilter) {
        filtered = filtered.filter(work => work.status === this.statusFilter);
      }
      
      // Assignment filter
      if (this.assignmentFilter) {
        const currentUserContractId = this.$store?.state?.user?.currentContractId;
        
        if (this.assignmentFilter === 'my') {
          filtered = filtered.filter(work => 
            work.work_assignments?.some(a => a.contract_id === currentUserContractId)
          );
        } else if (this.assignmentFilter === 'unassigned') {
          filtered = filtered.filter(work => 
            !work.work_assignments || work.work_assignments.length === 0
          );
        }
      }
      
      return filtered;
    },
    hasActiveFilters() {
      return this.searchQuery || this.statusFilter || this.assignmentFilter;
    }
  },
  methods: {
    async fetchWorks() {
      this.loading = true;
      try {
        const response = await axios.get('/works/fetch');
        this.works = response.data;
      } catch (error) {
        this.error = error.response?.data?.error || this.$t('works.fetch_error', 'Chyba při načítání zakázek');
        const event = new CustomEvent('flashMessage', {
          detail: { text: this.error, type: 'error' }
        });
        document.dispatchEvent(event);
      } finally {
        this.loading = false;
      }
    },
    workSaved(work) {
      this.works.unshift(work);
      this.showForm = false;
      const event = new CustomEvent('flashMessage', {
        detail: { text: this.$t('works.created', 'Zakázka byla vytvořena'), type: 'notice' }
      });
      document.dispatchEvent(event);
    },
    workDeleted(workId) {
      this.works = this.works.filter(w => w.id !== workId);
      const event = new CustomEvent('flashMessage', {
        detail: { text: this.$t('works.deleted', 'Zakázka byla smazána'), type: 'notice' }
      });
      document.dispatchEvent(event);
    },
    workUpdated(updatedWork) {
      const index = this.works.findIndex(w => w.id === updatedWork.id);
      if (index !== -1) {
        this.works.splice(index, 1, updatedWork);
      }
      // Don't show message here - it's already shown by CentralModal on successful save
    },
    applyFilters() {
      // The filtering is handled by the computed property
      // This method is here for potential future enhancements
    },
    clearFilters() {
      this.searchQuery = '';
      this.statusFilter = '';
      this.assignmentFilter = '';
    }
  }
};
</script>