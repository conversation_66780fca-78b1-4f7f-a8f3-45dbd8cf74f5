<template>
  <nav class="header-nav">
    <div :class="['nav-item', { 'active': isActive('root') }]">
      <LocalizedLink :to="'/'" class="nav-link" :use-anchor="true">Přehled</LocalizedLink>
    </div>
    <div :class="['nav-item', { 'active': isActive('events') }]">
      <LocalizedLink :to="'/events'" class="nav-link" :use-anchor="true">Kalendář</LocalizedLink>
    </div>
    <div v-if="can('can_manage_contracts')" :class="['nav-item', { 'active': isActive('contracts') }]">
      <LocalizedLink :to="'/contracts'" class="nav-link" :use-anchor="true">Tým</LocalizedLink>
    </div>
  </nav>
</template>

<script>
import authorizationMixin from '../mixins/authorizationMixin';
import LocalizedLink from './LocalizedLink.vue';

export default {
  mixins: [authorizationMixin],
  components: {
    LocalizedLink
  },
  props: {
    currentPath: {
      type: String,
      default: '/'
    }
  },
  methods: {
    isActive(route) {
      if (route === 'root' && this.currentPath === '/') return true;
      return this.currentPath.includes(`/${route}`);
    }
  }
}
</script>