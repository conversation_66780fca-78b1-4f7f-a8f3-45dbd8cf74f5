/* Import Tailwind CSS directives */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
/* @tailwind base;
@tailwind components;
@tailwind utilities; */

@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Outfit:wght@100..900&display=swap');

/* This file will be processed by Vite and included in the application */

/* Typography */
.text-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.text-title-lg {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
}

.text-subtitle {
  font-size: 1rem;
  font-weight: 500;
  color: #374151;
}

.text-body {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
}

.text-muted {
  font-size: 0.875rem;
  color: #6b7280;
}

.text-caption {
  font-size: 0.75rem;
  color: #6b7280;
}

.text-link {
  color: #2563eb;
}
.text-link:hover {
  color: #1d4ed8;
}

.text-link-action {
  font-size: 0.875rem;
  color: #2563eb;
  font-weight: 500;
}

/* Buttons */
.btn-icon-subtle {
  color: #9ca3af;
}
.btn-icon-subtle:hover {
  color: #4b5563;
}

/* List items */
.item-list {
  display: flex;
  flex-direction: column;
  /* gap: 0.75rem; */
}

.item-list-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 0.375rem;
}

.avatar-placeholder-sm {
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  background-color: #f3f4f6;
}