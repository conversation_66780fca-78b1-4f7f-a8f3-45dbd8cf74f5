export default {
  computed: {
    userRoles() {
      return this.$store.state.userStore.roles || [];
    },
    firstRole() {
      return this.$store.getters['userStore/firstRole']; 
    },
    isOwner() {
      return this.$store.getters['userStore/isOwner'];
    },
    isAdmin() {
      return this.$store.getters['userStore/isAdmin'];
    },
    isSupervisor() {
      return this.$store.getters['userStore/isSupervisor'];
    },
    isManager() {
      return this.$store.getters['userStore/isManager'];
    },
    hasPlus() {
      return this.$store.getters['userStore/hasPlusPlan'];
    },
    hasPremium() {
      return this.$store.getters['userStore/hasPremiumPlan'];
    },
    currentPlan() {
      return this.$store.getters['userStore/currentPlan'];
    }
  },
  methods: {
    hasRole(role) {
      return this.$store.getters['userStore/hasRole'](role);
    },
    hasAnyRole(roles) {
      return this.$store.getters['userStore/hasAnyRole'](roles);
    },
    can(permission) {
      return this.$store.getters['userStore/can'](permission);
    }
  }
}