import axios from 'axios';
import i18n from '../i18n';

// Create a reusable axios instance for API calls
const api = axios.create({
  baseURL: '/api/v1',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Add a request interceptor to include the current locale
api.interceptors.request.use(config => {
  // Add the current locale as a header
  config.headers['Accept-Language'] = i18n.global.locale.value;
  return config;
});

export default api;