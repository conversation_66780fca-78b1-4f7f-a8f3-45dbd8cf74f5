import axios from 'axios';
import { sendFlashMessage } from './flashMessage';

axios.interceptors.response.use(
  response => {
    if (response.data && response.data.message) {
      const messageText = response.data.message;
      const messageType = response.data.messageType || (response.data.success === false ? 'error' : 'success');

      sendFlashMessage(messageText, messageType);

      // const { text, type } = response.data.message;
      // if (text) {
      //   sendFlashMessage(text, type || 'info');
      // }
    }
    return response;
  },
  error => {
    if (error.response && error.response.data) {
      if (error.response.data.message) {
        const messageType = error.response.data.messageType || 'error';
        sendFlashMessage(error.response.data.message, messageType);

        // const { text, type } = error.response.data.message;
        // if (text) {
        //   sendFlashMessage(text, type || 'error');
        // }

      } else if (error.response.data.errors) {
        const errorMessage = Array.isArray(error.response.data.errors) 
          ? error.response.data.errors.join(', ') 
          : Object.values(error.response.data.errors).flat().join(', ');
        
        sendFlashMessage(errorMessage, 'error');
      }
    } else {
      sendFlashMessage('Došlo k chybě při komunikaci se serverem.', 'error');
    }
    return Promise.reject(error);
  }
);

export default axios;