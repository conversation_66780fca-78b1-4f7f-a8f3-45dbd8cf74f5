export class DateFormatter {
  format(date = new Date(), locale = 'cs-CZ', options = { weekday: 'long', day: 'numeric', month: 'long' }) {
    const formatted = date.toLocaleDateString(locale, options);
    return formatted.charAt(0).toUpperCase() + formatted.slice(1);
  }
  
  // Generic methods that accept locale
  shortDate(date = new Date(), locale) {
    return this.format(date, locale, { day: 'numeric', month: 'numeric' });
  }
 
  longDate(date = new Date(), locale) {
    return this.format(date, locale, { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' });
  }
  
  // Legacy methods for backward compatibility
  shortDateCz(date = new Date()) {
    return this.shortDate(date, 'cs-CZ');
  }
 
  longDateCz(date = new Date()) {
    return this.longDate(date, 'cs-CZ');
  }
  
  shortDateEn(date = new Date()) {
    return this.format(date, 'en-US', { day: 'numeric', month: 'short' });
  }
 
  longDateEn(date = new Date()) {
    return this.format(date, 'en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' });
  }
  
  // Helper to convert i18n locale to browser locale format
  getLocaleString(locale) {
    const localeMap = {
      'cs': 'cs-CZ',
      'sk': 'sk-SK',
      'en': 'en-US'
    };
    return localeMap[locale] || locale;
  }
}

export const dateFormatter = new DateFormatter();

// Export helper function for use in components
export const getLocaleString = (locale) => dateFormatter.getLocaleString(locale);