import { createI18n } from 'vue-i18n';
import axios from 'axios';

export const SUPPORT_LOCALES = ['en', 'cs', 'sk'];

export function getStartingLocale() {
  // 1. Check for locale in URL path
  const pathSegments = window.location.pathname.split('/').filter(Boolean);
  console.log('URL locale', pathSegments)
  if (pathSegments.length > 0 && SUPPORT_LOCALES.includes(pathSegments[0])) {
    return pathSegments[0];
  }

  // 2. Check for locale in cookies
  const cookies = document.cookie.split(';');
  console.log('Checking cookies locale', cookies)
  for (let cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'locale' && SUPPORT_LOCALES.includes(value)) {
      return value;
    }
  }

  // 3. Fallback to browser language
  const browserLocale = navigator.language.split('-')[0];
  console.log('Checking browser locale', browserLocale)
  if (SUPPORT_LOCALES.includes(browserLocale)) {
    return browserLocale;
  }

  // 4. Check html lang attribute
  const htmlLang = document.documentElement.lang;
  console.log('Checking html lang attribute', htmlLang)
  if (htmlLang && SUPPORT_LOCALES.includes(htmlLang)) {
    return htmlLang;
  }

  // 5. Fallback to default locale
  return import.meta.env.VITE_DEFAULT_LOCALE || 'cs'; // Rails default_locale
}

export async function loadLocaleMessages(i18n, locale) {
  if (!i18n.global.availableLocales.includes(locale)) {
    try {
      const response = await axios.get(`/api/v1/translations/${locale}`);
      const messages = response.data;
      
      // The API now returns the 'front' keys directly at the root level
      i18n.global.setLocaleMessage(locale, messages);
      
      console.log(`Successfully loaded translations for ${locale}`);
      
    } catch (error) {
      console.error(`Failed to load messages for locale: ${locale}`, error);
      // Fallback to empty object so app doesn't crash
      i18n.global.setLocaleMessage(locale, {});
    }
  }
  return Promise.resolve();
}

const datetimeFormats = { // Basic example, align with your Rails formats
  en: {
    short: { year: 'numeric', month: 'short', day: 'numeric' },
    long: { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' },
  },
  cs: {
    short: { day: 'numeric', month: 'numeric', year: 'numeric' },
    long: { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' },
  },
  sk: {
    short: { day: 'numeric', month: 'numeric', year: 'numeric' },
    long: { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' },
  }
};

const numberFormats = { // Basic example
  en: { currency: { style: 'currency', currency: 'USD' } },
  cs: { currency: { style: 'currency', currency: 'CZK', currencyDisplay: 'code' } },
  sk: { currency: { style: 'currency', currency: 'EUR', currencyDisplay: 'code' } }
};

const i18n = createI18n({
  legacy: false, 
  locale: getStartingLocale(),
  fallbackLocale: import.meta.env.VITE_FALLBACK_LOCALE || 'cs',
  messages: {}, 
  datetimeFormats,
  numberFormats,
  missingWarn: import.meta.env.MODE !== 'production',
  fallbackWarn: import.meta.env.MODE !== 'production',
});

export default i18n; 