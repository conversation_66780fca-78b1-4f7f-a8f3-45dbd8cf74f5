<div class="min-h-screen bg-gray-50 flex flex-col items-center p-4 px-4 sm:px-6 lg:px-8">
  <div class="w-full max-w-md">
    <div class="text-center my-8">
      <%= image_tag "/logo-v7a.png", alt: 'Týmbox Logo', class: 'mx-auto h-20 w-auto' %>
    </div>

    <div class="bg-white py-8 px-6 shadow-xl rounded-xl sm:px-10">
      <h1 class="mt-4 mb-10 text-5xl font-black tracking-tight text-charcoal">
        <%= t("reset_password_instructions", default: "Vytvořte si nové heslo") %>
      </h1>
    
      <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put }) do |f| %>
        <%= f.hidden_field :reset_password_token %>
        <div class="mb-4">
          <%= render "devise/shared/error_messages", resource: resource %>
        </div>

        <div class="mb-5">
          <div class="relative">
            <%= f.password_field :password, autofocus: true, class: "block w-full px-4 py-3 pr-16 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-primary focus:border-green-primary sm:text-sm", placeholder: t("pass", default:'Nové heslo'), autocorrect:"off", autocapitalize:"off" %>
            
            <button type="button" class="pwd-field absolute inset-y-0 right-0 px-4 flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none" tabindex="-1" aria-label="Show password">
              <%= t("show_password", default: "ukázat") %>
            </button>
          </div>
        </div>

        <div class="mb-5">
          <div class="relative">
            <%= f.password_field :password_confirmation, class: "block w-full px-4 py-3 pr-16 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-primary focus:border-green-primary sm:text-sm", placeholder: t("confirmpassword", default:"Potvrzení nového hesla"), autocorrect:"off", autocapitalize:"off" %>
            <button type="button" class="pwd-field absolute inset-y-0 right-0 px-4 flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none" tabindex="-1" aria-label="Show password">
              <%= t("show_password", default: "ukázat") %>
            </button>
          </div>
        </div>

        <div class="mb-6">
          <%= f.submit t("change_password", default:'Změnit heslo'), class: "w-full flex justify-center !py-3 !px-4 border border-transparent !rounded-full shadow-sm text-base !font-medium text-white bg-green-primary hover:bg-green-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-primary" %>
        </div>
      <% end %>

      <div class="text-sm text-center space-y-2 mt-8">
        <p>
          <%= link_to t("login", default: "Přihlásit se"), new_session_path(resource_name), class: "font-medium text-gray-700 hover:text-green-primary hover:underline" %>
        </p>
        <% if devise_mapping.registerable? && controller_name != 'registrations' %>
          <p>
            <%= link_to t("free_registration_here", default: "Bezplatná registrace zde."), new_registration_path(resource_name), class: "font-medium text-gray-700 hover:text-green-primary hover:underline" %>
          </p>
        <% end %>
      </div>
    </div>
  </div>
</div>