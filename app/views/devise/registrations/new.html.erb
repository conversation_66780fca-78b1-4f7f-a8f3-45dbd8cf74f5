<div class="min-h-screen bg-gray-50 flex flex-col items-center p-4 px-4 sm:px-6 lg:px-8">
  <div class="w-full max-w-md">
    <div class="text-center my-8">
      <%= image_tag "/logo-v7a.png", alt: 'Týmbox Logo', class: 'mx-auto h-20 w-auto' %>
    </div>

    <div class="bg-white py-8 px-6 shadow-xl rounded-xl sm:px-10">
      <h1 class="mt-4 mb-10 text-5xl font-black tracking-tight text-charcoal text-center">
        <%= t 'signup', default: "Registrovat se" %>
      </h1>

      <%= form_for(resource, as: resource_name, url: registration_path(resource_name)) do |f| %>
        <div class="mb-4">
          <%= render "devise/shared/error_messages", resource: resource %>
        </div>

        <div class="mb-5">
          <%= f.email_field :email, autofocus: true, class: "block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-primary focus:border-green-primary sm:text-sm", autocomplete: "email", placeholder: t("youremail", default: "Váš e-mail"), autocorrect:"off", autocapitalize:"off" %>
        </div>

        <div class="mb-2">
          <div class="relative">
            <%= f.password_field :password, class: "block w-full px-4 py-3 pr-16 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-primary focus:border-green-primary sm:text-sm", autocomplete: "new-password", placeholder: t("yourpass", default: "Vaše heslo"), autocorrect:"off", autocapitalize:"off" %>
            <button type="button" class="pwd-field absolute inset-y-0 right-0 px-4 flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none" tabindex="-1" aria-label="Show password">
              <%= t("show_password", default: "ukázat") %>
            </button>
          </div>
        </div>
        <div class="mb-5 text-xs text-gray-500 pl-1">
          <% if @minimum_password_length %>
            (<%= t ("passlimit")%>)
          <% end %>
        </div>

        <div class="mb-5">
          <div class="relative">
            <%= f.password_field :password_confirmation, class: "block w-full px-4 py-3 pr-16 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-primary focus:border-green-primary sm:text-sm", autocomplete: "new-password", placeholder: t("confirmpassword", default: "Potvrzení hesla"), autocorrect:"off", autocapitalize:"off" %>
            <button type="button" class="pwd-field absolute inset-y-0 right-0 px-4 flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none" tabindex="-1" aria-label="Show password">
              <%= t("show_password", default: "ukázat") %>
            </button>
          </div>
        </div>

        <div class="mb-6">
          <%= f.submit t("signup"), class: "w-full flex justify-center !py-3 !px-4 border border-transparent !rounded-full shadow-sm text-base !font-medium text-white bg-green-primary hover:bg-green-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-primary" %>
        </div>

        <div class="text-xs text-gray-600 text-center space-y-1">
          <p>
            <%= t("tos", default: "Dokončením registrace souhlasíte s") %> <a href="#" data-terms="tos" class="terms-link underline hover:underline"><%= t("tos_link", default: "podmínkami poskytování bezplatné služby Týmbox") %></a>.
          </p>
          <p>
            <%= t("gdpr", default: "Vaše osobní údaje budeme zpracovávat podle našich") %> <a href="#" data-terms="gdpr" class="terms-link underline hover:underline"><%= t("gdpr_link", default: "zásad zpracování osobních údajů") %></a>.
          </p>
        </div>
      <% end %>

      <div class="text-sm text-center space-y-2 mt-8">
         <p>
          <%= link_to t("have_account", default: "Máte již účet? Přihlaste se") , new_user_session_path, class: "font-medium text-gray-700 hover:text-green-primary hover:underline" %>
        </p>
      </div>
    </div>
  </div>
</div>
