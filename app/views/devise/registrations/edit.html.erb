<div class="section">
  <div class="box">
    <div class="form-container">
      
      <h2><PERSON><PERSON><PERSON><PERSON><PERSON> heslo</h2>

      <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { method: :put, class: "styled-form" }) do |f| %>
        <%= render "devise/shared/error_messages", resource: resource %>

        <% if devise_mapping.confirmable? && resource.pending_reconfirmation? %>
          <div>Očekáváme potvrzení registrace: <%= resource.unconfirmed_email %></div>
        <% end %>

        <div class="form-group pwd-field col">
          <i>(ponechte prázdné pokud nechcete změnit)</i><br />
          <div class="pwd-input-wrapper">
            <%= f.password_field :password, class: "form-control pwd-input", autocomplete: "", placeholder:t("newpass", default:'Nové heslo'), autocorrect:"off", autocapitalize:"off" %>
            <button type="button" class="pwd-field" tabindex="-1" aria-label="Show password">ukázat</button>
          </div>
        </div>
        <div class="form-group pwd-field">
          <div class="pwd-input-wrapper">
            <%= f.password_field :password_confirmation, class: "form-control pwd-input", autocomplete: "", placeholder:t("confirmpassword", default:"Potvrzení hesla"), autocorrect:"off", autocapitalize:"off" %>
            <button type="button" class="pwd-field" tabindex="-1" aria-label="Show password">ukázat</button>
          </div>
        </div>

        <div class="form-group pwd-field">
          <i>(pro změnu je potřebné potvrzení současným heslem)</i><br />
          <div class="pwd-input-wrapper">
            <%= f.password_field :current_password, class: "form-control pwd-input", autocomplete: "", placeholder:t("currentpass", default:'Současné heslo'), autocorrect:"off", autocapitalize:"off" %>
            <button type="button" class="pwd-field" tabindex="-1" aria-label="Show password">ukázat</button>
          </div>
        </div>

        <div class="form-group">
          <%= f.submit t("update", default:'Změnit'), class: "submit-btn" %>
        </div>
      <% end %>
    </div>
  </div>
</div>
