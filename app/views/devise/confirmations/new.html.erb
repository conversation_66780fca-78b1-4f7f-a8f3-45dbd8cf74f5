<div class="min-h-screen bg-gray-50 flex flex-col items-center p-4 px-4 sm:px-6 lg:px-8">
  <div class="w-full max-w-md">
    <div class="text-center my-8">
      <%= image_tag "/logo-v7a.png", alt: 'Týmbox Logo', class: 'mx-auto h-20 w-auto' %>
    </div>

    <div class="bg-white py-8 px-6 shadow-xl rounded-xl sm:px-10">
      <h1 class="mt-4 mb-10 text-5xl font-black tracking-tight text-charcoal">
        <%= t("resend_confirmation_instructions", default: "Znovu odeslat potvrzovací instrukce") %>
      </h1>

      <%= form_for(resource, as: resource_name, url: confirmation_path(resource_name), html: { method: :post }) do |f| %>
        <div class="mb-4">
          <%= render "devise/shared/error_messages", resource: resource %>
        </div>

        <div class="mb-5">
          <%= f.email_field :email, autofocus: true, class: "block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-primary focus:border-green-primary sm:text-sm", autocomplete: "email", placeholder:"#{ t('youremail', default: 'Váš e-mail') }", value: (resource.pending_reconfirmation? ? resource.unconfirmed_email : resource.email), autocorrect:"off", autocapitalize:"off" %>
        </div>

        <div class="mb-6">
          <%= f.submit t("resend_confirmation_instructions", default: "Znovu odeslat potvrzovací instrukce"), class: "w-full flex justify-center !py-3 !px-4 border border-transparent !rounded-full shadow-sm text-base !font-medium text-white bg-green-primary hover:bg-green-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-primary" %>
        </div>
      <% end %>

      <div class="text-sm text-center space-y-2 mt-8">
        <p>
          <%= link_to t("signin", default: "Přihlásit se"), new_session_path(resource_name), class: "font-medium text-gray-700 hover:text-green-primary hover:underline" %>
        </p>
        <% if devise_mapping.registerable? && controller_name != 'registrations' %>
          <p>
            <a href="<%= new_registration_path(resource_name) %>" class="font-medium text-gray-700 hover:text-green-primary hover:underline"><%= t("free_registration_here", default: "Bezplatná registrace zde.") %></a>
          </p>
        <% end %>
        <% if devise_mapping.lockable? && resource_class.unlock_strategy_enabled?(:email) && controller_name != 'unlocks' %>
          <p>
            <%= link_to t("did_not_receive_unlock_instructions", default: "Nedostali jste odemykací instrukce?"), new_unlock_path(resource_name), class: "font-medium text-gray-700 hover:text-green-primary hover:underline" %>
          </p>
        <% end %>
      </div>
    </div>
  </div>
</div>