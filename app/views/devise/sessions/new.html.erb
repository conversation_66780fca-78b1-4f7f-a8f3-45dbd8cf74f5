<div class="min-h-screen bg-gray-50 flex flex-col items-center p-4 px-4 sm:px-6 lg:px-8">
  <div class="w-full max-w-md">
    <div class="text-center my-8">
      <%= image_tag "/logo-v7a.png", alt: 'Týmbox Logo', class: 'mx-auto h-20 w-auto' %>
    </div>

    <div class="bg-white py-8 px-6 shadow-xl rounded-xl sm:px-10">
      <h1 class="mt-4 mb-10 text-5xl font-black tracking-tight text-charcoal">
        <%= t('signin') %>
      </h1>

      <%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>
        <div class="mb-4">
          <%= render "devise/shared/error_messages", resource: resource %>
        </div>

        <div class="mb-5">
          <%= f.email_field :email, autofocus: true, class: "block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-primary focus:border-green-primary sm:text-sm", autocomplete: "email", placeholder:"#{ t('youremail', default: 'Váš e-mail') }", autocorrect:"off", autocapitalize:"off" %>
        </div>

        <div class="mb-5">
          <div class="relative">
            <%= f.password_field :password, class: "block w-full px-4 py-3 pr-16 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-primary focus:border-green-primary sm:text-sm", placeholder:"#{ t(  'yourpass', default: 'Vaše heslo') }", autocorrect:"off", autocapitalize:"off" %>
            <button type="button" class="pwd-field absolute inset-y-0 right-0 px-4 flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none" tabindex="-1" aria-label="Show password">
              <%= t('show_password') %>
            </button>
          </div>
        </div>

        <div class="mb-5 flex items-center">
          <%= f.check_box :remember_me, checked: true, class: "h-4 w-4 text-green-primary focus:ring-green-secondary border-gray-300 rounded" %>
          <%= f.label :remember_me, t("rememberme", default: "Zapamatovat si mě"), class: "ml-2 block text-sm text-gray-700" %>
        </div>

        <div class="mb-6">
          <%= f.submit t("signin"), class: "w-full flex justify-center !py-3 !px-4 border border-transparent !rounded-full shadow-sm text-base !font-medium text-white bg-green-primary hover:bg-green-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-primary" %>
        </div>
      <% end %>

      <div class="text-sm text-center space-y-2">
        <p>
          <a href="<%= new_user_registration_path %>" class="font-medium hover:underline"><%= t('free_registration_here', default: 'Bezplatná registrace zde.') %></a>
        </p>
        <p>
          <%= link_to t('forgot_password', default: 'Zapoměli ste heslo?'), new_user_password_path, class: "font-medium hover:underline" %>
        </p>
        <% if devise_mapping.confirmable? && controller_name != 'confirmations' %>
          <p>
            <%= link_to t('did_not_receive_confirmation_instructions', default: 'Nedostali jste potvrzovací instrukce?'), new_confirmation_path(resource_name), class: "font-medium hover:underline" %>
          </p>
        <% end %>
      </div>
    </div>
  </div>
</div>