<div class="min-h-screen bg-gray-50 flex flex-col items-center p-4 px-4 sm:px-6 lg:px-8">
  <div class="w-full max-w-md">
    <div class="text-center my-8">
      <%= image_tag "/logo-v7a.png", alt: 'Týmbox Logo', class: 'mx-auto h-20 w-auto' %>
    </div>

    <div class="bg-white py-8 px-6 shadow-xl rounded-xl sm:px-10">
      <div class="flex flex-col items-center justify-start mb-6">
        <div class="inline-flex items-center justify-center rounded-full w-20 h-20 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#27A844" stroke-width="4" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        </div>  
        <h1 class="mt-4 text-5xl font-black tracking-tight text-charcoal text-center">
          Vítejte v Týmboxu!
        </h1>
        <p class="text-charcoal mt-4 text-center">Vaše pozvání bylo úspěšně přijato.</p>
        <p class="text-charcoal mt-2 text-center text-sm">Týmbox Vám umožní jednoduše sledovat docházku, plánovat schůzky a organizovat práci - vše na jednom místě, bez zbytečných složitostí.</p>
        <h3 class="text-xl mt-6 mb-2 font-semibold">Pro dokončení registrace si nastavte heslo</h3>
      </div>

      <%= form_for(resource, as: resource_name, url: invitation_path(resource_name), html: { method: :put }) do |f| %>
        <div class="mb-4">
          <%= render "devise/shared/error_messages", resource: resource %>
        </div>
        <%= f.hidden_field :invitation_token, readonly: true %>

        <% if f.object.class.require_password_on_accepting %>
          <div class="mb-5">
            <div class="relative">
              <%= f.password_field :password, class: "block w-full px-4 py-3 pr-16 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-primary focus:border-green-primary sm:text-sm", placeholder: "Zvolte silné heslo" %>
              <button type="button" class="pwd-field absolute inset-y-0 right-0 px-4 flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none" tabindex="-1" aria-label="Show password">
                ukázat
              </button>
            </div>
          </div>

          <div class="mb-5">
            <div class="relative">
              <%= f.password_field :password_confirmation, class: "block w-full px-4 py-3 pr-16 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-primary focus:border-green-primary sm:text-sm", placeholder: "Zadejte heslo znovu" %>
              <button type="button" class="pwd-field absolute inset-y-0 right-0 px-4 flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none" tabindex="-1" aria-label="Show password">
                ukázat
              </button>
            </div>
          </div>
        <% end %>

        <div class="mb-6 text-center">
          <%= f.submit "Dokončit registraci", class: "w-full sm:w-auto inline-flex justify-center !py-3 !px-6 border border-transparent !rounded-full shadow-sm text-base !font-medium text-white bg-green-primary hover:bg-green-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-primary" %>
        </div>
        
        <div class="mt-6 text-gray-600 text-xs text-center">
          Dokončením registrace získáte přístup ke funkcím Týmboxu a souhlasíte s <a href="#" data-terms="tos" class="terms-link underline hover:underline">podmínkami poskytování bezplatné služby Týmbox</a>.
          Vaše osobní údaje budeme zpracovávat podle našich <a href="#" data-terms="gdpr" class="terms-link underline hover:underline">zásad zpracování osobních údajů</a>.
        </div>
      <% end %>
    </div>
  </div>
</div>