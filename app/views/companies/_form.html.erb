<%= form_with(model: company, class:"styled-form") do |form| %>
  <%= render 'shared/error_messages', resource: company %>

  <div class="form-group">
    <%= form.text_field :name, class: "form-control", autocomplete: "name", placeholder:t("name", default:'Název'), autocorrect:"off", autocapitalize:"off" %>
  </div>

  <div class="form-group">
    <%= form.text_field :address, class: "form-control", autocomplete: "address", placeholder: t("address", default: 'Adresa'), autocorrect: "off", autocapitalize: "off" %>
  </div>

  <div class="form-group">
    <%= form.text_field :phone, class: "form-control", autocomplete: "phone", placeholder: t("phone", default: 'Telefon'), autocorrect: "off", autocapitalize: "off" %>
  </div>
  
  <div class="form-group">
    <%= form.text_field :web, class: "form-control", autocomplete: "web", placeholder: t("web", default: 'Web'), autocorrect: "off", autocapitalize: "off" %>
  </div>

  <div class="form-group">
    <%= form.text_area :description, class: "form-control", placeholder: t("description", default: 'Popis') %>
  </div>

  <div class="form-group">
    <%= form.submit 'Uložit', class: 'submit-btn' %>
  </div>
<% end %>
