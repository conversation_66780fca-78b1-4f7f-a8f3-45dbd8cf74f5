<div class="form-container">
  <%= form_with model: current_user, url: force_password_change_path, method: :patch, local: true, class: "styled-form" do |f| %>
    <div class="form-group pwd-field">
      <%= f.password_field :password, class: "form-control pwd-input", placeholder:t("pass", default:'<PERSON><PERSON><PERSON>'), autocorrect:"off", autocapitalize:"off" %>
      <button type="button" class="pwd-toggle" tabindex="-1" aria-label="Show password">ukázat</button>
    </div>
    <div class="form-group pwd-field">
      <%= f.password_field :password_confirmation, class: "form-control pwd-input", placeholder:t("confirmpassword", default:"Potvrzení hesla"), autocorrect:"off", autocapitalize:"off" %>
      <button type="button" class="pwd-toggle" tabindex="-1" aria-label="Show password">ukázat</button>
    </div>

    <div class="form-group">
      <%= f.submit t("update", default:'Změnit'), class: "submit-btn" %>
    </div>
  <% end %>
</div>

<%= render "devise/shared/links" %>