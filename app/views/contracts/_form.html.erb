<%= form_with(model: contract, class:"styled-form") do |form| %>
  <%= render 'shared/error_messages', resource: contract %>

  <div class="form-group">
    <%= form.text_field :first_name, class: "form-control", placeholder:t("firstname", default:'<PERSON><PERSON><PERSON>'), autocorrect:"off", autocapitalize:"off" %>
  </div>

  <div class="form-group">
    <%= form.text_field :last_name, class: "form-control", placeholder:t("secondname", default:'Příjmení'), autocorrect:"off", autocapitalize:"off" %>
  </div>

  <div class="form-group">
    <%= form.text_field :job_title, class: "form-control", placeholder:t("job_title", default:'Pracovní titul'), autocorrect:"off", autocapitalize:"off" %>
  </div>

  <div class="form-group">
    <%= form.text_field :contract_type, class: "form-control", autocomplete: "Pracovní smlouva", placeholder:t("contract_type", default:'Typ pracovního poměru'), autocorrect:"off", autocapitalize:"off" %>
  </div>

    <div class="form-group">
      <%= form.text_field :phone, class: "form-control", placeholder:t("telefone", default:'Telefon'), autocorrect:"off", autocapitalize:"off" %>
    </div>

  <% if contract.new_record? %>
    <div class="left">
      <h4>Pozvání na e-mail</h4>
      <p>Na uvedený e-mail odešleme pozvánku na připojení sa k Vašemu pracovnímu prostoru.</p>
      <p>Když chcete přidat sami sebe, ponechte prázdné.</p>
    </div>
    
    <div class="form-group">
      <%= form.text_field :email, class: "form-control", placeholder:t("email", default:'E-mail'), autocorrect:"off", autocapitalize:"off" %>
    </div>
  <% end %>

  <div class="form-group">
    <%= form.submit 'Uložit', class: 'submit-btn' %>
  </div>
<% end %>
