<!DOCTYPE html>
<html lang="cs">
  <head>
    <% if Rails.env.development? %>
      <title>DEVS VERSION Týmbox.cz</title>
    <% else %>
      <title>Týmbox.cz | Online docházkový systém</title>
    <% end %>
    
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <meta name="description" content="Online docházkový systém zdarma. Mobilní docházka bez instalace, měsíční výkazy práce online a šablony na docházku pro malé firmy.">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<%= request.original_url %>">
    <meta property="og:title" content="Týmbox.cz | Online docházkový systém">
    <meta property="og:description" content="Online docházkový systém zdarma. Mobilní docházka bez instalace, měs<PERSON><PERSON><PERSON><PERSON> výka<PERSON> pr<PERSON>ce.">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<%= request.original_url %>">
    <meta property="twitter:title" content="Týmbox.cz | Online docházkový systém">
    <meta property="twitter:description" content="Online docházkový systém zdarma. Mobilní docházka bez instalace.">

    <!-- Canonical URL -->
    <link rel="canonical" href="<%= request.original_url %>">

    <!-- Keywords -->
    <meta name="keywords" content="docházkový systém cena, online docházka ceník, cena evidence docházky, docházkový systém zdarma, Týmbox ceník">

    <%= vite_client_tag %>
    <%= vite_javascript_tag 'welcome' %>

    <link rel="icon" type="image/png" href="/favicon.ico"> 
    <!-- Google tag (gtag.js) -->
    <script async defer src="https://www.googletagmanager.com/gtag/js?id=G-HWE817PRNM"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-HWE817PRNM');
    </script>
  </head>

  <div id="flash-messages" 
    data-messages="<%= {
      notice: flash[:notice],
      alert: flash[:alert],
      error: flash[:error]
    }.to_json %>">
  </div>

  <body class="bg-gray-50">
    <div>
      <main class="">
        <%= yield %>
      </main>

      <div id="cookie-consent"></div>

    </div>

  </body>
</html>