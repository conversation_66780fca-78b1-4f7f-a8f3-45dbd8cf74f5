<!DOCTYPE html>
<html lang="<%= I18n.locale %>">
  <head>
    <% if Rails.env.development? %>
      <title>Týmbox <%= controller_name %> - <%= action_name %></title>
    <% else %>
      <title>Online docházkový systém zdarma | Týmbox | Docházka do mobilu</title>
    <% end %>
    <meta name="description" content="Online docházka zdarma pro malé týmy. Jednoduchý docházkový systém do mobilu bez instalace. Zaznamenávejte pracovní dobu odkudkoliv s Týmboxem.">

    <meta property="og:type" content="website">
    <meta property="og:url" content="<%= request.original_url %>">
    <meta property="og:title" content="Docházkový systém zdarma | Online evidence pracovní doby">
    <meta property="og:description" content="Jednoduchý online docházkový systém zdarma. Mobilní docházka bez instalace, měsíční výkazy práce online.">

    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<%= request.original_url %>">
    <meta property="twitter:title" content="Docházkový systém zdarma | Online evidence pracovní doby">
    <meta property="twitter:description" content="Jednoduchý online docházkový systém zdarma. Mobilní docházka bez instalace.">

    <link rel="canonical" href="<%= request.original_url %>">
    
    <meta name="keywords" content="docházkový systém zdarma, online docházka, evidence pracovní doby, mobilní docházka, měsíční výkazy práce, Týmbox">

    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= vite_client_tag %>
    <%= vite_javascript_tag 'application' %>
    <%#= vite_stylesheet_tag 'application' %>

    <% if content_for?(:head_tags) %>
      <%= yield :head_tags %>
    <% end %>
    
    <link rel="icon" type="image/png" href="/favicon.ico"> 
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-HWE817PRNM"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-HWE817PRNM');
    </script>
  </head>

  <body>
    <% if user_signed_in? %>
      <!-- Flash Messages (always visible) -->
      <div id="flash-messages" 
        data-messages="<%= {
          notice: flash[:notice],
          alert: flash[:alert],
          error: flash[:error],
          analytics_event: flash[:analytics_event]
        }.to_json %>">
      </div>
      
      <!-- Application layout -->
      <div class="flex h-screen bg-gray-50">
        <% 
          user_role = current_user.company_user_roles.find_by(company: current_tenant)&.role&.name || 'employee'
          current_plan = current_tenant&.current_plan&.name || 'free'
        %>
        <div 
          id="sidebar-app"
          data-root-path="<%= root_path %>"
          data-logo-path="/logo-v7a.png"
          data-daily-logs-path="<%= daily_logs_path %>"
          data-events-path="<%= events_path %>"
          data-works-path="<%= works_path %>"
          data-bookings-path="<%= bookings_path %>"
          data-meetings-path="<%= meetings_path %>"
          data-report-daily-logs-path="<%= daily_logs_report_path %>"
          data-owner-reports-path="<%= daily_logs_owner_reports_path %>"
          data-team-summary-daily-logs-path="<%= team_summary_daily_logs_path %>"
          data-contracts-path="<%= contracts_path %>"
          data-company-connections-path="<%= company_connections_path %>"
          data-user-settings-path="<%= user_settings_edit_path %>"
          data-reports-activities-path="<%= reports_activities_path %>"
          data-destroy-user-session-path="<%= destroy_user_session_path %>"
          data-current-page-path="<%= request.path %>"
          data-current-controller-name="<%= controller_name %>"
          data-current-action-name="<%= action_name %>"
          data-user-email="<%= current_user.email %>"
          data-is-working="<%= current_user.daily_logs.is_working.exists? %>"
          data-user-role="<%= user_role %>"
          data-current-plan-name="<%= current_plan %>"
          data-csrf-token="<%= form_authenticity_token %>"
        >
          <!-- Vue Sidebar Component will mount here -->
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden">
          <!-- Top Bar Mount Point -->
          <div 
            id="topbar-app"
            data-company-name="<%= current_tenant&.name %>"
            data-current-plan-name="<%= current_plan %>"
            data-user-role="<%= user_role %>"
            data-company-connections-path="<%= company_connections_path %>"
            data-team-summary-path="<%= team_summary_daily_logs_path %>" <%# Assuming team_summary link goes here %>
            data-new-work-path="<%= new_work_path %>"
          >
            <!-- Vue Topbar Component will mount here -->
          </div>

          <!-- Content area -->
          <div class="content-area">
            <%= yield %>
          </div>
        </div>
      </div>
    <% else %>
      <!-- Not logged in - show flash messages and content -->
      <div id="flash-messages" 
        data-messages="<%= {
          notice: flash[:notice],
          alert: flash[:alert],
          error: flash[:error],
          analytics_event: flash[:analytics_event]
        }.to_json %>">
      </div>
      
      <div id="cookie-consent"></div>
      
      <main>
        <%= yield %>
      </main>
      
      <footer class="footer">
        <div class="beta-box">© 2025 Týmbox<sup><span class="beta-tag">beta</sup></span></div>
        Protože na vašem čase záleží.
      </footer>
    <% end %>
    <div id="central-modal-app"></div>
  </body>
</html>