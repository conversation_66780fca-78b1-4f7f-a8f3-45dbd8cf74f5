<%= form_with(model: user_profile, class:"styled-form") do |form| %>
  <%= render 'shared/error_messages', resource: user_profile %>

  <div class="form-group left">
    <p>E-mail <strong><%= @user_profile.user.email %></strong> v aplikaci není možné změnit. Máte-li zájem o změnu e-mailu, prosím kontaktujte nás. </p>
  </div>

  <div class="form-group">
    <%= form.text_field :title_prefix, class: "form-control", placeholder:t("prefix", default:'Titul před jménem'), autocorrect:"off", autocapitalize:"on" %>
  </div>
  <div class="form-group">
    <%= form.text_field :first_name, class: "form-control", placeholder:t("first_name", default:'J<PERSON>no'), autocorrect:"off", autocapitalize:"on" %>
  </div>
  <div class="form-group">
    <%= form.text_field :last_name, class: "form-control", placeholder:t("last_name", default:'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'), autocorrect:"off", autocapitalize:"on" %>
  </div>
  <div class="form-group">
    <%= form.text_field :title_suffix, class: "form-control", placeholder:t("prefix", default:'Titul za jménem'), autocorrect:"off", autocapitalize:"on" %>
  </div>

  <div class="form-group">
    <%= form.submit 'Uložit', class: 'btn submit bl' %>
  </div>
<% end %>


