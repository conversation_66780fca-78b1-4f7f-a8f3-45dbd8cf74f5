# Part of the timezone refactor
module DateT<PERSON><PERSON>el<PERSON>
  def self.beginning_of_day(date)
    date.in_time_zone.beginning_of_day
  end
  
  def self.end_of_day(date)
    date.in_time_zone.end_of_day
  end
  
  def self.day_range(date)
    beginning_of_day(date)..end_of_day(date)
  end
  
  def self.same_day?(time1, time2)
    time1.in_time_zone.to_date == time2.in_time_zone.to_date
  end
  
  def self.safe_parse_date(date_string)
    return nil unless date_string =~ /^\d{4}-\d{2}-\d{2}$/
    Date.parse(date_string)
  rescue ArgumentError
    nil
  end
end