class ContractPolicy < ApplicationPolicy

  # Helper methods for reusable role checks
  def user_role
    @user_role ||= user.role_in(record.company)
  end

  def user_is_owner?
    user_role&.name == "owner"
  end

  def has_advanced_role?
    user_role&.name.in?(["supervisor", "admin"])
  end

  def is_contract_user?
    user.id == record.user_id
  end

  # Plan permissions (leveraging CompanyPolicy)
  def advanced_roles_enabled?
    allowed_to?(:advanced_roles_enabled?, record.company)
  end


  # Contract policy: 
  # If the user is the company owner OR (user has advanced role
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  def index?
    user_is_owner? || (has_advanced_role? && advanced_roles_enabled?)
  end

  # Contract policy: 
  # If the user is the company owner OR (user has advanced role
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  # OR the user is contract.user
  def show?
    is_contract_user? || user_is_owner? || (has_advanced_role? && advanced_roles_enabled?)
  end

  # Contract policy: 
  # If the user is the company owner OR (user has advanced role
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  def manage?
    user_is_owner? || (has_advanced_role? && advanced_roles_enabled?)
  end

  alias_method :edit?, :manage?
  alias_method :update?, :manage?

  # Actions that cannot be performed on own contract
  # Contract policy: 
  # If the user is the company owner OR (user has advanced role
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  # AND the user is not the contract.user
  def suspend?
    return false if is_contract_user?
    return false if contract_user_is_owner?
    manage?
  end
  
  # Contract policy: 
  # If the user is the company owner OR (user has advanced role
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  # AND the user is not the contract.user
  def terminate?
    return false if is_contract_user?
    return false if contract_user_is_owner?
    manage?
  end
  
  # Contract policy: 
  # If the user is the company owner OR (user has advanced role
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  # AND the user is not the contract.user
  def reactivate? 
    return false if is_contract_user?
    manage?
  end

  # Contract policy: 
  # If the user is the company owner OR (user has advanced role
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  # AND the user is not the contract.user
  def destroy?
    return false if is_contract_user?
    manage?
  end

  private

  def contract_user_is_owner?
    return false unless record.user
    record.user.has_role?("owner", record.company)
  end

  # TODO: Refactor according to the example in the company_policy
  # by using reusable methods and/or creating new ones to be reused in the contract policy 
  # specifically when required
  # def has_administrative_permission?
  #   role_name = user.role_in(record.company)&.name
    
  #   # Owner can always perform these actions
  #   return true if role_name == "owner"
    
  #   # For advanced roles, check if company has the right plan
  #   if role_name == "supervisor"
  #     return allowed_to?(:can_use_role?, record.company, with: {role_name: role_name})
  #   end
    
  #   if role_name == "admin"
  #     return allowed_to?(:can_use_role?, record.company, with: {role_name: role_name})
  #   end
    
  #   false
  # end



end