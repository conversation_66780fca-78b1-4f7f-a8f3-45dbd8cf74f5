class CompanyPolicy < ApplicationPolicy
  
  attr_reader :meeting # Add reader for meeting context

  # Refactored correct policies START HERE ------------------------
  def user_role
    @user_role ||= user.role_in(record)
  end

  def plus_plan?
    %w[premium plus].include?(record.current_plan&.name)
  end

  def premium_plan?
    %w[premium].include?(record.current_plan&.name)
  end

  def has_advanced_role?
    user_role&.name.in?(["supervisor", "admin"])
  end
    
  def user_is_owner?
    user_role&.name == "owner"
  end

  def advanced_roles_enabled?
    plus_plan? || premium_plan?
  end

  def view_employee_reports?
    user_is_owner? || has_advanced_role?
  end

  def can_assign_role?(role_name)
    return true if role_name.in?(['owner', 'employee'])
    return true if role_name.in?(["admin", "supervisor"]) && plus_plan?
    return true if premium_plan?
    false
  end

  def can_use_role?(role_name)
    return true if role_name.in?(['owner', 'employee'])
    return true if role_name.in?(["admin", "supervisor"]) && plus_plan?
    return true if premium_plan?
    false
  end

  # Allow any authenticated user associated with the company to view the colleague list
  def view_colleagues?
    user.company_user_roles.where(company: record).exists?
  end

  # Manage contract:
  # edit, update, destroy, suspend, terminate, reactivate, 
  # resend_invitation, assign_role_to_user
  # Can the user manage contracts? (e.g., owner, admin, supervisor with permission)
  def manage_contract?
    # Check role via CompanyUserRole association
    role_name = user.role_in(record)&.name # Use existing helper if available, or query directly
    role_name == 'owner' || (advanced_roles_enabled? && ['admin', 'supervisor'].include?(role_name))
  end

  # Manage meetings:
  # If the company has a plus or premium plan
  # # AND if the user is the owner OR (has advanced role AND the company has a plus or premium plan)
  # def manage_meetings?
  #   return false unless plus_plan?
  #   return true if user_is_owner?
  #   return false unless advanced_roles_enabled?
  #   has_advanced_role?
  # end

  # Meeting specific actions:
  # resend_invitation, extend_meeting_dates, get_best_options, confirm, destroy
  # If meeting is provided, check if user is the creator
  
  def manage_meeting_actions? 
    return false unless plus_plan? 
    return true if user_is_owner?
    return true if has_advanced_role?
    false
  end

  # Create meetings:
  # Any company user (owner, manager, employee) can create meetings
  def create_meeting?
    user_role.present? # Any user with a role in the company can create meetings
  end

  # Manage subscriptions:
  # If the user is the owner to manage the company OR (user has advanced role
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  def manage_subscription?
    return true if user_is_owner?
    return false unless advanced_roles_enabled?
    has_advanced_role?
  end

  # View bookings: 
  # Any authenticated user with role in company can view bookings
  def view_bookings?
    user_role.present?
  end

  # Manage bookings: 
  # If the company has a plus or premium plan
  def manage_bookings?
    plus_plan?
  end

  # Manage booking actions (update, confirm, cancel, convert_to_work)
  # If the company has a plus or premium plan
  # AND if the user is the owner OR has advanced role
  def manage_booking_actions?
    return false unless plus_plan?
    return true if user_is_owner?
    return true if has_advanced_role?
    false
  end

  # Receive daily email with team at work
  # If the company has a plus or premium plan
  # AND if the user is the owner OR (has advanced role AND the company has a plus or premium plan)
  def receive_team_status_emails?
    return false unless plus_plan?
    return true if user_is_owner?
    return false unless advanced_roles_enabled?
    has_advanced_role?
  end

  # Company management authoriztion methods
  # If the user is the owner to manage the company OR (has advanced role 
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  # AND Company has to have a plus or premium plan to use new method
  def new?
    return false unless plus_plan? 
    return true if user_is_owner?
    return false unless advanced_roles_enabled?
    has_advanced_role?
  end

  def manage_logo?
    return false unless plus_plan? 
    return true if user_is_owner?
    return false unless advanced_roles_enabled?
    has_advanced_role?
  end

  def create?
    new?
  end

  # If the user is the owner to manage the company OR (has advanced role 
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  def manage?
    return true if user_is_owner?
    return false unless advanced_roles_enabled?
    has_advanced_role?
  end

  def team_summary?
    manage? 
  end
  
  # Allow employees to manage works through company policy
  def manage_works?
    user_role&.name.in?(["owner", "employee", "supervisor", "admin"])
  end

  alias_method :index?, :manage?
  alias_method :show?, :manage?
  alias_method :edit?, :manage?
  alias_method :update?, :manage?
  alias_method :destroy?, :manage?
 
end
