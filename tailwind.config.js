/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './app/views/**/*.html.erb',
    './app/helpers/**/*.rb',
    './app/frontend/**/*.js',
    './app/frontend/**/*.vue',
  ],
  theme: {
    extend: {
      colors: {
        'primary': '#3b82f6',
        'primary-hover': '#2563eb',
        'primary-light': '#dbeafe',
        'primary-text': '#2563eb',
        'success': '#22c55e',
        'success-light': '#dcfce7',
        'warning': '#eab308',
        'warning-light': '#fef9c3',
        'danger': '#ef4444',
        'danger-light': '#fee2e2',
        'gray-50': '#f9fafb',
        'gray-100': '#f3f4f6',
        'gray-200': '#e5e7eb',
        'gray-300': '#d1d5db',
        'gray-400': '#9ca3af',
        'gray-500': '#6b7280',
        'gray-600': '#4b5563',
        'gray-700': '#374151',
        'gray-800': '#1f2937',
        'gray-900': '#111827',
      }
    },
  },
  plugins: [],
} 