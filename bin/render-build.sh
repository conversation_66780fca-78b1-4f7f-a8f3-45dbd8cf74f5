#!/usr/bin/env bash
# exit on error
set -o errexit

gem update --system

bundle install
npm install
# bundle exec rails assets:precompile
# bundle exec rails assets:clean
bundle exec vite build

# If you're using a Free instance type, you need to
# perform database migrations in the build command.
# Uncomment the following line:

bundle exec rails db:migrate

bundle exec rails db:seed

# If you want to ensure ImageMagick is available, you can include installation commands in your 
# Render service's build script section in the render.yaml file or in the service's dashboard settings:
# apt-get update && apt-get install -y imagemagick