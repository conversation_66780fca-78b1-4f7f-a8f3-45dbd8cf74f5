import { defineConfig } from 'vite'
import RubyPlugin from 'vite-plugin-ruby'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [
    RubyPlugin(),
    vue()
  ],
  server: {
    hmr: {
      // Enable HMR
      overlay: true,
    },
  },
  build: {
    // Ensure CSS is included in the build (Removed, let Vite handle splitting)
    // cssCodeSplit: false, 
    // Ensure CSS is processed correctly
    // cssMinify: 'lightningcss',
  }
})
