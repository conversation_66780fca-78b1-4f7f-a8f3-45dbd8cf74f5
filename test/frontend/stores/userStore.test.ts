import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createStore } from 'vuex'
import userStore from '@/store/userStore'
import axios from 'axios'

// Mock axios
vi.mock('axios')

describe('userStore', () => {
  let store

  beforeEach(() => {
    store = createStore({
      modules: {
        user: userStore,
      },
    })
    vi.clearAllMocks()
  })

  describe('mutations', () => {
    it('setUserRole sets the user role', () => {
      store.commit('user/setUserRole', 'admin')
      expect(store.state.user.role).toBe('admin')
    })

    it('setUserRoles handles array of roles', () => {
      store.commit('user/setUserRoles', ['owner', 'admin'])
      expect(store.state.user.roles).toEqual(['owner', 'admin'])
    })

    it('setUserRoles handles single role', () => {
      store.commit('user/setUserRoles', 'owner')
      expect(store.state.user.roles).toEqual(['owner'])
    })

    it('setUserRoles handles array with all values', () => {
      store.commit('user/setUserRoles', ['owner', 'admin', 'employee'])
      expect(store.state.user.roles).toEqual(['owner', 'admin', 'employee'])
    })

    it('setPermissions sets permissions object', () => {
      const permissions = { canEdit: true, canDelete: false }
      store.commit('user/setPermissions', permissions)
      expect(store.state.user.permissions).toEqual(permissions)
    })

    it('setPermissions handles null permissions', () => {
      store.commit('user/setPermissions', null)
      expect(store.state.user.permissions).toEqual({})
    })

    it('setEmail sets user email', () => {
      store.commit('user/setEmail', '<EMAIL>')
      expect(store.state.user.email).toBe('<EMAIL>')
    })

    it('setPlanInfo sets plan information', () => {
      const planInfo = {
        hasPlusPlan: true,
        hasPremiumPlan: false,
        currentPlan: 'plus',
      }
      store.commit('user/setPlanInfo', planInfo)
      expect(store.state.user.hasPlusPlan).toBe(true)
      expect(store.state.user.hasPremiumPlan).toBe(false)
      expect(store.state.user.currentPlan).toBe('plus')
    })
  })

  describe('getters', () => {
    beforeEach(() => {
      store.commit('user/setUserRoles', ['owner', 'admin'])
      store.commit('user/setPermissions', {
        canEdit: true,
        canDelete: false,
        canView: true,
      })
    })

    it('firstRole returns the first role', () => {
      store.commit('user/setUserRole', 'owner')
      expect(store.getters['user/firstRole']).toBe('owner')
    })

    it('hasRole checks if user has specific role', () => {
      expect(store.getters['user/hasRole']('owner')).toBe(true)
      expect(store.getters['user/hasRole']('admin')).toBe(true)
      expect(store.getters['user/hasRole']('employee')).toBe(false)
    })

    it('hasAnyRole checks if user has any of the specified roles', () => {
      expect(store.getters['user/hasAnyRole'](['employee', 'owner'])).toBe(true)
      expect(store.getters['user/hasAnyRole'](['employee', 'supervisor'])).toBe(false)
    })

    it('isOwner returns true for owner role', () => {
      expect(store.getters['user/isOwner']).toBe(true)
    })

    it('isAdmin returns true for admin role', () => {
      expect(store.getters['user/isAdmin']).toBe(true)
    })

    it('isSupervisor returns false when user is not supervisor', () => {
      expect(store.getters['user/isSupervisor']).toBe(false)
    })

    it('isManager returns true for owner, admin, or supervisor', () => {
      expect(store.getters['user/isManager']).toBe(true)
      
      // Test with only supervisor role
      store.commit('user/setUserRoles', ['supervisor'])
      expect(store.getters['user/isManager']).toBe(true)
      
      // Test with employee role
      store.commit('user/setUserRoles', ['employee'])
      expect(store.getters['user/isManager']).toBe(false)
    })

    it('can checks permissions', () => {
      expect(store.getters['user/can']('canEdit')).toBe(true)
      expect(store.getters['user/can']('canDelete')).toBe(false)
      expect(store.getters['user/can']('canView')).toBe(true)
      expect(store.getters['user/can']('nonExistentPermission')).toBe(false)
    })

    it('plan getters return correct values', () => {
      store.commit('user/setPlanInfo', {
        hasPlusPlan: true,
        hasPremiumPlan: false,
        currentPlan: 'plus',
      })
      
      expect(store.getters['user/hasPlusPlan']).toBe(true)
      expect(store.getters['user/hasPremiumPlan']).toBe(false)
      expect(store.getters['user/currentPlan']).toBe('plus')
    })
  })

  describe('actions', () => {
    it('fetchUserData fetches and commits user data', async () => {
      const mockUserData = {
        data: {
          email: '<EMAIL>',
          role: 'admin',
          roles: ['admin', 'owner'],
          permissions: { canEdit: true },
          has_plus_plan: true,
          has_premium_plan: false,
          current_plan: 'plus',
        },
      }

      vi.mocked(axios.get).mockResolvedValueOnce(mockUserData)

      await store.dispatch('user/fetchUserData')

      expect(axios.get).toHaveBeenCalledWith('/api/v1/user')
      expect(store.state.user.email).toBe('<EMAIL>')
      expect(store.state.user.role).toBe('admin')
      expect(store.state.user.roles).toEqual(['admin', 'owner'])
      expect(store.state.user.permissions).toEqual({ canEdit: true })
      expect(store.state.user.hasPlusPlan).toBe(true)
      expect(store.state.user.hasPremiumPlan).toBe(false)
      expect(store.state.user.currentPlan).toBe('plus')
    })

    it('fetchUserData handles errors gracefully', async () => {
      // Mock console.error to suppress expected error output
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      vi.mocked(axios.get).mockRejectedValueOnce(new Error('Network error'))
      
      // Should not throw
      await expect(store.dispatch('user/fetchUserData')).resolves.toBeUndefined()
      
      // Verify error was logged
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error fetching user data:', expect.any(Error))
      
      consoleErrorSpy.mockRestore()
    })
  })
})