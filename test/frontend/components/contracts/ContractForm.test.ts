import { describe, it, expect, vi, beforeEach } from 'vitest'
import { fireEvent, waitFor } from '@testing-library/vue'
import { renderWithProviders } from '../../utils/test-utils'
import ContractForm from '@/components/contracts/ContractForm.vue'
import axios from 'axios'

vi.mock('axios')

describe('ContractForm', () => {
  const mockProps = {
    contractId: null,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders form fields correctly', () => {
    const { getByLabelText, getByText } = renderWithProviders(ContractForm, {
      props: mockProps,
    })

    // Check form fields are present
    expect(getByLabelText(/jméno/i)).toBeInTheDocument()
    expect(getByLabelText(/příjmení/i)).toBeInTheDocument()
    expect(getByLabelText(/email/i)).toBeInTheDocument()
    expect(getByLabelText(/telefon/i)).toBeInTheDocument()
    expect(getByLabelText(/pozice/i)).toBeInTheDocument()
    expect(getByText(/uložit/i)).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const { getByText } = renderWithProviders(ContractForm, {
      props: mockProps,
    })

    const submitButton = getByText(/uložit/i)
    await fireEvent.click(submitButton)

    // Form should not submit without required fields
    expect(axios.post).not.toHaveBeenCalled()
  })

  it('submits form with valid data', async () => {
    const mockResponse = { data: { id: 1, name: 'John Doe' } }
    vi.mocked(axios.post).mockResolvedValueOnce(mockResponse)

    const { getByLabelText, getByText } = renderWithProviders(ContractForm, {
      props: mockProps,
    })

    // Fill form
    await fireEvent.update(getByLabelText(/jméno/i), 'John')
    await fireEvent.update(getByLabelText(/příjmení/i), 'Doe')
    await fireEvent.update(getByLabelText(/email/i), '<EMAIL>')
    await fireEvent.update(getByLabelText(/pozice/i), 'Developer')

    // Submit
    await fireEvent.click(getByText(/uložit/i))

    await waitFor(() => {
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/contracts'),
        expect.objectContaining({
          contract: expect.objectContaining({
            first_name: 'John',
            last_name: 'Doe',
            email: '<EMAIL>',
            job_title: 'Developer',
          }),
        })
      )
    })
  })

  it('handles API errors gracefully', async () => {
    vi.mocked(axios.post).mockRejectedValueOnce(new Error('API Error'))
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    const { getByLabelText, getByText } = renderWithProviders(ContractForm, {
      props: mockProps,
    })

    // Fill minimum required fields
    await fireEvent.update(getByLabelText(/jméno/i), 'John')
    await fireEvent.update(getByLabelText(/příjmení/i), 'Doe')
    await fireEvent.update(getByLabelText(/email/i), '<EMAIL>')

    // Submit
    await fireEvent.click(getByText(/uložit/i))

    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalled()
    })

    consoleErrorSpy.mockRestore()
  })

  it('pre-fills form when editing existing contract', async () => {
    const existingContract = {
      id: 1,
      first_name: 'Jane',
      last_name: 'Smith',
      email: '<EMAIL>',
      phone: '+420123456789',
      job_title: 'Manager',
    }

    // Mock the fetch request for existing contract
    vi.mocked(axios.get).mockResolvedValueOnce({ data: existingContract })

    const { getByLabelText } = renderWithProviders(ContractForm, {
      props: {
        ...mockProps,
        contractId: existingContract.id,
      },
    })

    // Wait for the component to fetch and populate the data
    await waitFor(() => {
      expect(getByLabelText(/jméno/i)).toHaveValue('Jane')
      expect(getByLabelText(/příjmení/i)).toHaveValue('Smith')
      expect(getByLabelText(/email/i)).toHaveValue('<EMAIL>')
      expect(getByLabelText(/telefon/i)).toHaveValue('+420123456789')
      expect(getByLabelText(/pozice/i)).toHaveValue('Manager')
    })
  })
})