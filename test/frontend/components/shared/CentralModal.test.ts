import { describe, it, expect, vi } from 'vitest'
import { fireEvent } from '@testing-library/vue'
import { renderWithProviders } from '../../utils/test-utils'
import CentralModal from '@/components/shared/CentralModal.vue'

describe('CentralModal', () => {
  const defaultProps = {
    modelValue: false,
    title: 'Test Modal',
  }

  it('does not render when closed', () => {
    const { queryByText } = renderWithProviders(CentralModal, {
      props: defaultProps,
    })

    expect(queryByText('Test Modal')).not.toBeInTheDocument()
  })

  it('renders when open', () => {
    const { getByText, getByTestId } = renderWithProviders(CentralModal, {
      props: {
        ...defaultProps,
        modelValue: true,
      },
      slots: {
        default: '<p>Modal content</p>',
      },
    })

    expect(getByText('Test Modal')).toBeInTheDocument()
    expect(getByText('Modal content')).toBeInTheDocument()
    expect(getByTestId('modal-backdrop')).toBeInTheDocument()
  })

  it('emits close event when clicking backdrop', async () => {
    const { getByTestId, emitted } = renderWithProviders(CentralModal, {
      props: {
        ...defaultProps,
        modelValue: true,
      },
    })

    await fireEvent.click(getByTestId('modal-backdrop'))

    expect(emitted()['update:modelValue']).toBeTruthy()
    expect(emitted()['update:modelValue'][0]).toEqual([false])
  })

  it('emits close event when clicking close button', async () => {
    const { getByLabelText, emitted } = renderWithProviders(CentralModal, {
      props: {
        ...defaultProps,
        modelValue: true,
      },
    })

    await fireEvent.click(getByLabelText(/close/i))

    expect(emitted()['update:modelValue']).toBeTruthy()
    expect(emitted()['update:modelValue'][0]).toEqual([false])
  })

  it('does not close when clicking modal content', async () => {
    const { getByTestId, emitted } = renderWithProviders(CentralModal, {
      props: {
        ...defaultProps,
        modelValue: true,
      },
    })

    await fireEvent.click(getByTestId('modal-content'))

    expect(emitted()['update:modelValue']).toBeUndefined()
  })

  it('closes on ESC key press', async () => {
    const { emitted } = renderWithProviders(CentralModal, {
      props: {
        ...defaultProps,
        modelValue: true,
      },
    })

    await fireEvent.keyDown(document, { key: 'Escape' })

    expect(emitted()['update:modelValue']).toBeTruthy()
    expect(emitted()['update:modelValue'][0]).toEqual([false])
  })

  it('renders with custom size', () => {
    const { getByTestId } = renderWithProviders(CentralModal, {
      props: {
        ...defaultProps,
        modelValue: true,
        size: 'large',
      },
    })

    expect(getByTestId('modal-dialog')).toHaveClass('modal-lg')
  })

  it('renders footer slot content', () => {
    const { getByText } = renderWithProviders(CentralModal, {
      props: {
        ...defaultProps,
        modelValue: true,
      },
      slots: {
        footer: '<button>Save</button><button>Cancel</button>',
      },
    })

    expect(getByText('Save')).toBeInTheDocument()
    expect(getByText('Cancel')).toBeInTheDocument()
  })

  it('prevents body scroll when open', () => {
    renderWithProviders(CentralModal, {
      props: {
        ...defaultProps,
        modelValue: true,
      },
    })

    expect(document.body.style.overflow).toBe('hidden')
  })

  it('restores body scroll when closed', async () => {
    const { rerender } = renderWithProviders(CentralModal, {
      props: {
        ...defaultProps,
        modelValue: true,
      },
    })

    expect(document.body.style.overflow).toBe('hidden')

    await rerender({ modelValue: false })

    expect(document.body.style.overflow).toBe('')
  })

  it('traps focus within modal', async () => {
    const { getByText, getByLabelText } = renderWithProviders(CentralModal, {
      props: {
        ...defaultProps,
        modelValue: true,
      },
      slots: {
        default: `
          <button>First button</button>
          <input type="text" placeholder="Input field" />
          <button>Last button</button>
        `,
      },
    })

    const firstButton = getByText('First button')
    const lastButton = getByText('Last button')
    const closeButton = getByLabelText(/close/i)

    // Focus should be trapped between focusable elements
    firstButton.focus()
    expect(document.activeElement).toBe(firstButton)

    // Tab to last element then wrap to close button
    await fireEvent.keyDown(document, { key: 'Tab', shiftKey: true })
    expect([closeButton, lastButton]).toContain(document.activeElement)
  })
})