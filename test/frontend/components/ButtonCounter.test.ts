import { describe, it, expect } from 'vitest'
import { fireEvent } from '@testing-library/vue'
import { renderWithProviders } from '../utils/test-utils'
import ButtonCounter from '@/components/ButtonCounter.vue'

describe('ButtonCounter', () => {
  it('renders initial count', () => {
    const { getByText } = renderWithProviders(ButtonCounter)
    expect(getByText('You clicked me 0 times.')).toBeInTheDocument()
  })

  it('increments count when clicked', async () => {
    const { getByText, getByRole } = renderWithProviders(ButtonCounter)
    const button = getByRole('button')
    
    expect(getByText('You clicked me 0 times.')).toBeInTheDocument()
    
    await fireEvent.click(button)
    expect(getByText('You clicked me 1 times.')).toBeInTheDocument()
    
    await fireEvent.click(button)
    expect(getByText('You clicked me 2 times.')).toBeInTheDocument()
  })

  it('maintains separate count state for each instance', async () => {
    // Create separate containers for each instance
    const container1 = document.createElement('div')
    const container2 = document.createElement('div')
    document.body.appendChild(container1)
    document.body.appendChild(container2)
    
    const instance1 = renderWithProviders(ButtonCounter, { container: container1 })
    const instance2 = renderWithProviders(ButtonCounter, { container: container2 })
    
    const button1 = instance1.getByRole('button')
    const button2 = instance2.getByRole('button')
    
    // Click first button
    await fireEvent.click(button1)
    expect(instance1.getByText('You clicked me 1 times.')).toBeInTheDocument()
    
    // Second button should still show 0
    expect(instance2.getByText('You clicked me 0 times.')).toBeInTheDocument()
    
    // Cleanup
    document.body.removeChild(container1)
    document.body.removeChild(container2)
  })
})