import { describe, it, expect, vi, beforeEach } from 'vitest'
import { fireEvent, waitFor } from '@testing-library/vue'
import { renderWithProviders } from '../../utils/test-utils'
import EventForm from '@/components/events/EventForm.vue'
import axios from 'axios'

// Mock axios
vi.mock('axios')

// Mock VueDatePicker component
vi.mock('@vuepic/vue-datepicker', () => ({
  default: {
    name: 'VueDatePicker',
    props: ['modelValue', 'format', 'range', 'enableTimePicker', 'placeholder', 'locale'],
    emits: ['update:modelValue'],
    template: `
      <input 
        :placeholder="placeholder"
        @change="$emit('update:modelValue', $event.target.value)"
        data-testid="date-picker"
      />
    `,
  },
}))

describe('EventForm', () => {
  const mockProps = {
    contractId: 1,
    companyId: 1,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders event type options', () => {
    const { getByLabelText } = renderWithProviders(EventForm, {
      props: mockProps,
    })

    expect(getByLabelText('Dovolená')).toBeInTheDocument()
    expect(getByLabelText('Pracovní neschopnost')).toBeInTheDocument()
    expect(getByLabelText('Ošetřování člena rodiny')).toBeInTheDocument()
    expect(getByLabelText('Návštěva lékaře')).toBeInTheDocument()
    expect(getByLabelText('Jiná absence')).toBeInTheDocument()
    expect(getByLabelText('Celodenní pracovní cesta')).toBeInTheDocument()
  })

  it('shows date picker when event type is selected', async () => {
    const { getByLabelText, getByTestId } = renderWithProviders(EventForm, {
      props: mockProps,
    })

    const vacationRadio = getByLabelText('Dovolená')
    await fireEvent.click(vacationRadio)

    expect(getByTestId('date-picker')).toBeInTheDocument()
  })

  it('shows travel-specific form for travel event type', async () => {
    const { getByLabelText, getByPlaceholderText } = renderWithProviders(EventForm, {
      props: mockProps,
    })

    const travelRadio = getByLabelText('Celodenní pracovní cesta')
    await fireEvent.click(travelRadio)

    expect(getByPlaceholderText('Trvání cesty')).toBeInTheDocument()
  })

  // TODO: Add tests for form submission once we have a better way to interact with VueDatePicker
  // The current implementation requires deep integration with the date picker component
  // which is complex to mock properly
})