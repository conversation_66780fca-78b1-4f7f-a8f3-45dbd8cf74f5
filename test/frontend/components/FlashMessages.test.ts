import { describe, it, expect, vi, beforeEach } from 'vitest'
import { fireEvent, waitFor } from '@testing-library/vue'
import { renderWithProviders } from '../utils/test-utils'
import FlashMessages from '@/components/FlashMessages.vue'

describe('FlashMessages', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders nothing when no messages', () => {
    const { container } = renderWithProviders(FlashMessages)
    expect(container.querySelector('.flash-message')).not.toBeInTheDocument()
  })

  it('displays success message', async () => {
    const { getByText, rerender } = renderWithProviders(FlashMessages)

    // Simulate flash message being added
    window.showFlashMessage('Operation successful', 'success')

    await rerender({})

    const message = getByText('Operation successful')
    expect(message).toBeInTheDocument()
    expect(message.closest('.alert')).toHaveClass('alert-success')
  })

  it('displays error message', async () => {
    const { getByText, rerender } = renderWithProviders(FlashMessages)

    window.showFlashMessage('Something went wrong', 'error')

    await rerender({})

    const message = getByText('Something went wrong')
    expect(message).toBeInTheDocument()
    expect(message.closest('.alert')).toHaveClass('alert-danger')
  })

  it('displays warning message', async () => {
    const { getByText, rerender } = renderWithProviders(FlashMessages)

    window.showFlashMessage('Please be careful', 'warning')

    await rerender({})

    const message = getByText('Please be careful')
    expect(message).toBeInTheDocument()
    expect(message.closest('.alert')).toHaveClass('alert-warning')
  })

  it('auto-dismisses message after timeout', async () => {
    vi.useFakeTimers()
    
    const { getByText, queryByText, rerender } = renderWithProviders(FlashMessages)

    window.showFlashMessage('Temporary message', 'info')
    await rerender({})

    expect(getByText('Temporary message')).toBeInTheDocument()

    // Fast-forward time
    vi.advanceTimersByTime(5000)
    await waitFor(() => {
      expect(queryByText('Temporary message')).not.toBeInTheDocument()
    })

    vi.useRealTimers()
  })

  it('allows manual dismissal', async () => {
    const { getByText, queryByText, getByLabelText, rerender } = renderWithProviders(FlashMessages)

    window.showFlashMessage('Dismissible message', 'info')
    await rerender({})

    expect(getByText('Dismissible message')).toBeInTheDocument()

    // Click close button
    const closeButton = getByLabelText(/close/i)
    await fireEvent.click(closeButton)

    await waitFor(() => {
      expect(queryByText('Dismissible message')).not.toBeInTheDocument()
    })
  })

  it('displays multiple messages', async () => {
    const { getByText, rerender } = renderWithProviders(FlashMessages)

    window.showFlashMessage('First message', 'success')
    window.showFlashMessage('Second message', 'error')
    window.showFlashMessage('Third message', 'info')

    await rerender({})

    expect(getByText('First message')).toBeInTheDocument()
    expect(getByText('Second message')).toBeInTheDocument()
    expect(getByText('Third message')).toBeInTheDocument()
  })

  it('handles HTML content safely', async () => {
    const { container, rerender } = renderWithProviders(FlashMessages)

    window.showFlashMessage('<script>alert("XSS")</script>Secure message', 'info')
    await rerender({})

    // Should escape HTML
    expect(container.textContent).toContain('<script>')
    expect(container.querySelector('script')).not.toBeInTheDocument()
  })
})