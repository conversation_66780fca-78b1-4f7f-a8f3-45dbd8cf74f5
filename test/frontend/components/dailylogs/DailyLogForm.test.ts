import { describe, it, expect, vi, beforeEach } from 'vitest'
import { fireEvent, waitFor } from '@testing-library/vue'
import { renderWithProviders } from '../../utils/test-utils'
import DailyLogForm from '@/components/dailylogs/DailyLogForm.vue'
import axios from 'axios'

vi.mock('axios')

describe('DailyLogForm', () => {
  const mockProps = {
    log: null,
    action: 'start',
    buttonLabel: 'Začít',
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Mock current date/time
    vi.useFakeTimers()
    vi.setSystemTime(new Date('2024-01-15 09:00:00'))
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('renders start form correctly', () => {
    const { getByText, getByLabelText } = renderWithProviders(DailyLogForm, {
      props: mockProps,
    })

    expect(getByText(/začít pracovní den/i)).toBeInTheDocument()
    expect(getByLabelText(/čas příchodu/i)).toBeInTheDocument()
    expect(getByText(/začít/i)).toBeInTheDocument()
  })

  it('renders finish form correctly', () => {
    const { getByText, getByLabelText } = renderWithProviders(DailyLogForm, {
      props: {
        ...mockProps,
        action: 'finish',
        buttonLabel: 'Ukončit',
        log: { id: 1, check_in: '09:00' },
      },
    })

    expect(getByText(/ukončit pracovní den/i)).toBeInTheDocument()
    expect(getByLabelText(/čas odchodu/i)).toBeInTheDocument()
    expect(getByText(/ukončit/i)).toBeInTheDocument()
  })

  it('submits start time correctly', async () => {
    const mockResponse = { data: { id: 1, check_in: '09:00:00' } }
    vi.mocked(axios.post).mockResolvedValueOnce(mockResponse)

    const { getByLabelText, getByText } = renderWithProviders(DailyLogForm, {
      props: mockProps,
    })

    // Set start time
    const timeInput = getByLabelText(/čas příchodu/i)
    await fireEvent.update(timeInput, '09:00')

    // Submit
    await fireEvent.click(getByText(/začít/i))

    await waitFor(() => {
      expect(axios.post).toHaveBeenCalledWith(
        '/daily_logs/start',
        expect.objectContaining({
          daily_log: expect.objectContaining({
            check_in: '09:00',
          }),
        })
      )
    })
  })

  it('submits finish time correctly', async () => {
    const mockResponse = { data: { id: 1, check_out: '17:00:00' } }
    vi.mocked(axios.post).mockResolvedValueOnce(mockResponse)

    const { getByLabelText, getByText } = renderWithProviders(DailyLogForm, {
      props: {
        ...mockProps,
        action: 'finish',
        buttonLabel: 'Ukončit',
        log: { id: 1, check_in: '09:00' },
      },
    })

    // Set finish time
    const timeInput = getByLabelText(/čas odchodu/i)
    await fireEvent.update(timeInput, '17:00')

    // Submit
    await fireEvent.click(getByText(/ukončit/i))

    await waitFor(() => {
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/finish'),
        expect.objectContaining({
          daily_log: expect.objectContaining({
            check_out: '17:00',
          }),
        })
      )
    })
  })

  it('shows break button when log is active', () => {
    const { getByText } = renderWithProviders(DailyLogForm, {
      props: {
        ...mockProps,
        action: 'active',
        buttonLabel: 'Přestávka',
        log: { id: 1, check_in: '09:00', is_on_break: false },
      },
    })

    expect(getByText(/přestávka/i)).toBeInTheDocument()
  })

  it('validates time format', async () => {
    const { getByLabelText, getByText } = renderWithProviders(DailyLogForm, {
      props: mockProps,
    })

    const timeInput = getByLabelText(/čas příchodu/i)
    await fireEvent.update(timeInput, 'invalid-time')

    await fireEvent.click(getByText(/začít/i))

    // Should not submit with invalid time
    expect(axios.post).not.toHaveBeenCalled()
  })

  it('handles API errors', async () => {
    vi.mocked(axios.post).mockRejectedValueOnce(new Error('Network error'))
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    const { getByLabelText, getByText } = renderWithProviders(DailyLogForm, {
      props: mockProps,
    })

    await fireEvent.update(getByLabelText(/čas příchodu/i), '09:00')
    await fireEvent.click(getByText(/začít/i))

    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalled()
    })

    consoleErrorSpy.mockRestore()
  })
})