import { describe, it, expect, vi, beforeEach } from 'vitest'
import { fireEvent } from '@testing-library/vue'
import { renderWithProviders } from '../utils/test-utils'
import HeaderNav from '@/components/HeaderNav.vue'
import { createMockStore } from '../utils/test-utils'

describe('HeaderNav', () => {
  let mockStore

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders navigation for authenticated user', () => {
    mockStore = createMockStore({
      user: {
        state: {
          isAuthenticated: true,
          currentUser: { name: '<PERSON>', email: '<EMAIL>' },
          roles: ['employee'],
        },
        getters: {
          isAuthenticated: () => true,
          currentUser: () => ({ name: '<PERSON>', email: '<EMAIL>' }),
          isOwner: () => false,
          isAdmin: () => false,
        },
      },
    })

    const { getByText, queryByText } = renderWithProviders(HeaderNav, {
      global: {
        plugins: [mockStore],
      },
    })

    // Should show main navigation items
    expect(getByText(/hlavní stránka/i)).toBeInTheDocument()
    expect(getByText(/docházka/i)).toBeInTheDocument()
    expect(getByText(/události/i)).toBeInTheDocument()

    // Should not show admin items
    expect(queryByText(/správa společnosti/i)).not.toBeInTheDocument()
  })

  it('shows admin menu items for admin users', () => {
    mockStore = createMockStore({
      user: {
        state: {
          isAuthenticated: true,
          roles: ['admin'],
        },
        getters: {
          isAuthenticated: () => true,
          isAdmin: () => true,
          isOwner: () => false,
          hasRole: () => (role) => role === 'admin',
        },
      },
    })

    const { getByText } = renderWithProviders(HeaderNav, {
      global: {
        plugins: [mockStore],
      },
    })

    // Should show admin items
    expect(getByText(/správa společnosti/i)).toBeInTheDocument()
    expect(getByText(/zaměstnanci/i)).toBeInTheDocument()
    expect(getByText(/nastavení/i)).toBeInTheDocument()
  })

  it('shows owner-specific items for owners', () => {
    mockStore = createMockStore({
      user: {
        state: {
          isAuthenticated: true,
          roles: ['owner'],
        },
        getters: {
          isAuthenticated: () => true,
          isOwner: () => true,
          isAdmin: () => false,
          hasRole: () => (role) => role === 'owner',
        },
      },
    })

    const { getByText } = renderWithProviders(HeaderNav, {
      global: {
        plugins: [mockStore],
      },
    })

    // Should show owner items
    expect(getByText(/společnosti/i)).toBeInTheDocument()
    expect(getByText(/předplatné/i)).toBeInTheDocument()
  })

  it('highlights active route', () => {
    const mockRoute = {
      path: '/daily_logs',
      name: 'daily_logs',
    }

    mockStore = createMockStore({
      user: {
        state: { isAuthenticated: true },
        getters: { isAuthenticated: () => true },
      },
    })

    const { getByText } = renderWithProviders(HeaderNav, {
      global: {
        plugins: [mockStore],
        mocks: {
          $route: mockRoute,
        },
      },
    })

    const dailyLogsLink = getByText(/docházka/i).closest('a')
    expect(dailyLogsLink).toHaveClass('active')
  })

  it('toggles mobile menu', async () => {
    mockStore = createMockStore({
      user: {
        state: { isAuthenticated: true },
        getters: { isAuthenticated: () => true },
      },
    })

    const { getByLabelText, getByTestId } = renderWithProviders(HeaderNav, {
      global: {
        plugins: [mockStore],
      },
    })

    const menuToggle = getByLabelText(/menu/i)
    const navMenu = getByTestId('nav-menu')

    // Initially closed
    expect(navMenu).not.toHaveClass('show')

    // Open menu
    await fireEvent.click(menuToggle)
    expect(navMenu).toHaveClass('show')

    // Close menu
    await fireEvent.click(menuToggle)
    expect(navMenu).not.toHaveClass('show')
  })

  it('shows user dropdown menu', async () => {
    mockStore = createMockStore({
      user: {
        state: {
          isAuthenticated: true,
          currentUser: { name: 'John Doe' },
        },
        getters: {
          isAuthenticated: () => true,
          currentUser: () => ({ name: 'John Doe' }),
        },
      },
    })

    const { getByText, getByTestId } = renderWithProviders(HeaderNav, {
      global: {
        plugins: [mockStore],
      },
    })

    const userMenu = getByTestId('user-menu')
    await fireEvent.click(userMenu)

    // Should show dropdown items
    expect(getByText(/profil/i)).toBeInTheDocument()
    expect(getByText(/nastavení/i)).toBeInTheDocument()
    expect(getByTestId('logout-link')).toBeInTheDocument()
  })

  it('handles logout', async () => {
    const mockDeleteMethod = vi.fn()
    window.Turbo = { visit: vi.fn() }

    mockStore = createMockStore({
      user: {
        state: {
          isAuthenticated: true,
          currentUser: { name: 'John Doe' },
        },
        getters: {
          isAuthenticated: () => true,
          currentUser: () => ({ name: 'John Doe' }),
        },
      },
    })

    const { getByTestId } = renderWithProviders(HeaderNav, {
      global: {
        plugins: [mockStore],
        mocks: {
          $http: {
            delete: mockDeleteMethod,
          },
        },
      },
    })

    // Open user menu
    await fireEvent.click(getByTestId('user-menu'))
    
    // Click logout
    await fireEvent.click(getByTestId('logout-link'))

    expect(mockDeleteMethod).toHaveBeenCalledWith('/users/sign_out')
  })
})