import { vi } from 'vitest'

// Mock window.showFlashMessage
export const mockFlashMessage = () => {
  const showFlashMessage = vi.fn()
  window.showFlashMessage = showFlashMessage
  return showFlashMessage
}

// Mock window.location
export const mockWindowLocation = (url: string = 'http://localhost:3000') => {
  delete (window as any).location
  window.location = new URL(url) as any
  window.location.assign = vi.fn()
  window.location.replace = vi.fn()
  window.location.reload = vi.fn()
  return window.location
}

// Mock Turbo
export const mockTurbo = () => {
  window.Turbo = {
    visit: vi.fn(),
    cache: {
      clear: vi.fn(),
    },
  } as any
  return window.Turbo
}

// Mock i18n translations
export const mockTranslations = {
  en: {
    cancel: 'Cancel',
    save: 'Save',
    submit: 'Submit',
    delete: 'Delete',
    edit: 'Edit',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    confirmation: 'Are you sure?',
    
    // Component specific
    events: {
      vacation: 'Vacation',
      sick: 'Sick',
      sick_day: 'Sick Day',
      travel: 'Business Trip',
      unpaid: 'Unpaid Leave',
      compensatory: 'Compensatory Time',
      travel_duration: 'Travel Duration',
      select_days: 'Select day(s)',
    },
    
    booking: {
      title: 'New Booking',
      duration: 'Duration',
      expected_duration: 'Expected Duration',
      notes: 'Notes',
      confirm: 'Confirm Booking',
    },
    
    works: {
      title: 'Work Sessions',
      start_time: 'Start Time',
      end_time: 'End Time',
      duration: 'Duration',
      description: 'Description',
    },
  },
}

// Mock API error responses
export const mockApiError = (status: number = 400, message: string = 'Bad Request') => ({
  response: {
    status,
    data: {
      error: message,
      errors: {
        base: [message],
      },
    },
  },
})

// Mock successful API response
export const mockApiSuccess = (data: any) => ({
  data,
  status: 200,
  statusText: 'OK',
  headers: {},
  config: {},
})