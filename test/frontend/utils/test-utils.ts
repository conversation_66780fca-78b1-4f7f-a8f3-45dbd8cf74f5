import { render, RenderOptions as BaseRenderOptions } from '@testing-library/vue'
import { createI18n } from 'vue-i18n'
import { createStore, Store } from 'vuex'
import { Component } from 'vue'

// Create a mock i18n instance
export const createMockI18n = (messages = {}) => {
  return createI18n({
    legacy: false,
    locale: 'en',
    fallbackLocale: 'en',
    messages: {
      en: {
        // Common actions
        cancel: 'Zrušit',
        save: '<PERSON><PERSON><PERSON><PERSON>',
        submit: '<PERSON><PERSON><PERSON>',
        select: 'Vybrat',
        delete: 'Smazat',
        edit: 'Upravit',
        loading: 'Načítání...',
        error: 'Chy<PERSON>',
        success: 'Úspěch',
        confirmation: 'Jste si jisti?',
        
        // Common fields
        first_name: '<PERSON><PERSON><PERSON>',
        last_name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        email: 'E-mail',
        phone: 'Telefon',
        job_title: 'Pracovní titul',
        
        event_type: {
          travel_all_day: 'Celodenní pracovní cesta',
          vacation: 'Do<PERSON><PERSON><PERSON>',
          illness: 'Pracov<PERSON><PERSON> neschopnost',
          family_sick: 'O<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> člena rodin<PERSON>',
          day_care: 'Náv<PERSON>těva léka<PERSON>e',
          other: '<PERSON><PERSON> absence',
          travel: 'Pracovní cesta',
          sick: 'Nemoc',
          sick_day: 'Sick Day',
          compensatory: 'Náhradní volno',
          unpaid: 'Neplacené volno',
        },
        
        events: {
          vacation: 'Dovolená',
          sick: 'Nemoc',
          sick_day: 'Sick Day',
          travel: 'Pracovní cesta',
          unpaid: 'Neplacené volno',
          compensatory: 'Náhradní volno',
          travel_duration: 'Trvání cesty',
          select_days: 'Vyberte den/dny',
        },
        
        contracts: {
          contract_type_label: 'Typ pracovního poměru',
          contract_type_placeholder: 'Typ pracovního poměru',
          email_invitation_title: 'Pozvání na e-mail',
          email_invitation_desc_1: 'Na uvedený e-mail odešleme pozvánku na připojení sa k Vašemu pracovnímu prostoru.',
          email_invitation_desc_2: 'Když chcete přidat sami sebe, ponechte prázdné.',
        },
        
        dailylogs: {
          start_work_day: 'Začít pracovní den',
          finish_work_day: 'Ukončit pracovní den',
          check_in_time: 'Čas příchodu',
          check_out_time: 'Čas odchodu',
          break: 'Přestávka',
          start: 'Začít',
          finish: 'Ukončit',
        },
        
        navigation: {
          home: 'Hlavní stránka',
          daily_logs: 'Docházka',
          events: 'Události',
          company_management: 'Správa společnosti',
          employees: 'Zaměstnanci',
          settings: 'Nastavení',
          companies: 'Společnosti',
          subscription: 'Předplatné',
          profile: 'Profil',
          logout: 'Odhlásit',
        },
        
        ...messages,
      },
    },
  })
}

// Define module structure
interface StoreModules {
  user?: any;
  log?: any;
  owner?: any;
  [key: string]: any;
}

// Create a mock Vuex store
export const createMockStore = (modules: StoreModules = {}) => {
  return createStore({
    modules: {
      user: {
        namespaced: true,
        state: {
          currentUser: null,
          isAuthenticated: false,
        },
        getters: {
          isAuthenticated: (state: any) => state.isAuthenticated,
          currentUser: (state: any) => state.currentUser,
        },
        ...modules.user,
      },
      log: {
        namespaced: true,
        state: {
          logs: [],
          currentLog: null,
        },
        ...modules.log,
      },
      owner: {
        namespaced: true,
        state: {
          companies: [],
          currentCompany: null,
        },
        ...modules.owner,
      },
      ...modules,
    },
  })
}

// Custom render function with common providers
export function renderWithProviders(
  component: Component,
  options: BaseRenderOptions<any> = {}
) {
  const i18n = options.global?.plugins?.find((p: any) => p.install?.name === 'i18n') || createMockI18n()
  const store = options.global?.plugins?.find((p: any) => p._modules) || createMockStore()

  return render(component, {
    ...options,
    global: {
      ...options.global,
      plugins: [i18n, store, ...(options.global?.plugins || [])],
      stubs: {
        teleport: true,
        ...options.global?.stubs,
      },
    },
  })
}

// Mock API responses
export const mockApiResponses = {
  success: (data: any) => Promise.resolve({ data }),
  error: (message: string, status = 400) => 
    Promise.reject({
      response: {
        status,
        data: { error: message },
      },
    }),
}