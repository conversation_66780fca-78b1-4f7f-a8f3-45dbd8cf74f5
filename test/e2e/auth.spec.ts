import { test, expect } from './fixtures/auth';

test.describe('Authentication Flow', () => {
  test('should display login page correctly', async ({ loginPage }) => {
    await loginPage.goto();
    
    // Check all elements are visible
    await expect(loginPage.emailInput).toBeVisible();
    await expect(loginPage.passwordInput).toBeVisible();
    await expect(loginPage.submitButton).toBeVisible();
    await expect(loginPage.signUpLink).toBeVisible();
    await expect(loginPage.forgotPasswordLink).toBeVisible();
  });

  test('should show error with invalid credentials', async ({ loginPage }) => {
    await loginPage.goto();
    
    await loginPage.login('<EMAIL>', 'wrongpassword');
    
    // Should stay on login page
    await loginPage.expectToBeOnLoginPage();
    
    // Should show error message
    const hasError = await loginPage.expectErrorMessage('Invalid Email or password');
    expect(hasError).toBeTruthy();
  });

  test('should login successfully with valid credentials', async ({ loginPage }) => {
    await loginPage.goto();
    
    // Use valid test credentials
    await loginPage.login('<EMAIL>', 'password123');
    
    // Should redirect to mainbox
    await loginPage.expectToBeLoggedIn();
    
    // Should see user menu or logout option
    await expect(loginPage.page.locator('[data-testid="user-menu"]')).toBeVisible();
  });

  test('should redirect to login when accessing protected route', async ({ page }) => {
    // Try to access protected route
    await page.goto('/mainbox');
    
    // Should redirect to login
    await expect(page).toHaveURL(/.*\/users\/sign_in/);
  });

  test('should logout successfully', async ({ authenticatedPage }) => {
    const page = authenticatedPage.page;
    
    // Click logout
    await page.locator('[data-testid="user-menu"]').click();
    await page.locator('[data-testid="logout-link"]').click();
    
    // Should redirect to login page
    await expect(page).toHaveURL(/.*\/users\/sign_in/);
    
    // Try to access protected route again
    await page.goto('/mainbox');
    await expect(page).toHaveURL(/.*\/users\/sign_in/);
  });

  test('should handle session timeout', async ({ authenticatedPage }) => {
    const page = authenticatedPage.page;
    
    // Clear cookies to simulate session timeout
    await page.context().clearCookies();
    
    // Try to navigate
    await page.goto('/mainbox');
    
    // Should redirect to login
    await expect(page).toHaveURL(/.*\/users\/sign_in/);
  });
});