import { Page, Locator } from '@playwright/test';

export class BookingPage {
  readonly page: Page;
  readonly titleInput: Locator;
  readonly availabilityCheckboxes: Locator;
  readonly durationSelect: Locator;
  readonly maxBookingsInput: Locator;
  readonly saveButton: Locator;
  readonly bookingLinks: Locator;
  readonly publicLinkButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.titleInput = page.locator('input[name="booking_link[title]"]');
    this.availabilityCheckboxes = page.locator('input[type="checkbox"][name*="availability"]');
    this.durationSelect = page.locator('select[name="booking_link[duration]"]');
    this.maxBookingsInput = page.locator('input[name="booking_link[max_bookings_per_day]"]');
    this.saveButton = page.locator('button[type="submit"]');
    this.bookingLinks = page.locator('[data-testid="booking-link-item"]');
    this.publicLinkButton = page.locator('[data-testid="public-link-button"]');
  }

  async goto() {
    await this.page.goto('/bookings');
  }

  async createBookingLink(title: string, duration: string = '60') {
    await this.page.locator('[data-testid="new-booking-link"]').click();
    await this.titleInput.fill(title);
    await this.durationSelect.selectOption(duration);
    
    // Select some availability
    const checkboxes = await this.availabilityCheckboxes.all();
    if (checkboxes.length > 0) {
      await checkboxes[0].check();
    }
    
    await this.saveButton.click();
    
    // Wait for redirect back to list
    await this.page.waitForURL('**/bookings');
  }

  async openPublicBookingPage(linkIndex: number = 0) {
    const links = await this.bookingLinks.all();
    if (links[linkIndex]) {
      await links[linkIndex].locator(this.publicLinkButton).click();
    }
  }

  async expectBookingLinkCount(count: number) {
    await this.page.waitForSelector('[data-testid="booking-link-item"]');
    const links = await this.bookingLinks.all();
    return links.length === count;
  }
}