import { Page, Locator } from '@playwright/test';

export class LoginPage {
  readonly page: Page;
  readonly emailInput: Locator;
  readonly passwordInput: Locator;
  readonly submitButton: Locator;
  readonly signUpLink: Locator;
  readonly forgotPasswordLink: Locator;
  readonly errorMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    this.emailInput = page.locator('input[type="email"]');
    this.passwordInput = page.locator('input[type="password"]');
    this.submitButton = page.locator('button[type="submit"]');
    this.signUpLink = page.locator('a:has-text("Sign up")');
    this.forgotPasswordLink = page.locator('a:has-text("Forgot your password")');
    this.errorMessage = page.locator('.alert-danger');
  }

  async goto() {
    await this.page.goto('/users/sign_in');
  }

  async login(email: string, password: string) {
    await this.emailInput.fill(email);
    await this.passwordInput.fill(password);
    await this.submitButton.click();
  }

  async expectToBeOnLoginPage() {
    await this.page.waitForURL('**/users/sign_in');
  }

  async expectToBeLoggedIn() {
    // After successful login, should redirect to mainbox
    await this.page.waitForURL('**/mainbox', { timeout: 10000 });
  }

  async expectErrorMessage(message: string) {
    await this.errorMessage.waitFor({ state: 'visible' });
    await this.page.waitForTimeout(100); // Let the message render
    const errorText = await this.errorMessage.textContent();
    return errorText?.includes(message) || false;
  }
}