import { test, expect } from './fixtures/auth';
import { BookingPage } from './pages/BookingPage';

test.describe('Booking Management Flow', () => {
  let bookingPage: BookingPage;

  test.beforeEach(async ({ authenticatedPage }) => {
    bookingPage = new BookingPage(authenticatedPage.page);
  });

  test('should create a new booking link', async () => {
    await bookingPage.goto();
    
    const initialCount = await bookingPage.bookingLinks.count();
    
    // Create new booking link
    await bookingPage.createBookingLink('Test Consultation', '30');
    
    // Verify it was created
    const newCount = await bookingPage.bookingLinks.count();
    expect(newCount).toBe(initialCount + 1);
    
    // Verify the new link appears in the list
    await expect(bookingPage.page.locator('text=Test Consultation')).toBeVisible();
  });

  test('should access public booking page', async () => {
    await bookingPage.goto();
    
    // Ensure at least one booking link exists
    const count = await bookingPage.bookingLinks.count();
    if (count === 0) {
      await bookingPage.createBookingLink('Public Test Link', '60');
    }
    
    // Open public page in new tab
    const [newPage] = await Promise.all([
      bookingPage.page.context().waitForEvent('page'),
      bookingPage.openPublicBookingPage(0),
    ]);
    
    // Verify public page loaded
    await newPage.waitForLoadState();
    expect(newPage.url()).toContain('/public_bookings/');
    
    // Should see calendar and time slots
    await expect(newPage.locator('[data-testid="booking-calendar"]')).toBeVisible();
    
    await newPage.close();
  });

  test('should make a booking on public page', async ({ page }) => {
    // This test simulates an unauthenticated user making a booking
    
    // First, create a booking link as authenticated user
    const authBookingPage = new BookingPage(page);
    await authBookingPage.goto();
    await authBookingPage.createBookingLink('Public Booking Test', '45');
    
    // Get the public URL
    const publicUrl = await page.locator('[data-testid="public-link-button"]').first().getAttribute('href');
    
    // Open in incognito context (no auth)
    const context = await page.context().browser()!.newContext();
    const publicPage = await context.newPage();
    
    await publicPage.goto(publicUrl!);
    
    // Select a date
    await publicPage.locator('[data-testid="available-date"]').first().click();
    
    // Select a time slot
    await publicPage.locator('[data-testid="time-slot"]').first().click();
    
    // Fill booking form
    await publicPage.fill('input[name="name"]', 'John Doe');
    await publicPage.fill('input[name="email"]', '<EMAIL>');
    await publicPage.fill('input[name="phone"]', '+420123456789');
    await publicPage.fill('textarea[name="notes"]', 'Looking forward to the meeting');
    
    // Submit booking
    await publicPage.click('button[type="submit"]');
    
    // Should see confirmation
    await expect(publicPage.locator('text=Booking confirmed')).toBeVisible();
    
    await context.close();
  });

  test('should manage booking availability', async () => {
    await bookingPage.goto();
    
    // Create a booking link with limited availability
    await bookingPage.page.locator('[data-testid="new-booking-link"]').click();
    await bookingPage.titleInput.fill('Limited Availability');
    await bookingPage.durationSelect.selectOption('90');
    await bookingPage.maxBookingsInput.fill('2');
    
    // Set specific days
    const mondayCheckbox = await bookingPage.page.locator('input[type="checkbox"][value="monday"]');
    const wednesdayCheckbox = await bookingPage.page.locator('input[type="checkbox"][value="wednesday"]');
    await mondayCheckbox.check();
    await wednesdayCheckbox.check();
    
    await bookingPage.saveButton.click();
    
    // Verify settings were saved
    await expect(bookingPage.page.locator('text=Limited Availability')).toBeVisible();
    await expect(bookingPage.page.locator('text=90 minutes')).toBeVisible();
  });

  test('should cancel a booking', async () => {
    // Navigate to bookings list
    await bookingPage.page.goto('/works');
    
    // If there are bookings, try to cancel one
    const bookingItems = await bookingPage.page.locator('[data-testid="booking-item"]').all();
    if (bookingItems.length > 0) {
      await bookingItems[0].locator('[data-testid="cancel-booking"]').click();
      
      // Confirm cancellation
      await bookingPage.page.locator('button:has-text("Confirm")').click();
      
      // Should see cancellation message
      await expect(bookingPage.page.locator('text=Booking cancelled')).toBeVisible();
    }
  });
});