import { test as base } from '@playwright/test';
import { LoginPage } from '../pages/LoginPage';

type TestFixtures = {
  loginPage: LoginPage;
  authenticatedPage: LoginPage;
};

export const test = base.extend<TestFixtures>({
  loginPage: async ({ page }, use) => {
    const loginPage = new LoginPage(page);
    await use(loginPage);
  },

  authenticatedPage: async ({ page }, use) => {
    // This fixture provides a pre-authenticated page
    const loginPage = new LoginPage(page);
    await loginPage.goto();
    
    // Use test credentials (should be configured in test environment)
    await loginPage.login('<EMAIL>', 'password123');
    await loginPage.expectToBeLoggedIn();
    
    await use(loginPage);
  },
});

export { expect } from '@playwright/test';