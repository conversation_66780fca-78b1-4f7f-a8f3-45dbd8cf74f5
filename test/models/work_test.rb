require 'test_helper'

class WorkTest < ActiveSupport::TestCase
  # Skip fixture loading
  def setup_fixtures; end
  def teardown_fixtures; end
  
  def setup
    @company = Company.create!(name: "Test Company", subdomain: "test")
    @contract = Contract.create!(
      company: @company,
      first_name: "Test",
      last_name: "User"
    )
  end

  test "should be valid with required attributes" do
    work = Work.new(
      title: "Test Work",
      company: @company,
      status: "scheduled"
    )
    assert work.valid?
  end

  test "should require title" do
    work = Work.new(
      company: @company,
      status: "scheduled"
    )
    assert_not work.valid?
    assert work.errors[:title].any?, "Should have an error on title"
  end

  test "should require company" do
    work = Work.new(
      title: "Test Work",
      status: "scheduled"
    )
    assert_not work.valid?
    assert work.errors[:company].any?, "Should have an error on company"
  end

  test "should require status" do
    # For status validation, we need to explicitly set it to nil
    # since enums typically have default values
    work = Work.new(
      title: "Test Work",
      company: @company
    )
    # Remove the status before validation
    work.status = nil
    assert_not work.valid?
    assert work.errors[:status].any?, "Should have an error on status"
  end

  test "should accept valid status values" do
    valid_statuses = %w[scheduled in_progress completed cancelled rescheduled]
    valid_statuses.each do |status|
      work = Work.new(
        title: "Test Work",
        company: @company,
        status: status
      )
      assert work.valid?, "Status #{status} should be valid"
    end
  end

  test "should reject invalid status values" do
    # Handle enum validation differently - enums in Rails validate at the DB level
    assert_raises ArgumentError do
      Work.new(
        title: "Test Work",
        company: @company,
        status: "invalid_status"
      )
    end
  end

  test "should handle work assignments" do
    work = Work.create!(
      title: "Test Work",
      company: @company,
      status: "scheduled"
    )
    
    work_assignment = WorkAssignment.create!(
      work: work,
      contract: @contract,
      company: @company
    )
    
    assert_equal 1, work.work_assignments.count
    assert_equal @contract, work.contracts.first
  end

  test "should destroy dependent work assignments" do
    work = Work.create!(
      title: "Test Work",
      company: @company,
      status: "scheduled"
    )
    
    WorkAssignment.create!(
      work: work,
      contract: @contract,
      company: @company
    )
    
    assert_difference 'WorkAssignment.count', -1 do
      work.destroy
    end
  end

  test "should handle work sessions" do
    work = Work.create!(
      title: "Test Work",
      company: @company,
      status: "scheduled"
    )
    
    # Create a unique email for each test to avoid conflicts
    user = User.create!(
      email: "test_sessions_#{Time.now.to_i}@example.com",
      password: "password123",
      password_confirmation: "password123"
    )
    
    # Add status to work_session if required
    work_session = WorkSession.create!(
      work: work,
      user: user,
      company: @company,
      start_time: Time.current,
      status: "in_progress"
    )
    
    assert_equal 1, work.work_sessions.count
  end

  test "should destroy dependent work sessions" do
    work = Work.create!(
      title: "Test Work",
      company: @company,
      status: "scheduled"
    )
    
    # Create a unique email for each test to avoid conflicts
    user = User.create!(
      email: "test_destroy_#{Time.now.to_i}@example.com",
      password: "password123",
      password_confirmation: "password123"
    )
    
    # Add status to work_session if required
    WorkSession.create!(
      work: work,
      user: user,
      company: @company,
      start_time: Time.current,
      status: "in_progress"
    )
    
    assert_difference 'WorkSession.count', -1 do
      work.destroy
    end
  end

  test "should not require work assignments" do
    work = Work.new(
      title: "Test Work",
      company: @company,
      status: "scheduled"
    )
    assert work.valid?
  end
  
  test "should properly track scheduled dates" do
    start_date = Date.today
    end_date = start_date + 7.days
    
    work = Work.create!(
      title: "Test Work",
      company: @company,
      status: "scheduled",
      scheduled_start_date: start_date,
      scheduled_end_date: end_date
    )
    
    assert_equal start_date, work.scheduled_start_date
    assert_equal end_date, work.scheduled_end_date
  end
  
  test "should save and retrieve location information" do
    work = Work.create!(
      title: "Test Work",
      company: @company,
      status: "scheduled",
      location: "Test Location",
      latitude: 42.123,
      longitude: -71.456
    )
    
    # Reload to ensure data is saved properly
    work.reload
    
    assert_equal "Test Location", work.location
    assert_equal 42.123, work.latitude
    assert_equal -71.456, work.longitude
  end
  
  test "should allow setting work type" do
    work = Work.create!(
      title: "Test Work",
      company: @company, 
      status: "scheduled",
      work_type: "maintenance"
    )
    
    work.reload
    assert_equal "maintenance", work.work_type
  end
  
  def teardown
    @company.destroy if @company && @company.persisted?
    @contract.destroy if @contract && @contract.persisted?
  end
end 