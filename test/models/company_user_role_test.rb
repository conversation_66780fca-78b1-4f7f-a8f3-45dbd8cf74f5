require "test_helper"

class CompanyUserRoleTest < ActiveSupport::TestCase
  setup do
    @user = User.create!(email: "<EMAIL>", password: "password")
    @company = Company.create!(name: "Test Company")
    @role = Role.create!(name: "Admin")
  end

  test "user can be assigned to a company with a role" do
    # Create the association
    company_user_role = CompanyUserRole.create!(user: @user, company: @company, role: @role)

    # Assertions
    assert_equal @company, company_user_role.company
    assert_equal @user, company_user_role.user
    assert_equal @role, company_user_role.role
  end

  test "user cannot be assigned to the same company with the same role twice" do
    # Create the first association
    CompanyUserRole.create!(user: @user, company: @company, role: @role)
    
    # Attempt to create the duplicate association
    duplicate = CompanyUserRole.new(user: @user, company: @company, role: @role)

    # Assertions
    assert_not duplicate.valid?
    assert_includes duplicate.errors[:user], "has already been taken"
  end
end
