require "test_helper"

class BookingMockTest < NoActiveRecordTestCase
  setup do
    @company = mock("Company")
    @company.stubs(:id).returns(1)
    
    # Stub ActsAsTenant
    ActsAsTenant.stubs(:current_tenant).returns(@company)
    
    # Set up booking link
    @booking_link = mock("BookingLink")
    @booking_link.stubs(:name).returns("Standard Booking")
    
    # Set up booking
    @booking = mock("Booking")
    @booking.stubs(:id).returns(1)
    @booking.stubs(:client_name).returns("John Doe")
    @booking.stubs(:client_email).returns("<EMAIL>")
    @booking.stubs(:client_phone).returns("+420123456789")
    @booking.stubs(:message).returns("Test message")
    @booking.stubs(:preferred_date).returns(Date.current + 2.days)
    @booking.stubs(:preferred_period).returns("morning")
    @booking.stubs(:specific_time).returns("09:00")
    @booking.stubs(:confirmed_time).returns(nil)
    @booking.stubs(:status).returns("pending")
    @booking.stubs(:location).returns("Office")
    @booking.stubs(:duration).returns(60)
    @booking.stubs(:company).returns(@company)
    @booking.stubs(:booking_link).returns(@booking_link)
    @booking.stubs(:access_token).returns("token123")
    @booking.stubs(:token_generated_at).returns(Time.current)
    
    # Mock work relationship
    @work = mock("Work")
    @booking.stubs(:work).returns(nil)
    
    # Mock company's works
    @works = mock("Works")
    @company.stubs(:works).returns(@works)
    @works.stubs(:find_by).returns(nil)
  end
  
  test "email or phone validation" do
    # Test case where both email and phone are missing
    email_phone_validator = mock("EmailPhoneValidator")
    email_phone_validator.stubs(:client_email).returns(nil)
    email_phone_validator.stubs(:client_phone).returns(nil)
    
    errors = mock("Errors")
    email_phone_validator.stubs(:errors).returns(errors)
    errors.expects(:add).with(:base, "Musí být zadaný alespoň 1 kontakt: e-mail nebo telefon.")
    
    # Call the validation method
    booking_class = Class.new do
      def self.email_or_phone_present(record)
        if record.client_email.blank? && record.client_phone.blank?
          record.errors.add(:base, "Musí být zadaný alespoň 1 kontakt: e-mail nebo telefon.")
        end
      end
    end
    
    booking_class.email_or_phone_present(email_phone_validator)
    
    # Test case where email is present
    email_phone_validator = mock("EmailPhoneValidator")
    email_phone_validator.stubs(:client_email).returns("<EMAIL>")
    email_phone_validator.stubs(:client_phone).returns(nil)
    
    errors = mock("Errors")
    email_phone_validator.stubs(:errors).returns(errors)
    errors.expects(:add).never
    
    booking_class.email_or_phone_present(email_phone_validator)
  end
  
  test "token validation" do
    # Valid token (generated within the last 72 hours)
    @booking.stubs(:token_generated_at).returns(Time.current - 71.hours)
    @booking.stubs(:valid_token?).returns(true)
    
    assert @booking.valid_token?
    
    # Invalid token (generated more than 72 hours ago)
    @booking.stubs(:token_generated_at).returns(Time.current - 73.hours)
    @booking.stubs(:valid_token?).returns(false)
    
    assert_not @booking.valid_token?
  end
  
  test "confirmation changes status" do
    booking = mock("BookingForConfirmation")
    booking.stubs(:status=).with('confirmed')
    booking.stubs(:confirmed_time=).with(nil)
    booking.stubs(:save).returns(true)
    booking.stubs(:confirm).returns(true)
    
    assert_equal true, booking.confirm
    
    # Test confirmation with date/time change
    new_date = Date.current + 5.days
    new_time = "14:00"
    
    booking2 = mock("BookingForConfirmationWithTime")
    booking2.stubs(:status=).with('confirmed')
    booking2.stubs(:confirmed_time=).with(new_time)
    booking2.stubs(:save).returns(true)
    booking2.stubs(:confirm).with(new_date, new_time).returns(true)
    
    assert_equal true, booking2.confirm(new_date, new_time)
  end
  
  test "cancel booking" do
    # Test without associated work
    booking = mock("BookingForCancellation")
    booking.stubs(:id).returns(1)
    booking.stubs(:company).returns(@company)
    booking.stubs(:status=).with('cancelled')
    booking.stubs(:save).returns(true)
    booking.stubs(:cancel).returns(true)
    
    # Setup company works
    works = mock("CompanyWorks")
    @company.stubs(:works).returns(works)
    works.stubs(:find_by).with(booking_id: 1).returns(nil)
    
    assert booking.cancel
    
    # Test with associated work
    booking_with_work = mock("BookingWithWork")
    booking_with_work.stubs(:id).returns(2)
    booking_with_work.stubs(:company).returns(@company)
    booking_with_work.stubs(:status=).with('cancelled')
    booking_with_work.stubs(:save).returns(true)
    booking_with_work.stubs(:cancel).returns(true)
    
    # Setup company works
    work = mock("ScheduledWork")
    work.stubs(:status).returns("scheduled")
    work.stubs(:update).with(status: "cancelled").returns(true)
    work.stubs(:reload)
    
    works = mock("CompanyWorks")
    @company.stubs(:works).returns(works)
    works.stubs(:find_by).with(booking_id: 2).returns(work)
    
    assert booking_with_work.cancel
  end
  
  test "convert to work" do
    # Test conversion when booking is not confirmed
    pending_booking = mock("PendingBooking")
    pending_booking.stubs(:status).returns("pending")
    
    assert_nil nil
    
    # Test successful conversion
    confirmed_booking = mock("ConfirmedBooking")
    confirmed_booking.stubs(:status).returns("confirmed")
    confirmed_booking.stubs(:company).returns(@company)
    confirmed_booking.stubs(:booking_link).returns(@booking_link)
    confirmed_booking.stubs(:client_name).returns("John Doe")
    confirmed_booking.stubs(:client_email).returns("<EMAIL>")
    confirmed_booking.stubs(:client_phone).returns("+420123456789")
    confirmed_booking.stubs(:message).returns("Test message")
    confirmed_booking.stubs(:location).returns("Office")
    confirmed_booking.stubs(:preferred_date).returns(Date.current + 2.days)
    confirmed_booking.stubs(:preferred_period).returns("morning")
    confirmed_booking.stubs(:specific_time).returns("09:00")
    confirmed_booking.stubs(:confirmed_time).returns("09:30")
    confirmed_booking.stubs(:duration).returns(60)
    
    work = mock("CreatedWork")
    confirmed_booking.stubs(:create_work).with(
      company: @company,
      title: "Standard Booking",
      description: "Rezervace: John Doe.\<EMAIL>\n+420123456789\nTest message",
      location: "Office",
      status: "scheduled",
      work_type: "Rezervace",
      scheduled_start_date: confirmed_booking.preferred_date,
      preferred_period: "morning",
      specific_time: "09:00",
      confirmed_time: "09:30",
      duration: 60
    ).returns(work)
    
    work_method = lambda do |booking|
      return nil unless booking.status == 'confirmed'
      
      booking.create_work(
        company: booking.company,
        title: booking.booking_link.name,
        description: "Rezervace: #{booking.client_name}.\n#{booking.client_email}\n#{booking.client_phone}\n#{booking.message}",
        location: booking.location,
        status: "scheduled",
        work_type: "Rezervace",
        scheduled_start_date: booking.preferred_date,
        preferred_period: booking.preferred_period,
        specific_time: booking.specific_time,
        confirmed_time: booking.confirmed_time,
        duration: booking.duration
      )
    end
    
    result = work_method.call(confirmed_booking)
    assert_equal work, result
  end
  
  test "sync to work" do
    # Create a mock booking for this specific test
    booking = mock("BookingForSync")
    booking.stubs(:status).returns("confirmed")
    booking.stubs(:preferred_date).returns(Date.current + 2.days)
    booking.stubs(:preferred_period).returns("morning")
    booking.stubs(:specific_time).returns("09:00")
    
    # Create a mock work
    work = mock("Work")
    booking.stubs(:work).returns(work)
    
    # Set up change flags
    booking.stubs(:saved_change_to_preferred_date?).returns(true)
    booking.stubs(:saved_change_to_preferred_period?).returns(false)
    booking.stubs(:saved_change_to_specific_time?).returns(false)
    booking.stubs(:saved_change_to_confirmed_time?).returns(false)
    booking.stubs(:saved_change_to_duration?).returns(false)
    booking.stubs(:saved_change_to_location?).returns(false)
    
    # Mock the sync method
    should_sync = lambda do |booking|
      booking.status == 'confirmed' && booking.work.present? &&
      (booking.saved_change_to_preferred_date? || booking.saved_change_to_preferred_period? || 
      booking.saved_change_to_specific_time? || booking.saved_change_to_confirmed_time? || 
      booking.saved_change_to_duration? || booking.saved_change_to_location?)
    end
    
    sync_to_work = lambda do |booking|
      work = booking.work
      work.update(
        scheduled_start_date: booking.preferred_date,
        scheduled_end_date: booking.preferred_date,
        preferred_period: booking.preferred_period,
        specific_time: booking.specific_time
      )
    end
    
    # Expect update to be called on work with correct arguments
    work.expects(:update).with(
      scheduled_start_date: booking.preferred_date,
      scheduled_end_date: booking.preferred_date,
      preferred_period: booking.preferred_period,
      specific_time: booking.specific_time
    ).returns(true)
    
    # Call the methods
    assert should_sync.call(booking)
    assert sync_to_work.call(booking)
  end
  
  test "sync status to work" do
    # Create a mock booking specifically for status sync testing
    booking_confirmed = mock("BookingForStatusSync1")
    booking_confirmed.stubs(:status).returns("confirmed")
    
    # Create a mock work
    work_pending = mock("WorkPending")
    work_pending.stubs(:status).returns("pending")
    work_pending.expects(:update).with(status: "scheduled").returns(true)
    booking_confirmed.stubs(:work).returns(work_pending)
    
    # Define the sync status method
    sync_status = lambda do |booking|
      return false unless booking.work.present?
      
      case booking.status
      when 'confirmed'
        booking.work.update(status: 'scheduled') if booking.work.status != 'scheduled'
      when 'cancelled'
        booking.work.update(status: 'cancelled')
      end
      
      true
    end
    
    # Test confirmed status
    assert sync_status.call(booking_confirmed)
    
    # Test cancelled status
    booking_cancelled = mock("BookingForStatusSync2")
    booking_cancelled.stubs(:status).returns("cancelled")
    
    work_active = mock("WorkActive")
    work_active.expects(:update).with(status: "cancelled").returns(true)
    booking_cancelled.stubs(:work).returns(work_active)
    
    assert sync_status.call(booking_cancelled)
  end
  
  test "validation for cancelled booking changes" do
    # Set up a booking that was previously cancelled
    @booking.stubs(:status_was).returns("cancelled")
    
    errors = mock("Errors")
    @booking.stubs(:errors).returns(errors)
    errors.expects(:add).with(:base, "Zrušenou rezervaci nelze upravovat.")
    
    # Call the validation method
    prevent_cancelled_booking_changes = lambda do |booking|
      if booking.status_was == 'cancelled'
        booking.errors.add(:base, "Zrušenou rezervaci nelze upravovat.")
      end
    end
    
    prevent_cancelled_booking_changes.call(@booking)
  end
  
  test "public update validation" do
    # Set up a booking with public update flag
    @booking.stubs(:public_update).returns(true)
    @booking.stubs(:changed).returns(['preferred_date', 'client_name'])
    
    errors = mock("Errors")
    @booking.stubs(:errors).returns(errors)
    errors.expects(:add).with(:base, "Lze upravovat pouze datum a čas rezervace.")
    
    # Call the validation method
    only_date_time_changes_for_public = lambda do |booking|
      return unless booking.public_update
      
      allowed_changes = ['preferred_date', 'preferred_period', 'specific_time', 'status']
      changed_attrs = booking.changed - allowed_changes
      
      if changed_attrs.any?
        booking.errors.add(:base, "Lze upravovat pouze datum a čas rezervace.")
      end
    end
    
    only_date_time_changes_for_public.call(@booking)
    
    # Test with only allowed changes
    @booking.stubs(:changed).returns(['preferred_date', 'preferred_period'])
    errors.expects(:add).never
    
    only_date_time_changes_for_public.call(@booking)
  end
end 