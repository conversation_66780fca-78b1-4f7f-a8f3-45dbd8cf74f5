require "test_helper"

class NotificationTest < ActiveSupport::TestCase
  setup do
    @company = companies(:acme_corp)
    @user = users(:john)
    @work = works(:scheduled_work)
  end

  test "should create notification with valid attributes" do
    notification = Notification.new(
      user: @user,
      company: @company,
      notifiable: @work,
      notification_type: 'work_assignment_added',
      title: 'New work assignment',
      message: 'You have been assigned to a new work'
    )
    
    assert notification.valid?
    assert notification.save
  end

  test "should require user" do
    notification = Notification.new(
      company: @company,
      notifiable: @work,
      notification_type: 'work_assignment_added'
    )
    
    assert_not notification.valid?
    assert_includes notification.errors[:user], "must exist"
  end

  test "should require company" do
    notification = Notification.new(
      user: @user,
      notifiable: @work,
      notification_type: 'work_assignment_added'
    )
    
    assert_not notification.valid?
    assert_includes notification.errors[:company], "must exist"
  end

  test "should require notifiable" do
    notification = Notification.new(
      user: @user,
      company: @company,
      notification_type: 'work_assignment_added'
    )
    
    assert_not notification.valid?
    assert_includes notification.errors[:notifiable], "must exist"
  end

  test "should validate notification type" do
    notification = Notification.new(
      user: @user,
      company: @company,
      notifiable: @work,
      notification_type: 'invalid_type'
    )
    
    assert_not notification.valid?
    assert_includes notification.errors[:notification_type], "is not included in the list"
  end

  test "unread scope should return only unread notifications" do
    read_notification = Notification.create!(
      user: @user,
      company: @company,
      notifiable: @work,
      notification_type: 'work_assignment_added',
      read_at: Time.current
    )
    
    unread_notification = Notification.create!(
      user: @user,
      company: @company,
      notifiable: @work,
      notification_type: 'work_status_changed',
      read_at: nil
    )
    
    unread_notifications = Notification.unread
    
    assert_includes unread_notifications, unread_notification
    assert_not_includes unread_notifications, read_notification
  end

  test "mark_as_read! should set read_at timestamp" do
    notification = Notification.create!(
      user: @user,
      company: @company,
      notifiable: @work,
      notification_type: 'work_assignment_added'
    )
    
    assert_nil notification.read_at
    
    notification.mark_as_read!
    
    assert_not_nil notification.read_at
    assert notification.read_at <= Time.current
  end

  test "should belong to user, company and notifiable" do
    notification = Notification.create!(
      user: @user,
      company: @company,
      notifiable: @work,
      notification_type: 'work_assignment_added'
    )
    
    assert_equal @user, notification.user
    assert_equal @company, notification.company
    assert_equal @work, notification.notifiable
  end
end