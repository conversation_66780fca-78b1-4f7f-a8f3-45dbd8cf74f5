require "test_helper"

class BookingTest < ActiveSupport::TestCase
  test "should not save booking without required fields" do
    booking = Booking.new
    assert_not booking.save, "Saved booking without required fields"
    
    booking.client_name = "<PERSON>"
    assert_not booking.save, "Saved booking without email or phone"
    
    booking.client_email = "<EMAIL>"
    assert_not booking.save, "Saved booking without preferred date"
    
    booking.preferred_date = Date.current
    assert_not booking.save, "Saved booking without preferred period"
    
    booking.preferred_period = "morning"
    assert_not booking.save, "Saved booking without company or booking link"
    
    booking.company = companies(:company1)
    booking.booking_link = booking_links(:standard_booking_link)
    assert booking.save, "Could not save booking with all required fields"
  end
  
  test "should validate email format" do
    booking = Booking.new(
      client_name: "<PERSON>",
      client_email: "invalid-email",
      preferred_date: Date.current,
      preferred_period: "morning",
      company: companies(:company1),
      booking_link: booking_links(:standard_booking_link),
      status: "pending"
    )
    
    assert_not booking.save, "Saved booking with invalid email format"
    
    booking.client_email = "<EMAIL>"
    assert booking.save, "Could not save booking with valid email"
  end
  
  test "should validate preferred period inclusion" do
    booking = Booking.new(
      client_name: "<PERSON>",
      client_email: "<EMAIL>",
      preferred_date: Date.current,
      preferred_period: "invalid",
      company: companies(:company1),
      booking_link: booking_links(:standard_booking_link),
      status: "pending"
    )
    
    assert_not booking.save, "Saved booking with invalid preferred period"
    
    booking.preferred_period = "morning"
    assert booking.save, "Could not save booking with valid preferred period"
    
    booking.preferred_period = "afternoon"
    assert booking.save, "Could not save booking with valid preferred period"
  end
  
  test "should validate status inclusion" do
    booking = Booking.new(
      client_name: "John Doe",
      client_email: "<EMAIL>",
      preferred_date: Date.current,
      preferred_period: "morning",
      company: companies(:company1),
      booking_link: booking_links(:standard_booking_link),
      status: "invalid"
    )
    
    assert_not booking.save, "Saved booking with invalid status"
    
    valid_statuses = %w[pending confirmed completed cancelled rescheduled]
    valid_statuses.each do |status|
      booking.status = status
      assert booking.save, "Could not save booking with valid status: #{status}"
    end
  end
  
  test "should require either email or phone" do
    booking = Booking.new(
      client_name: "John Doe",
      preferred_date: Date.current,
      preferred_period: "morning",
      company: companies(:company1),
      booking_link: booking_links(:standard_booking_link),
      status: "pending"
    )
    
    assert_not booking.save, "Saved booking without email or phone"
    
    booking.client_email = "<EMAIL>"
    assert booking.save, "Could not save booking with email only"
    
    booking.client_email = nil
    booking.client_phone = "+420123456789"
    assert booking.save, "Could not save booking with phone only"
  end
  
  test "should generate access token on create" do
    booking = Booking.new(
      client_name: "John Doe",
      client_email: "<EMAIL>",
      preferred_date: Date.current,
      preferred_period: "morning",
      company: companies(:company1),
      booking_link: booking_links(:standard_booking_link),
      status: "pending"
    )
    
    assert_nil booking.access_token
    assert booking.save
    assert_not_nil booking.access_token
    assert_not_nil booking.token_generated_at
  end
  
  test "should validate token age" do
    booking = bookings(:booking_pending)
    assert booking.valid_token?, "New token should be valid"
    
    # Set token generation time to 73 hours ago
    booking.update_column(:token_generated_at, 73.hours.ago)
    assert_not booking.valid_token?, "Expired token should not be valid"
  end
  
  test "should regenerate access token" do
    booking = bookings(:booking_pending)
    old_token = booking.access_token
    old_timestamp = booking.token_generated_at
    
    booking.regenerate_access_token
    
    assert_not_equal old_token, booking.access_token, "Token should be regenerated"
    assert booking.token_generated_at > old_timestamp, "Token generation timestamp should be updated"
  end
  
  test "should prevent email changes" do
    booking = bookings(:booking_pending)
    original_email = booking.client_email
    
    booking.client_email = "<EMAIL>"
    assert_not booking.save, "Saved booking with changed email"
    assert_includes booking.errors[:client_email], "Nelze změnit email k téhle rezervaci."
    
    booking.reload
    assert_equal original_email, booking.client_email
  end
  
  test "should prevent changes to cancelled bookings" do
    booking = bookings(:booking_cancelled)
    
    booking.client_name = "New Name"
    assert_not booking.save, "Saved cancelled booking with changes"
    assert_includes booking.errors[:base], "Zrušenou rezervaci nelze upravovat."
  end
  
  test "should validate booking limits on create" do
    # The booking_link has a morning limit of 1 and already has a booking
    booking_link = booking_links(:standard_booking_link)
    date = bookings(:booking_pending).preferred_date
    
    new_booking = Booking.new(
      client_name: "John Doe",
      client_email: "<EMAIL>",
      preferred_date: date,
      preferred_period: "morning",
      company: companies(:company1),
      booking_link: booking_link,
      status: "pending"
    )
    
    assert_not new_booking.save, "Saved booking that exceeds morning limit"
    assert_includes new_booking.errors[:base], "Termín dopoledne je již plně obsazen."
    
    # Change to afternoon which should be available
    new_booking.preferred_period = "afternoon"
    assert new_booking.save, "Could not save booking with available period"
  end
  
  test "should confirm booking" do
    booking = bookings(:booking_pending)
    assert_equal "pending", booking.status
    
    assert booking.confirm
    assert_equal "confirmed", booking.status
  end
  
  test "should confirm booking with new date and time" do
    booking = bookings(:booking_pending)
    new_date = Date.current + 7.days
    new_time = "11:00"
    
    assert booking.confirm(new_date, new_time)
    assert_equal "confirmed", booking.status
    assert_equal new_time, booking.confirmed_time
  end
  
  test "should convert to work when confirmed" do
    booking = bookings(:booking_pending)
    
    # First confirm the booking
    assert booking.confirm
    
    # Then convert to work
    work = booking.convert_to_work
    assert_not_nil work
    assert work.persisted?
    
    # Check work details
    assert_equal booking.company, work.company
    assert_equal booking.booking_link.name, work.title
    assert_equal booking.preferred_date, work.scheduled_start_date
    assert_equal booking.preferred_period, work.preferred_period
    assert_equal booking.specific_time, work.specific_time
    assert_equal booking.confirmed_time, work.confirmed_time
    assert_equal booking.duration, work.duration
    assert_equal booking.location, work.location
    assert_equal "scheduled", work.status
  end
  
  test "should not convert to work when not confirmed" do
    booking = bookings(:booking_pending)
    assert_nil booking.convert_to_work
  end
  
  test "should cancel booking" do
    booking = bookings(:booking_confirmed)
    
    assert booking.cancel
    assert_equal "cancelled", booking.status
  end
  
  test "should cancel associated work when booking is cancelled" do
    booking = bookings(:booking_with_work)
    
    # Create an associated work
    work = Work.create(
      company: booking.company,
      title: "Associated Work",
      status: "scheduled",
      scheduled_start_date: booking.preferred_date,
      booking_id: booking.id
    )
    
    assert booking.cancel
    work.reload
    assert_equal "cancelled", work.status
  end
  
  test "should complete booking" do
    booking = bookings(:booking_confirmed)
    
    assert booking.complete
    assert_equal "completed", booking.status
  end
  
  test "should only allow date and time changes when public_update is true" do
    booking = bookings(:booking_pending)
    booking.public_update = true
    
    # Try to change something not allowed
    booking.client_name = "New Name"
    assert_not booking.save
    assert_includes booking.errors[:base], "Lze upravovat pouze datum a čas rezervace."
    
    # Reset and try allowed changes
    booking.reload
    booking.public_update = true
    booking.preferred_date = Date.current + 7.days
    booking.preferred_period = "afternoon"
    booking.specific_time = "16:00"
    
    assert booking.save
  end
  
  test "should sync to work when relevant attributes change" do
    booking = bookings(:booking_with_work)
    
    # Create an associated work
    work = Work.create(
      company: booking.company,
      title: "Associated Work",
      status: "scheduled",
      scheduled_start_date: booking.preferred_date,
      booking_id: booking.id
    )
    
    # Change relevant attributes
    new_date = Date.current + 10.days
    new_period = booking.preferred_period == "morning" ? "afternoon" : "morning"
    new_time = "17:00"
    
    booking.update(
      preferred_date: new_date,
      preferred_period: new_period,
      specific_time: new_time
    )
    
    work.reload
    assert_equal new_date, work.scheduled_start_date
    assert_equal new_period, work.preferred_period
    assert_equal new_time, work.specific_time
  end
end 