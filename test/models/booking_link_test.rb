require "test_helper"

class BookingLinkTest < ActiveSupport::TestCase
  test "should not save booking link without name" do
    booking_link = BookingLink.new(
      company: companies(:company1),
      daily_limit: 2,
      morning_limit: 1,
      afternoon_limit: 1
    )
    assert_not booking_link.save, "Saved booking link without name"
  end
  
  test "should generate slug on create" do
    booking_link = BookingLink.new(
      name: "New Booking Link",
      company: companies(:company1),
      daily_limit: 2,
      morning_limit: 1,
      afternoon_limit: 1
    )
    assert booking_link.save
    assert_equal "new-booking-link", booking_link.slug
  end
  
  test "should generate unique slug when duplicate exists" do
    original = booking_links(:standard_booking_link)
    duplicate = BookingLink.new(
      name: "Standard Booking",
      company: companies(:company1)
    )
    assert duplicate.save
    assert_not_equal original.slug, duplicate.slug
    assert_equal "standard-booking-1", duplicate.slug
  end
  
  test "slug should be company-scoped" do
    # Create a new company for testing
    company2 = Company.create(name: "Company 2", slug: "company-2")
    
    # Create a booking link with the same name in different company
    booking_link = BookingLink.new(
      name: "Standard Booking",
      company: company2
    )
    
    assert booking_link.save
    assert_equal "standard-booking", booking_link.slug
  end
  
  test "exceeds_period_limit? should return false when no limit is set" do
    booking_link = BookingLink.new(
      name: "No Limits",
      company: companies(:company1),
      morning_limit: nil,
      afternoon_limit: nil
    )
    
    assert_not booking_link.exceeds_period_limit?(Date.current, "morning")
    assert_not booking_link.exceeds_period_limit?(Date.current, "afternoon")
  end
  
  test "exceeds_period_limit? should check morning bookings count" do
    booking_link = booking_links(:standard_booking_link)
    date = bookings(:booking_pending).preferred_date
    
    # Initially should not exceed limit (limit is 1, we have 1 booking)
    assert booking_link.exceeds_period_limit?(date, "morning")
    
    # Create a new booking for a different date
    new_booking = Booking.create(
      client_name: "Test Client",
      client_email: "<EMAIL>",
      preferred_date: date + 1.day,
      preferred_period: "morning",
      status: "pending",
      company: companies(:company1),
      booking_link: booking_link
    )
    
    # Should still not exceed limit for the original date
    assert booking_link.exceeds_period_limit?(date, "morning")
    
    # Should not exceed limit for the new date
    assert_not booking_link.exceeds_period_limit?(date + 1.day, "morning")
  end
  
  test "exceeds_period_limit? should ignore cancelled bookings" do
    booking_link = booking_links(:standard_booking_link)
    booking = bookings(:booking_pending)
    
    # Initially exceeding limit
    assert booking_link.exceeds_period_limit?(booking.preferred_date, "morning")
    
    # After cancelling, should not exceed limit
    booking.update(status: "cancelled")
    assert_not booking_link.exceeds_period_limit?(booking.preferred_date, "morning")
  end
  
  test "exceeds_daily_limit? should check total bookings for a day" do
    booking_link = booking_links(:standard_booking_link)
    date = bookings(:booking_pending).preferred_date
    
    # Create another booking for the same date but different period
    new_booking = Booking.create(
      client_name: "Test Client",
      client_email: "<EMAIL>",
      preferred_date: date,
      preferred_period: "afternoon",
      status: "pending",
      company: companies(:company1),
      booking_link: booking_link
    )
    
    # Daily limit is 2, we now have 2 bookings
    assert booking_link.exceeds_daily_limit?(date)
    
    # Cancel one booking
    new_booking.update(status: "cancelled")
    assert_not booking_link.exceeds_daily_limit?(date)
  end
  
  test "available_for_booking? should check both limits" do
    booking_link = booking_links(:standard_booking_link)
    date = Date.current + 10.days
    
    # Initially, date should be available
    assert booking_link.available_for_booking?(date, "morning")
    
    # Add enough bookings to exceed period limit
    Booking.create(
      client_name: "Test Client",
      client_email: "<EMAIL>",
      preferred_date: date,
      preferred_period: "morning",
      status: "pending",
      company: companies(:company1),
      booking_link: booking_link
    )
    
    # Morning should not be available but afternoon should
    assert_not booking_link.available_for_booking?(date, "morning")
    assert booking_link.available_for_booking?(date, "afternoon")
    
    # Add enough bookings to exceed daily limit
    Booking.create(
      client_name: "Test Client 2",
      client_email: "<EMAIL>",
      preferred_date: date,
      preferred_period: "afternoon",
      status: "pending",
      company: companies(:company1),
      booking_link: booking_link
    )
    
    # Both periods should be unavailable due to daily limit
    assert_not booking_link.available_for_booking?(date, "morning")
    assert_not booking_link.available_for_booking?(date, "afternoon")
  end
  
  test "include_works_in_count should affect availability" do
    booking_link = booking_links(:vip_booking_link)
    date = Date.current + 10.days
    
    # Initially, date should be available
    assert booking_link.available_for_booking?(date, "morning")
    
    # Create a work without a booking for the same date and period
    work = Work.create(
      title: "Test Work",
      scheduled_start_date: date,
      preferred_period: "morning",
      status: "scheduled",
      company: companies(:company1)
    )
    
    # With include_works_in_count=false, the work shouldn't affect availability
    assert booking_link.available_for_booking?(date, "morning")
    
    # Change to include works in count
    booking_link.update(include_works_in_count: true)
    
    # Now morning should not be available
    assert_not booking_link.available_for_booking?(date, "morning")
  end
end 