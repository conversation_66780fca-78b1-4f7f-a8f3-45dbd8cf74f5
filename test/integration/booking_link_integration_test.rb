require 'test_helper'

class BookingLinkIntegrationTest < ActionDispatch::IntegrationTest
  include Devise::Test::IntegrationHelpers
  
  setup do
    @company = companies(:company1)
    ActsAsTenant.with_tenant(@company) do
      @booking_link = BookingLink.create!(
        name: "Integration Test Link",
        daily_limit: 3,
        morning_limit: 2,
        afternoon_limit: 2,
        include_works_in_count: true,
        active: true,
        company: @company
      )
    end
    
    @user = users(:admin)
    sign_in @user
  end
  
  test "creating a new booking link" do
    ActsAsTenant.with_tenant(@company) do
      assert_difference('BookingLink.count') do
        post booking_links_path, params: {
          booking_link: {
            name: "New Test Link",
            daily_limit: 5,
            morning_limit: 3,
            afternoon_limit: 3,
            include_works_in_count: true,
            active: true,
            company_id: @company.id
          }
        }
      end
      
      new_link = BookingLink.find_by(name: "New Test Link")
      assert_not_nil new_link
      assert_equal "new-test-link", new_link.slug
      assert_equal 5, new_link.daily_limit
      assert_equal 3, new_link.morning_limit
      assert_equal 3, new_link.afternoon_limit
      assert new_link.include_works_in_count
      assert new_link.active
    end
  end
  
  test "slug is automatically generated and unique" do
    ActsAsTenant.with_tenant(@company) do
      # Create a booking link with the same name
      link1 = BookingLink.create!(
        name: "Duplicate Name",
        daily_limit: 2,
        company: @company
      )
      
      link2 = BookingLink.create!(
        name: "Duplicate Name",
        daily_limit: 2,
        company: @company
      )
      
      assert_equal "duplicate-name", link1.slug
      assert_not_equal link1.slug, link2.slug
      assert_equal "duplicate-name-1", link2.slug
    end
  end
  
  test "booking limits functionality" do
    ActsAsTenant.with_tenant(@company) do
      date = Date.current + 10.days
      
      # Initially no bookings
      assert_not @booking_link.exceeds_period_limit?(date, "morning")
      assert_not @booking_link.exceeds_period_limit?(date, "afternoon")
      assert_not @booking_link.exceeds_daily_limit?(date)
      assert @booking_link.available_for_booking?(date, "morning")
      assert @booking_link.available_for_booking?(date, "afternoon")
      
      # Create bookings to reach morning limit
      2.times do |i|
        Booking.create!(
          client_name: "Client #{i}",
          client_email: "client#{i}@example.com",
          preferred_date: date,
          preferred_period: "morning",
          status: "pending",
          company: @company,
          booking_link: @booking_link
        )
      end
      
      # Morning should be at limit, afternoon still available
      assert @booking_link.exceeds_period_limit?(date, "morning")
      assert_not @booking_link.exceeds_period_limit?(date, "afternoon")
      assert_not @booking_link.exceeds_daily_limit?(date)
      assert_not @booking_link.available_for_booking?(date, "morning")
      assert @booking_link.available_for_booking?(date, "afternoon")
      
      # Add more bookings to reach afternoon and daily limit
      2.times do |i|
        Booking.create!(
          client_name: "Afternoon Client #{i}",
          client_email: "afternoon#{i}@example.com",
          preferred_date: date,
          preferred_period: "afternoon",
          status: "pending",
          company: @company,
          booking_link: @booking_link
        )
      end
      
      # Now both periods and daily limit should be reached
      assert @booking_link.exceeds_period_limit?(date, "morning")
      assert @booking_link.exceeds_period_limit?(date, "afternoon")
      assert @booking_link.exceeds_daily_limit?(date)
      assert_not @booking_link.available_for_booking?(date, "morning")
      assert_not @booking_link.available_for_booking?(date, "afternoon")
    end
  end
  
  test "cancelled bookings don't count towards limits" do
    ActsAsTenant.with_tenant(@company) do
      date = Date.current + 11.days
      
      # Create a booking and then cancel it
      booking = Booking.create!(
        client_name: "Client To Cancel",
        client_email: "<EMAIL>",
        preferred_date: date,
        preferred_period: "morning",
        status: "pending",
        company: @company,
        booking_link: @booking_link
      )
      
      # Initially it should count towards the limit
      assert_not @booking_link.exceeds_period_limit?(date, "morning")
      
      # Create another booking to reach the limit
      Booking.create!(
        client_name: "Second Client",
        client_email: "<EMAIL>",
        preferred_date: date,
        preferred_period: "morning",
        status: "pending",
        company: @company,
        booking_link: @booking_link
      )
      
      # Now we should be at the limit
      assert @booking_link.exceeds_period_limit?(date, "morning")
      
      # Cancel the first booking
      booking.update(status: "cancelled")
      
      # Now we should be under the limit again
      assert_not @booking_link.exceeds_period_limit?(date, "morning")
    end
  end
  
  test "including works in booking counts" do
    ActsAsTenant.with_tenant(@company) do
      date = Date.current + 12.days
      
      # Create a booking link that includes works in count
      link_with_works = BookingLink.create!(
        name: "Link With Works",
        daily_limit: 1,
        morning_limit: 1,
        afternoon_limit: 1,
        include_works_in_count: true,
        active: true,
        company: @company
      )
      
      # Create a booking link that doesn't include works
      link_without_works = BookingLink.create!(
        name: "Link Without Works",
        daily_limit: 1,
        morning_limit: 1,
        afternoon_limit: 1,
        include_works_in_count: false,
        active: true,
        company: @company
      )
      
      # Create a work for the same date/period
      Work.create!(
        title: "Test Work",
        scheduled_start_date: date,
        preferred_period: "morning",
        status: "scheduled",
        company: @company
      )
      
      # The link that includes works should count this work
      assert link_with_works.exceeds_period_limit?(date, "morning")
      
      # The link that doesn't include works should not count it
      assert_not link_without_works.exceeds_period_limit?(date, "morning")
    end
  end
end 