require "test_helper"

class BookingLinkTest < ActionDispatch::IntegrationTest
  include Devise::Test::IntegrationHelpers
  
  setup do
    @company = companies(:company1)
    @user = users(:admin)
    ActsAsTenant.current_tenant = @company
    sign_in @user
  end
  
  teardown do
    ActsAsTenant.current_tenant = nil
  end
  
  test "create and update booking link" do
    # Create a new booking link
    assert_difference 'BookingLink.count' do
      post "/booking_links", params: {
        booking_link: {
          name: "New Integration Test Link",
          daily_limit: 3,
          morning_limit: 2,
          afternoon_limit: 2,
          include_works_in_count: true,
          active: true,
          company_id: @company.id
        }
      }
    end
    
    # Find the created booking link
    booking_link = BookingLink.find_by(name: "New Integration Test Link")
    assert_not_nil booking_link
    assert_equal "new-integration-test-link", booking_link.slug
    assert_equal 3, booking_link.daily_limit
    assert_equal 2, booking_link.morning_limit
    assert_equal 2, booking_link.afternoon_limit
    assert booking_link.include_works_in_count
    assert booking_link.active
    
    # Update the booking link
    patch "/booking_links/#{booking_link.id}", params: {
      booking_link: {
        name: "Updated Link Name",
        daily_limit: 4,
        morning_limit: 2,
        afternoon_limit: 3
      }
    }
    
    # Verify the update
    booking_link.reload
    assert_equal "Updated Link Name", booking_link.name
    # Slug should not change when name is updated
    assert_equal "new-integration-test-link", booking_link.slug
    assert_equal 4, booking_link.daily_limit
    assert_equal 2, booking_link.morning_limit
    assert_equal 3, booking_link.afternoon_limit
  end
  
  test "deactivate booking link" do
    booking_link = booking_links(:standard_booking_link)
    assert booking_link.active
    
    # Deactivate the booking link
    patch "/booking_links/#{booking_link.id}", params: {
      booking_link: {
        active: false
      }
    }
    
    # Verify the update
    booking_link.reload
    assert_not booking_link.active
  end
  
  test "public booking link page" do
    booking_link = booking_links(:standard_booking_link)
    
    # Access the public booking link page
    get "/booking_links/#{booking_link.slug}/public"
    assert_response :success
    
    # The page should contain the booking link name
    assert_match booking_link.name, response.body
  end
  
  test "create booking through booking link" do
    booking_link = booking_links(:standard_booking_link)
    
    # Check initial count
    initial_count = booking_link.bookings.count
    
    # Create a booking through the booking link
    post "/booking_links/#{booking_link.slug}/bookings", params: {
      booking: {
        client_name: "Public Booking Client",
        client_email: "<EMAIL>",
        client_phone: "+420777123456",
        message: "Booking made through public link",
        preferred_date: Date.current + 30.days,
        preferred_period: "morning",
        specific_time: "09:30",
        location: "Client location",
        duration: 60
      }
    }
    
    # Verify booking was created
    assert_equal initial_count + 1, booking_link.bookings.count
    
    # Find the created booking
    booking = booking_link.bookings.last
    assert_equal "Public Booking Client", booking.client_name
    assert_equal "<EMAIL>", booking.client_email
    assert_equal "pending", booking.status
  end
  
  test "check booking link availability" do
    booking_link = booking_links(:standard_booking_link)
    date = Date.current + 14.days
    
    # Check availability through API
    get "/booking_links/#{booking_link.slug}/availability", params: {
      date: date.to_s
    }, as: :json
    
    assert_response :success
    
    json_response = JSON.parse(response.body)
    assert_includes json_response, "morning"
    assert_includes json_response, "afternoon"
    
    # Create a booking for the morning
    Booking.create!(
      client_name: "Availability Test Client",
      client_email: "<EMAIL>",
      client_phone: "+420777888999",
      message: "Testing availability",
      preferred_date: date,
      preferred_period: "morning",
      status: "pending",
      location: "Test Location",
      duration: 60,
      booking_link: booking_link,
      company: @company
    )
    
    # Check availability again
    get "/booking_links/#{booking_link.slug}/availability", params: {
      date: date.to_s
    }, as: :json
    
    assert_response :success
    
    json_response = JSON.parse(response.body)
    # Morning should not be available now
    assert_not json_response["morning"]["available"]
    # Afternoon should still be available
    assert json_response["afternoon"]["available"]
  end
  
  test "booking link statistics" do
    booking_link = booking_links(:standard_booking_link)
    
    # Access booking link statistics page
    get "/booking_links/#{booking_link.id}/statistics"
    assert_response :success
    
    # API version
    get "/booking_links/#{booking_link.id}/statistics", as: :json
    assert_response :success
    
    json_response = JSON.parse(response.body)
    assert_includes json_response, "bookings_count"
    assert_includes json_response, "bookings_by_status"
    assert_includes json_response, "bookings_by_date"
  end
  
  test "listing booking links" do
    # Get list of booking links
    get "/booking_links"
    assert_response :success
    
    # JSON format
    get "/booking_links", as: :json
    assert_response :success
    
    json_response = JSON.parse(response.body)
    assert_kind_of Array, json_response
    assert_operator json_response.length, :>=, 2 # We have at least the 2 fixtures
  end
  
  test "delete booking link" do
    # Create a booking link specifically for deletion
    booking_link = BookingLink.create!(
      name: "Link To Delete",
      daily_limit: 1,
      morning_limit: 1,
      afternoon_limit: 1,
      include_works_in_count: true,
      active: true,
      company: @company
    )
    
    # Delete the booking link
    assert_difference 'BookingLink.count', -1 do
      delete "/booking_links/#{booking_link.id}"
    end
    
    # Verify it's gone
    assert_nil BookingLink.find_by(id: booking_link.id)
  end
  
  test "cannot delete booking link with bookings" do
    booking_link = booking_links(:standard_booking_link)
    
    # Create a booking for this link
    Booking.create!(
      client_name: "Deletion Test Client",
      client_email: "<EMAIL>",
      client_phone: "+420777888999",
      message: "Testing deletion constraint",
      preferred_date: Date.current + 14.days,
      preferred_period: "morning",
      status: "pending",
      location: "Test Location",
      duration: 60,
      booking_link: booking_link,
      company: @company
    )
    
    # Try to delete the booking link
    assert_no_difference 'BookingLink.count' do
      delete "/booking_links/#{booking_link.id}"
    end
    
    # Verify it still exists
    assert_not_nil BookingLink.find_by(id: booking_link.id)
  end
end 