require "test_helper"

class BookingFlowTest < ActionDispatch::IntegrationTest
  include Devise::Test::IntegrationHelpers
  
  setup do
    @company = companies(:company1)
    @booking_link = booking_links(:standard_booking_link)
    @user = users(:admin)
    ActsAsTenant.current_tenant = @company
  end
  
  teardown do
    ActsAsTenant.current_tenant = nil
  end
  
  test "create booking and view it" do
    # Create a new booking
    assert_difference 'Booking.count' do
      post "/bookings", params: {
        booking: {
          client_name: "New Client",
          client_email: "<EMAIL>",
          client_phone: "+420777888999",
          message: "Integration test booking",
          preferred_date: Date.current + 5.days,
          preferred_period: "morning",
          specific_time: "10:00",
          status: "pending",
          location: "Test Location",
          duration: 60,
          booking_link_id: @booking_link.id,
          company_id: @company.id
        }
      }
    end
    
    # Get the created booking
    booking = Booking.last
    assert_equal "New Client", booking.client_name
    assert_equal "<EMAIL>", booking.client_email
    assert_equal "+420777888999", booking.client_phone
    assert_equal "morning", booking.preferred_period
    assert_equal "pending", booking.status
    assert_not_nil booking.access_token
    
    # Access the booking page with the token
    get "/bookings/#{booking.id}?token=#{booking.access_token}"
    assert_response :success
  end
  
  test "booking update flow" do
    # Sign in as admin
    sign_in @user
    
    # Create a booking
    booking = Booking.create!(
      client_name: "Update Test Client",
      client_email: "<EMAIL>",
      client_phone: "+420777888999",
      message: "Test booking for update flow",
      preferred_date: Date.current + 7.days,
      preferred_period: "morning",
      specific_time: "09:30",
      status: "pending",
      location: "Test Location",
      duration: 60,
      booking_link: @booking_link,
      company: @company
    )
    
    # Update the booking
    patch "/bookings/#{booking.id}", params: {
      booking: {
        preferred_date: Date.current + 8.days,
        preferred_period: "afternoon",
        specific_time: "14:30"
      }
    }
    
    # Verify the update
    booking.reload
    assert_equal Date.current + 8.days, booking.preferred_date
    assert_equal "afternoon", booking.preferred_period
    assert_equal "14:30", booking.specific_time
    
    # Confirm the booking
    post "/bookings/#{booking.id}/confirm", params: {
      booking: {
        preferred_date: booking.preferred_date,
        specific_time: "15:00"
      }
    }
    
    # Verify confirmation
    booking.reload
    assert_equal "confirmed", booking.status
    assert_equal "15:00", booking.confirmed_time
  end
  
  test "booking to work conversion flow" do
    # Sign in as admin
    sign_in @user
    
    # Create and confirm a booking
    booking = Booking.create!(
      client_name: "Conversion Test Client",
      client_email: "<EMAIL>",
      client_phone: "+420777888999",
      message: "Test booking for conversion flow",
      preferred_date: Date.current + 10.days,
      preferred_period: "morning",
      specific_time: "09:00",
      status: "pending",
      location: "Test Location",
      duration: 60,
      booking_link: @booking_link,
      company: @company
    )
    
    booking.confirm
    
    # Convert to work
    assert_difference 'Work.count' do
      post "/bookings/#{booking.id}/convert_to_work"
    end
    
    # Verify work was created and associated with booking
    booking.reload
    assert_not_nil booking.work
    
    work = booking.work
    assert_equal booking.preferred_date, work.scheduled_start_date
    assert_equal booking.preferred_period, work.preferred_period
    assert_equal booking.specific_time, work.specific_time
    assert_equal "scheduled", work.status
  end
  
  test "booking cancellation flow" do
    # Sign in as admin
    sign_in @user
    
    # Create a booking
    booking = Booking.create!(
      client_name: "Cancellation Test Client",
      client_email: "<EMAIL>",
      client_phone: "+420777888999",
      message: "Test booking for cancellation flow",
      preferred_date: Date.current + 12.days,
      preferred_period: "afternoon",
      specific_time: "13:00",
      status: "pending",
      location: "Test Location",
      duration: 60,
      booking_link: @booking_link,
      company: @company
    )
    
    # Convert to a work first
    booking.confirm
    booking.convert_to_work
    
    # Cancel the booking
    post "/bookings/#{booking.id}/cancel"
    
    # Verify cancellation
    booking.reload
    assert_equal "cancelled", booking.status
    
    # Verify associated work was also cancelled
    assert_equal "cancelled", booking.work.status
  end
  
  test "public booking update flow" do
    # Create a booking
    booking = Booking.create!(
      client_name: "Public Update Test Client",
      client_email: "<EMAIL>",
      client_phone: "+420777888999",
      message: "Test booking for public update flow",
      preferred_date: Date.current + 15.days,
      preferred_period: "morning",
      specific_time: "10:00",
      status: "pending",
      location: "Test Location",
      duration: 60,
      booking_link: @booking_link,
      company: @company
    )
    
    # Public update (not signed in)
    patch "/public/bookings/#{booking.id}", params: {
      token: booking.access_token,
      booking: {
        preferred_date: Date.current + 16.days,
        preferred_period: "afternoon",
        specific_time: "16:00"
      }
    }
    
    # Verify the update
    booking.reload
    assert_equal Date.current + 16.days, booking.preferred_date
    assert_equal "afternoon", booking.preferred_period
    assert_equal "16:00", booking.specific_time
    
    # Try public update with invalid data (should fail)
    patch "/public/bookings/#{booking.id}", params: {
      token: booking.access_token,
      booking: {
        client_name: "Should Not Change",
        preferred_date: Date.current + 17.days
      }
    }
    
    # Verify client_name didn't change
    booking.reload
    assert_equal "Public Update Test Client", booking.client_name
    assert_equal Date.current + 17.days, booking.preferred_date
  end
  
  test "booking API endpoints" do
    sign_in @user
    
    # Fetch bookings through API
    get "/bookings/fetch", as: :json
    assert_response :success
    
    json_response = JSON.parse(response.body)
    assert_kind_of Array, json_response
    
    # Create a booking through API
    assert_difference 'Booking.count' do
      post "/bookings", params: {
        booking: {
          client_name: "API Test Client",
          client_email: "<EMAIL>",
          client_phone: "+420777888999",
          message: "API test booking",
          preferred_date: Date.current + 20.days,
          preferred_period: "morning",
          specific_time: "09:15",
          status: "pending",
          location: "API Test Location",
          duration: 60,
          booking_link_id: @booking_link.id,
          company_id: @company.id
        }
      }, as: :json
    end
    
    # Get the created booking
    booking = Booking.find_by(client_email: "<EMAIL>")
    assert_not_nil booking
    
    # Update through API
    patch "/bookings/#{booking.id}", params: {
      booking: {
        preferred_date: Date.current + 21.days
      }
    }, as: :json
    
    booking.reload
    assert_equal Date.current + 21.days, booking.preferred_date
  end
end 