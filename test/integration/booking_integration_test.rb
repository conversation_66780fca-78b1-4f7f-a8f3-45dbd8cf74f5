require 'test_helper'

class BookingIntegrationTest < ActionDispatch::IntegrationTest
  include Devise::Test::IntegrationHelpers
  
  setup do
    @company = companies(:company1)
    ActsAsTenant.with_tenant(@company) do
      @booking_link = BookingLink.create!(
        name: "Integration Test Link",
        daily_limit: 3,
        morning_limit: 2,
        afternoon_limit: 2,
        include_works_in_count: true,
        active: true,
        company: @company
      )
      
      @booking = Booking.create!(
        client_name: "Test Client",
        client_email: "<EMAIL>",
        client_phone: "+420123456789",
        message: "Integration test booking",
        preferred_date: Date.current + 5.days,
        preferred_period: "morning",
        specific_time: "10:00",
        status: "pending",
        location: "Test Location",
        duration: 60,
        booking_link: @booking_link,
        company: @company
      )
    end
    
    @user = users(:admin)
    sign_in @user
  end
  
  test "creating a new booking" do
    ActsAsTenant.with_tenant(@company) do
      assert_difference('Booking.count') do
        post bookings_path, params: {
          booking: {
            client_name: "New Integration Client",
            client_email: "<EMAIL>",
            client_phone: "+420987654321",
            message: "New test booking",
            preferred_date: Date.current + 7.days,
            preferred_period: "afternoon",
            specific_time: "14:00",
            status: "pending",
            location: "New Location",
            duration: 90,
            booking_link_id: @booking_link.id,
            company_id: @company.id
          }
        }
      end
      
      new_booking = Booking.find_by(client_email: "<EMAIL>")
      assert_not_nil new_booking
      assert_equal "New Integration Client", new_booking.client_name
      assert_equal "afternoon", new_booking.preferred_period
      assert_not_nil new_booking.access_token
      assert_not_nil new_booking.token_generated_at
    end
  end
  
  test "token generation and validation" do
    ActsAsTenant.with_tenant(@company) do
      # A newly created booking should have a valid token
      assert_not_nil @booking.access_token
      assert_not_nil @booking.token_generated_at
      assert @booking.valid_token?
      
      # Update token timestamp to be expired
      @booking.update_column(:token_generated_at, 73.hours.ago)
      assert_not @booking.valid_token?
      
      # Regenerate access token
      old_token = @booking.access_token
      @booking.regenerate_access_token
      
      assert_not_equal old_token, @booking.access_token
      assert @booking.valid_token?
    end
  end
  
  test "confirming a booking" do
    ActsAsTenant.with_tenant(@company) do
      # Initially the booking is pending
      assert_equal "pending", @booking.status
      
      # Confirm the booking
      @booking.confirm
      @booking.reload
      
      assert_equal "confirmed", @booking.status
      assert_nil @booking.confirmed_time
      
      # Confirm with a specific time
      new_booking = Booking.create!(
        client_name: "Confirm Test Client",
        client_email: "<EMAIL>",
        preferred_date: Date.current + 6.days,
        preferred_period: "afternoon",
        specific_time: "15:00",
        status: "pending",
        company: @company,
        booking_link: @booking_link
      )
      
      # Confirm with specific time
      new_date = Date.current + 7.days
      new_time = "16:30"
      new_booking.confirm(new_date, new_time)
      new_booking.reload
      
      assert_equal "confirmed", new_booking.status
      assert_equal new_time, new_booking.confirmed_time
    end
  end
  
  test "converting booking to work" do
    ActsAsTenant.with_tenant(@company) do
      # A pending booking should not be convertible to work
      assert_nil @booking.convert_to_work
      
      # Confirm the booking first
      @booking.confirm
      @booking.reload
      
      # Now convert to work
      assert_difference('Work.count') do
        work = @booking.convert_to_work
        assert_not_nil work
        assert work.persisted?
        
        # Check that the work has the correct attributes
        assert_equal @booking.company, work.company
        assert_equal @booking.booking_link.name, work.title
        assert_equal @booking.preferred_date, work.scheduled_start_date
        assert_equal @booking.preferred_period, work.preferred_period
        assert_equal @booking.specific_time, work.specific_time
        assert_equal @booking.duration, work.duration
        assert_equal @booking.location, work.location
        assert_equal "scheduled", work.status
        assert_equal @booking.id, work.booking_id
      end
    end
  end
  
  test "cancelling a booking" do
    ActsAsTenant.with_tenant(@company) do
      # Create a booking and confirm it
      booking = Booking.create!(
        client_name: "Cancellation Test Client",
        client_email: "<EMAIL>",
        preferred_date: Date.current + 8.days,
        preferred_period: "morning",
        status: "pending",
        company: @company,
        booking_link: @booking_link
      )
      
      booking.confirm
      booking.reload
      
      # Convert it to work
      work = booking.convert_to_work
      assert_not_nil work
      assert_equal "scheduled", work.status
      
      # Now cancel the booking
      booking.cancel
      booking.reload
      work.reload
      
      assert_equal "cancelled", booking.status
      assert_equal "cancelled", work.status
    end
  end
  
  test "completing a booking" do
    ActsAsTenant.with_tenant(@company) do
      # Confirm the booking first
      @booking.confirm
      @booking.reload
      
      # Complete the booking
      @booking.complete
      @booking.reload
      
      assert_equal "completed", @booking.status
    end
  end
  
  test "validation preventing changes to cancelled bookings" do
    ActsAsTenant.with_tenant(@company) do
      # Cancel the booking
      @booking.cancel
      @booking.reload
      
      # Try to update it
      @booking.client_name = "Changed Name"
      assert_not @booking.save
      assert_includes @booking.errors[:base], "Zrušenou rezervaci nelze upravovat."
    end
  end
  
  test "validation requiring either email or phone" do
    ActsAsTenant.with_tenant(@company) do
      # Try to create a booking without email or phone
      booking = Booking.new(
        client_name: "No Contact Info",
        preferred_date: Date.current + 9.days,
        preferred_period: "afternoon",
        status: "pending",
        company: @company,
        booking_link: @booking_link
      )
      
      assert_not booking.save
      assert_includes booking.errors[:base], "Musí být zadaný alespoň 1 kontakt: e-mail nebo telefon."
      
      # Add email
      booking.client_email = "<EMAIL>"
      assert booking.save
      
      # Or with just phone
      booking = Booking.new(
        client_name: "Phone Only",
        client_phone: "+420111222333",
        preferred_date: Date.current + 9.days,
        preferred_period: "afternoon",
        status: "pending",
        company: @company,
        booking_link: @booking_link
      )
      
      assert booking.save
    end
  end
  
  test "syncing booking changes to work" do
    ActsAsTenant.with_tenant(@company) do
      # Confirm the booking and convert to work
      @booking.confirm
      @booking.reload
      work = @booking.convert_to_work
      
      # Change the booking details
      new_date = Date.current + 10.days
      new_period = "afternoon"
      new_time = "15:30"
      
      @booking.update(
        preferred_date: new_date,
        preferred_period: new_period,
        specific_time: new_time
      )
      
      work.reload
      
      # The work should have the updated values
      assert_equal new_date, work.scheduled_start_date
      assert_equal new_period, work.preferred_period
      assert_equal new_time, work.specific_time
    end
  end
  
  test "public update validation" do
    ActsAsTenant.with_tenant(@company) do
      @booking.public_update = true
      
      # Try to change non-allowed attributes
      assert_not @booking.update(client_name: "Changed Name")
      assert_includes @booking.errors[:base], "Lze upravovat pouze datum a čas rezervace."
      
      # Try to change allowed attributes
      assert @booking.update(
        preferred_date: Date.current + 11.days,
        preferred_period: "afternoon",
        specific_time: "17:00"
      )
    end
  end
end 