# AttendifyApp Testing Guide

## Overview

This project uses a comprehensive testing stack:
- **Backend**: RSpec for Ruby/Rails testing
- **Frontend**: Vitest for Vue component testing  
- **E2E**: Playwright for end-to-end testing

## Running Tests

### Frontend Unit Tests
```bash
npm test                # Run in watch mode
npm test -- --run       # Run once
npm run test:ui         # Run with UI
npm run test:coverage   # Run with coverage report
```

### E2E Tests
```bash
npm run test:e2e        # Run all E2E tests
npm run test:e2e:ui     # Run with Playwright UI
npm run test:e2e:debug  # Debug mode
```

### Backend Tests
```bash
bundle exec rspec       # Run all RSpec tests
bundle exec rspec spec/models  # Run specific directory
bundle exec rspec spec/models/user_spec.rb  # Run specific file
```

## Test Structure

```
test/
├── frontend/           # Vitest unit tests
│   ├── components/     # Vue component tests
│   ├── stores/         # Vuex store tests
│   ├── utils/          # Test utilities and mocks
│   └── setup.ts        # Test setup and global mocks
├── e2e/                # Playwright E2E tests
│   ├── fixtures/       # Test fixtures and helpers
│   ├── pages/          # Page objects
│   └── *.spec.ts       # Test specifications
└── README.md           # This file

spec/                   # RSpec backend tests
├── models/             # Model tests
├── controllers/        # Controller tests
├── services/           # Service object tests
└── factories/          # FactoryBot factories
```

## Writing Tests

### Frontend Component Test Example
```typescript
import { describe, it, expect } from 'vitest'
import { renderWithProviders } from '../utils/test-utils'
import MyComponent from '@/components/MyComponent.vue'

describe('MyComponent', () => {
  it('renders correctly', () => {
    const { getByText } = renderWithProviders(MyComponent, {
      props: { title: 'Test Title' }
    })
    
    expect(getByText('Test Title')).toBeInTheDocument()
  })
})
```

### E2E Test Example
```typescript
import { test, expect } from '@playwright/test'

test('user can login', async ({ page }) => {
  await page.goto('/users/sign_in')
  await page.fill('input[type="email"]', '<EMAIL>')
  await page.fill('input[type="password"]', 'password')
  await page.click('button[type="submit"]')
  
  await expect(page).toHaveURL('/mainbox')
})
```

### RSpec Test Example
```ruby
RSpec.describe User, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:email) }
    it { should validate_uniqueness_of(:email) }
  end
end
```

## Best Practices

1. **Test Naming**: Use descriptive test names that explain the behavior
2. **Test Isolation**: Each test should be independent
3. **Mock External Services**: Use MSW for API mocking in frontend tests
4. **Use Page Objects**: For E2E tests, use page objects for better maintainability
5. **Test User Behavior**: Focus on testing what users do, not implementation details
6. **Keep Tests Fast**: Mock heavy operations and use test databases

## Debugging

- Frontend tests: Use `console.log` or VS Code debugger with Vitest
- E2E tests: Use `npm run test:e2e:debug` or Playwright Inspector
- Backend tests: Use `binding.pry` or debugger statements

## Coverage

- Frontend: Run `npm run test:coverage` to see coverage report
- Backend: SimpleCov generates coverage reports in `coverage/` directory