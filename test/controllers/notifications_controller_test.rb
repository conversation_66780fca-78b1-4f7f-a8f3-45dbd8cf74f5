require "test_helper"

class NotificationsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @user = users(:john)
    @company = companies(:acme_corp)
    @work = works(:scheduled_work)
    sign_in @user
    
    # Create some test notifications
    @unread_notification = Notification.create!(
      user: @user,
      company: @company,
      notifiable: @work,
      notification_type: 'work_assignment_added',
      title: 'New assignment',
      message: 'You have been assigned to a work'
    )
    
    @read_notification = Notification.create!(
      user: @user,
      company: @company,
      notifiable: @work,
      notification_type: 'work_status_changed',
      title: 'Status changed',
      message: 'Work status has changed',
      read_at: 1.hour.ago
    )
  end

  test "should get all notifications" do
    get api_v1_notifications_url
    assert_response :success
    
    json_response = JSON.parse(response.body)
    assert_equal 2, json_response['notifications'].length
  end

  test "should get unread notifications" do
    get unread_api_v1_notifications_url
    assert_response :success
    
    json_response = JSON.parse(response.body)
    assert_equal 1, json_response['notifications'].length
    assert_equal @unread_notification.id, json_response['notifications'].first['id']
  end

  test "should mark notification as read" do
    assert_nil @unread_notification.read_at
    
    patch mark_as_read_api_v1_notification_url(@unread_notification)
    assert_response :success
    
    @unread_notification.reload
    assert_not_nil @unread_notification.read_at
  end

  test "should delete notification" do
    assert_difference 'Notification.count', -1 do
      delete api_v1_notification_url(@unread_notification)
    end
    assert_response :success
  end

  test "should mark all notifications as read" do
    # Create another unread notification
    another_unread = Notification.create!(
      user: @user,
      company: @company,
      notifiable: @work,
      notification_type: 'work_check_in_reminder',
      title: 'Check-in reminder',
      message: 'Remember to check in'
    )
    
    post mark_all_as_read_api_v1_notifications_url
    assert_response :success
    
    assert_equal 0, @user.notifications.unread.count
    assert @unread_notification.reload.read_at.present?
    assert another_unread.reload.read_at.present?
  end

  test "should delete all notifications" do
    assert_difference 'Notification.count', -2 do
      delete destroy_all_api_v1_notifications_url
    end
    assert_response :success
    
    assert_equal 0, @user.notifications.count
  end

  test "should not access other user's notifications" do
    other_user = users(:jane)
    other_notification = Notification.create!(
      user: other_user,
      company: @company,
      notifiable: @work,
      notification_type: 'work_assignment_added',
      title: 'Other user notification',
      message: 'This belongs to another user'
    )
    
    patch mark_as_read_api_v1_notification_url(other_notification)
    assert_response :not_found
    
    delete api_v1_notification_url(other_notification)
    assert_response :not_found
  end

  test "notification response includes necessary attributes" do
    get api_v1_notifications_url
    assert_response :success
    
    json_response = JSON.parse(response.body)
    notification = json_response['notifications'].first
    
    assert notification.key?('id')
    assert notification.key?('notification_type')
    assert notification.key?('title')
    assert notification.key?('message')
    assert notification.key?('notifiable_type')
    assert notification.key?('notifiable_id')
    assert notification.key?('read_at')
    assert notification.key?('created_at')
  end
end