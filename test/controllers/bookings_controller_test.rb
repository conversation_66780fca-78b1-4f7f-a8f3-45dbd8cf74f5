require "test_helper"

class BookingsControllerTest < NoActiveRecordTestCase
  setup do
    # Initialize routes
    @routes = Rails.application.routes
    @controller = BookingsController.new
    
    @company = mock("Company")
    ActsAsTenant.stubs(:current_tenant).returns(@company)
    
    @booking_link = mock("BookingLink")
    @work = mock("Work")
    @booking = mock("Booking")
    bookings_array = mock("BookingsArray")
    
    # This is the key change - bookings_array.find must return @booking, not an enumerator
    @company.stubs(:bookings).returns(bookings_array)
    bookings_array.stubs(:find).with("1").returns(@booking)
    bookings_array.stubs(:includes).returns(bookings_array)
    bookings_array.stubs(:order).returns(bookings_array)
    bookings_array.stubs(:as_json).returns([])
    
    # Common methods that might be called on booking
    @booking.stubs(:preferred_date).returns(Date.today)
    @booking.stubs(:preferred_period).returns("morning")
    @booking.stubs(:specific_time).returns(nil)
    @booking.stubs(:as_json).returns({})
    
    # Mock authentication
    @warden = mock("Warden")
    @user = mock("User")
    @user.stubs(:id).returns(1)
    @user.stubs(:email).returns("<EMAIL>")
    @warden.stubs(:authenticate).returns(@user)
    @warden.stubs(:authenticate!).returns(@user)
    @warden.stubs(:authenticated?).returns(true)
    @warden.stubs(:user).returns(@user)
    request.env["warden"] = @warden
    
    # Skip before actions
    ApplicationController.any_instance.stubs(:require_login).returns(true)
    BookingsController.any_instance.stubs(:check_subscription).returns(true)
    BookingsController.any_instance.stubs(:authenticate_user!).returns(true)
    
    # Skip template rendering for most tests
    # Individual tests that want to verify JSON responses will override this
    BookingsController.any_instance.stubs(:render).returns(true)
  end
  
  test "should get index" do
    get :index
    assert_response :success
  end
  
  test "should show booking" do
    # Since the show method is empty and the controller might not be rendering a template,
    # we can skip the assertion about the render call
    get :show, params: { id: "1" }, format: :json
    assert_response :success
  end

  test "should update booking" do
    @booking.expects(:update).returns(true)
    
    # Mock mailer
    mailer = mock("Mailer")
    mailer.expects(:deliver_now).returns(true)
    BookingMailer.expects(:booking_updated_email).returns(mailer)
    
    patch :update, params: { id: "1", booking: { preferred_date: Date.today } }
    assert_response :success
  end
  
  test "should not update booking with invalid params" do
    @booking.expects(:update).returns(false)
    
    patch :update, params: { id: "1", booking: { preferred_date: nil } }
    assert_response :success
  end

  test "should confirm booking with existing date" do
    @booking.expects(:confirm).with(Date.today, nil).returns(true)
    
    post :confirm, params: { id: "1" }
    assert_response :success
  end
  
  test "should confirm booking with provided date and time" do
    @booking.expects(:confirm).with(Date.today.to_s, "10:00").returns(true)
    
    post :confirm, params: { id: "1", booking: { preferred_date: Date.today.to_s, specific_time: "10:00" } }
    assert_response :success
  end
  
  test "should not confirm booking when confirm fails" do
    @booking.expects(:confirm).returns(false)
    @booking.stubs(:errors).returns(mock(full_messages: ['Error message']))
    
    post :confirm, params: { id: "1" }
    assert_response :success
  end
  
  test "should fetch bookings" do
    bookings = mock("Bookings")
    @company.expects(:bookings).returns(bookings)
    bookings.expects(:includes).with(:booking_link, :work).returns(bookings)
    bookings.expects(:order).with(created_at: :desc).returns(bookings)
    bookings.expects(:as_json).returns([])
    @company.expects(:slug).returns("company-slug")
    
    get :fetch, format: :json
    assert_response :success
  end

  test "should convert booking to work" do
    # Set up the @booking mock with the expected method calls
    work = @work
    @booking.expects(:convert_to_work).returns(work)
    @booking.expects(:reload).returns(@booking)
    
    # Override the generic render stub and check for specific JSON response
    BookingsController.any_instance.unstub(:render)
    @controller.expects(:render).with(json: { 
      success: true, 
      booking: @booking, 
      work: work, 
      message: 'Rezervace byla převedena do práce' 
    })
    
    # Call the action normally
    post :convert_to_work, params: { id: "1" }, format: :json
    
    # Verify the response
    assert_response :success
  end
  
  test "should not convert booking to work when convert fails" do
    # Set up the @booking mock to return nil for convert_to_work
    @booking.expects(:convert_to_work).returns(nil)
    error_messages = ['Error message']
    error_mock = mock
    error_mock.stubs(:full_messages).returns(error_messages)
    @booking.stubs(:errors).returns(error_mock)
    
    # Override the generic render stub and check for specific JSON response
    BookingsController.any_instance.unstub(:render)
    @controller.expects(:render).with(json: { 
      success: false, 
      errors: error_messages 
    })
    
    # Call the action normally
    post :convert_to_work, params: { id: "1" }, format: :json
    
    # Verify the response
    assert_response :success
  end
  
  test "should cancel booking" do
    # Set up the @booking mock with expected method calls
    @booking.expects(:cancel).returns(true)
    
    # Override the generic render stub and check for specific JSON response
    BookingsController.any_instance.unstub(:render)
    @controller.expects(:render).with(json: { 
      success: true, 
      booking: @booking, 
      message: 'Rezervace byla zrušena.' 
    })
    
    # Call the action normally
    post :cancel, params: { id: "1" }, format: :json
    
    # Verify the response
    assert_response :success
  end
  
  test "should not cancel booking when cancel fails" do
    # Set up the @booking mock to fail when cancel is called
    @booking.expects(:cancel).returns(false)
    error_messages = ['Error message']
    error_mock = mock
    error_mock.stubs(:full_messages).returns(error_messages)
    @booking.stubs(:errors).returns(error_mock)
    
    # Override the generic render stub and check for specific JSON response
    BookingsController.any_instance.unstub(:render)
    @controller.expects(:render).with(json: { 
      success: false, 
      errors: error_messages 
    })
    
    # Call the action normally
    post :cancel, params: { id: "1" }, format: :json
    
    # Verify the response
    assert_response :success
  end
end