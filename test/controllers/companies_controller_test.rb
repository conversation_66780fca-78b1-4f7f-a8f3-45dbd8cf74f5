require "test_helper"

class CompaniesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @company = companies(:one)
    
    @user = User.create!(email: "<EMAIL>", password: "password")
    #@role = Role.create!(name: "owner")
    sign_in @user
  end

  test "user can create a company with a role" do
    Rails.logger.level = Logger::DEBUG
    ActiveRecord::Base.logger = Logger.new(STDOUT)

    # POST request to create a company
    assert_difference -> { Company.count } => 1, -> { CompanyUserRole.count } => 1 do
      post companies_url, params: { company: { name: "New Company" }, role: "owner" }
    end

    # Check response
    assert_response :redirect
    follow_redirect!
    assert_match "New Company", response.body
  end

  test "should get index" do
    get companies_url
    assert_response :success
  end

  test "should get new" do
    get new_company_url
    assert_response :success
  end

  test "should create company" do
    assert_difference("Company.count") do
      post companies_url, params: { company: { name: @company.name, slug: @company.slug, subdomain: @company.subdomain } }
    end

    assert_redirected_to company_url(Company.last)
  end

  test "should show company" do
    get company_url(@company)
    assert_response :success
  end

  test "should get edit" do
    get edit_company_url(@company)
    assert_response :success
  end

  test "should update company" do
    patch company_url(@company), params: { company: { name: @company.name, slug: @company.slug, subdomain: @company.subdomain } }
    assert_redirected_to company_url(@company)
  end

  test "should destroy company" do
    assert_difference("Company.count", -1) do
      delete company_url(@company)
    end

    assert_redirected_to companies_url
  end
end
