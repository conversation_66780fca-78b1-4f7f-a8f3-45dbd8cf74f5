require "test_helper"

class EventsControllerTest < NoActiveRecordTestCase
  def setup
    super
    @controller = EventsController.new
    
    @company = mock('Company')
    @user = mock('User')
    @contract = mock('Contract')
    @event = mock('Event')
    @events = mock('Array')
    @works = mock('Array')
    @contracts = mock('Contracts')
    @holidays = []

    # Mock company methods
    @company.stubs(:id).returns(1)
    @company.stubs(:name).returns('Test Company')
    @company.stubs(:events).returns(@events)
    @company.stubs(:works).returns(@works)

    # Mock user methods
    @user.stubs(:id).returns(1)
    @user.stubs(:email).returns('<EMAIL>')
    @user.stubs(:contracts).returns(@contracts)

    # Mock contracts collection methods
    @contracts.stubs(:find_by).with(company: @company).returns(@contract)

    # Mock contract methods
    @contract.stubs(:id).returns(1)
    @contract.stubs(:company).returns(@company)
    @contract.stubs(:events).returns(@events)
    @contract.stubs(:first_name).returns('John')
    @contract.stubs(:last_name).returns('Doe')

    # Mock events collection methods
    @events.stubs(:new).returns(@event)
    @events.stubs(:find).returns(@event)
    @events.stubs(:where).returns(@events)
    @events.stubs(:includes).returns(@events)
    @events.stubs(:as_json).returns([])

    # Mock works collection methods
    @works.stubs(:where).returns(@works)
    @works.stubs(:includes).returns(@works)
    @works.stubs(:as_json).returns([])

    # Mock event instance methods
    @event.stubs(:id).returns(1)
    @event.stubs(:user_id=).returns(1)
    @event.stubs(:save).returns(true)
    @event.stubs(:destroy).returns(true)
    @event.stubs(:as_json).returns({
      id: 1,
      title: 'Test Event',
      start_time: '2024-01-01 09:00:00',
      end_time: '2024-01-01 17:00:00',
      event_type: 'holiday'
    })

    # Set up authentication
    ActsAsTenant.stubs(:current_tenant).returns(@company)
    @controller.stubs(:current_user).returns(@user)
    @controller.stubs(:authenticate_user!).returns(true)

    # Mock Event class methods
    Event.stubs(:find).returns(@event)
  end

  test "should get index" do
    get :index
    assert_response :success
  end

  test "should fetch events data" do
    date = Date.today
    
    # Mock holiday fetcher
    @controller.expects(:fetch_holidays).returns([])
    
    get :fetch, params: { date: date.to_s }, format: :json
    assert_response :success
    
    response_data = JSON.parse(@response.body)
    assert_not_nil response_data["events"]
    assert_not_nil response_data["works"]
    assert_not_nil response_data["holidays"]
  end

  test "should not fetch events with invalid date" do
    Date.stubs(:parse).with("invalid-date").raises(Date::Error.new("invalid date"))
    @controller.stubs(:safe_parse_date).returns(nil)
    
    get :fetch, params: { date: "invalid-date" }, format: :json
    assert_response :unprocessable_entity
    response_data = JSON.parse(@response.body)
    assert_equal "Invalid date format", response_data["error"]
  end

  test "should create event" do
    event_params = {
      event_type: "Meeting",
      title: "Test Event",
      start_time: Time.current,
      end_time: Time.current + 1.hour,
      description: "Team meeting",
      place: "Conference Room"
    }
    
    post :create, params: { event: event_params }, format: :json
    assert_response :created
    response_data = JSON.parse(@response.body)
    assert_equal true, response_data["success"]
    assert_not_nil response_data["event"]
    assert_equal "Událost byla úspěšně vytvořena.", response_data["message"]
  end

  test "should not create event with invalid params" do
    invalid_event = mock('InvalidEvent')
    invalid_event.stubs(:user_id=).returns(1)
    invalid_event.stubs(:save).returns(false)
    invalid_event.stubs(:errors).returns(mock('Errors', full_messages: ["Error message"]))
    
    @events.stubs(:new).returns(invalid_event)
    
    post :create, params: {
      event: {
        event_type: "",
        title: "",
        start_time: "",
        end_time: "",
        description: "",
        place: ""
      }
    }, format: :json
    
    assert_response :unprocessable_entity
    response_data = JSON.parse(@response.body)
    assert_not_nil response_data["message"]
    assert_equal "error", response_data["messageType"]
  end

  test "should destroy event" do
    delete :destroy, params: { id: "1" }, format: :json
    assert_response :no_content
  end

  test "should redirect to root if no contract exists" do
    @contracts.stubs(:find_by).with(company: @company).returns(nil)
    
    get :index
    assert_redirected_to root_path
    assert_equal "Nejdřive sa musíte připojit k Pracovnímu prostoru.", flash[:alert]
  end

  test "should require authentication" do
    @controller.unstub(:authenticate_user!)
    @controller.stubs(:authenticate_user!).raises("Unauthorized")
    
    assert_raises(RuntimeError) do
      get :index
    end
  end
end
