require 'test_helper'
require 'webmock/minitest'

# This controller test is designed to run without accessing the database
# It uses mocks instead of fixtures for all external dependencies
# It inherits from NoActiveRecordTestCase which disables database access
class PublicMeetingsControllerTest < NoActiveRecordTestCase
  # Explicitly disable fixtures and transactional tests
  self.use_transactional_tests = false
  
  def setup
    super
    @controller = PublicMeetingsController.new
    
    # Create mock objects
    @company = mock('Company')
    @company.stubs(:id).returns(1)
    @company.stubs(:name).returns('Test Company')
    @company.stubs(:works).returns([])
    
    @meeting = mock('Meeting')
    @meeting.stubs(:id).returns(1)
    @meeting.stubs(:title).returns('Test Meeting')
    @meeting.stubs(:description).returns('Test Description')
    @meeting.stubs(:place).returns('Test Place')
    @meeting.stubs(:day_options).returns({ "2024-03-01" => true, "2024-03-02" => true })
    @meeting.stubs(:confirmed_date).returns(nil)
    @meeting.stubs(:company).returns(@company)
    @meeting.stubs(:meeting_users).returns([])
    @meeting.stubs(:as_json).returns({
      id: 1,
      title: 'Test Meeting',
      description: 'Test Description',
      place: 'Test Place',
      day_options: { "2024-03-01" => true, "2024-03-02" => true },
      confirmed_date: nil
    })
    
    @meeting_user = mock('MeetingUser')
    @meeting_user.stubs(:id).returns(1)
    @meeting_user.stubs(:email).returns('<EMAIL>')
    @meeting_user.stubs(:token).returns('valid_token')
    @meeting_user.stubs(:meeting).returns(@meeting)
    @meeting_user.stubs(:contract).returns(nil)
    @meeting_user.stubs(:selected_dates).returns(nil)
    @meeting_user.stubs(:as_json).returns({
      id: 1,
      email: '<EMAIL>',
      selected_dates: nil
    })
    
    setup_holidays_stub
  end

  test "should show public meeting with valid token" do
    MeetingUser.stubs(:find_by).with(token: 'valid_token').returns(@meeting_user)
    
    get :show, params: { token: 'valid_token' }
    assert_response :success
  end

  test "should not show public meeting with invalid token" do
    MeetingUser.stubs(:find_by).with(token: 'invalid_token').returns(nil)
    
    get :show, params: { token: 'invalid_token' }
    assert_response :forbidden
    assert_equal "Link je neaktivní", JSON.parse(@response.body)["error"]
  end

  test "should get meeting data with valid token" do
    MeetingUser.stubs(:find_by).with(token: 'valid_token').returns(@meeting_user)
    
    # Specify the request format as JSON
    @request.accept = 'application/json'
    
    # We need to manually set up the controller to handle the response
    @controller.stubs(:render).returns(true)
    
    get :meeting, params: { token: 'valid_token' }
    assert_response :success
  end

  test "should not get meeting data with invalid token" do
    MeetingUser.stubs(:find_by).with(token: 'invalid_token').returns(nil)
    
    get :meeting, params: { token: 'invalid_token' }
    assert_response :not_found
    assert_equal "Invalid or expired token", JSON.parse(@response.body)["error"]
  end

  test "should update selected dates for meeting user" do
    MeetingUser.stubs(:find_by).with(token: 'valid_token').returns(@meeting_user)
    
    selected_dates = { "2024-03-01" => true, "2024-03-02" => true }
    @meeting_user.expects(:update).with(selected_dates: kind_of(ActionController::Parameters)).returns(true)
    
    @meeting.expects(:all_responses_submitted?).returns(false)
    
    patch :update, params: { token: 'valid_token', selected_dates: selected_dates }
    
    assert_redirected_to root_path
    assert_equal 'Vaše preferene byly odeslány. Na e-mail vám přijde zpráva s potvrzením termínu schůzky.', flash[:notice]
  end

  test "should not update selected dates for confirmed meeting" do
    MeetingUser.stubs(:find_by).with(token: 'valid_token').returns(@meeting_user)
    @meeting.stubs(:confirmed_date).returns(DateTime.now)
    
    patch :update, params: { token: 'valid_token', selected_dates: { "2024-03-01" => true, "2024-03-02" => true } }
    
    assert_redirected_to root_path
    assert_equal 'Schůzka je ji ust len  nebo token je neplatn ', flash[:alert]
  end

  test "should not update selected dates with invalid token" do
    MeetingUser.stubs(:find_by).with(token: 'invalid_token').returns(nil)
    
    patch :update, params: { token: 'invalid_token', selected_dates: { "2024-03-01" => true, "2024-03-02" => true } }
    
    assert_redirected_to root_path
    assert_equal 'Schůzka je ji ust len  nebo token je neplatn ', flash[:alert]
  end

  test "should find best common time when all responses are submitted" do
    MeetingUser.stubs(:find_by).with(token: 'valid_token').returns(@meeting_user)
    
    # Setup expectations for this test
    selected_dates = { "2024-03-01" => true, "2024-03-02" => true }
    @meeting_user.expects(:update).with(selected_dates: kind_of(ActionController::Parameters)).returns(true)
    @meeting.expects(:all_responses_submitted?).returns(true)
    
    # Create a DateTime object for the expected date
    best_date = "2024-03-01"
    expected_date = DateTime.parse(best_date)
    
    @meeting.expects(:find_best_common_time).returns(best_date)
    @meeting.expects(:update).with(confirmed_date: expected_date).returns(true)
    
    # Mock array of meeting users
    mock_meeting_users = [@meeting_user]
    @meeting.stubs(:meeting_users).returns(mock_meeting_users)
    
    # Setup mail expectations
    mock_mailer = mock('MeetingMailer')
    mock_mailer.expects(:deliver_later).returns(true)
    MeetingMailer.expects(:confirmation_email).with(@meeting_user).returns(mock_mailer)
    
    patch :update, params: { token: 'valid_token', selected_dates: selected_dates }
    
    assert_redirected_to root_path
  end

  private

  def setup_holidays_stub
    stub_request(:get, /openholidaysapi\.org/)
      .to_return(
        status: 200,
        body: [
          { "startDate" => "2024-03-01" },
          { "startDate" => "2024-03-02" }
        ].to_json,
        headers: { 'Content-Type' => 'application/json' }
      )
  end
end 