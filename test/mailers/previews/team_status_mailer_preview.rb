# Preview all emails at http://0.0.0.0:5100/rails/mailers/team_status_mailer
class TeamStatusMailerPreview < ActionMailer::Preview

  # Preview this email at http://0.0.0.0:5100/rails/mailers/team_status_mailer/daily_report
  def daily_report
    owner = OpenStruct.new(email: "<EMAIL>", name: "<PERSON>")
    company = OpenStruct.new(name: "Example Corp")
    employees = [
      OpenStruct.new(name: "<PERSON>", status: "Present"),
      OpenStruct.new(name: "<PERSON>", status: "Absent")
    ]
    locale = "sk"

    TeamStatusMailer.daily_report(owner, company, employees, locale)
  end

end