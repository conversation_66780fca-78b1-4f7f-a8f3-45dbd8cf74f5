class MeetingMailerPreview < ActionMailer::Preview
  
  private

  def create_sample_data
    @company = OpenStruct.new(
      name: "Example Corp"
    )
    
    @meeting = OpenStruct.new(
      title: "Quarterly Planning Meeting",
      company: @company,
      created_by: OpenStruct.new(
        email: "<EMAIL>",
        name: "<PERSON>"
      ),
      confirmed_date: Time.current + 1.week,
      description: "Important quarterly planning meeting"
    )
    
    @meeting_user = OpenStruct.new(
      email: "<EMAIL>",
      name: "<PERSON>",
      token: "sample-token-12345",
      meeting: @meeting
    )
  end

  public

  # Preview this email at http://0.0.0.0:5100/rails/mailers/meeting_mailer/invitation_email
  def invitation_email
    create_sample_data
    MeetingMailer.invitation_email(@meeting, @meeting_user)
  end

  # Preview this email at http://0.0.0.0:5100/rails/mailers/meeting_mailer/confirmation_email
  def confirmation_email
    create_sample_data
    MeetingMailer.confirmation_email(@meeting_user)
  end

  # Preview this email at http://0.0.0.0:5100/rails/mailers/meeting_mailer/date_extension_email
  def date_extension_email
    create_sample_data
    MeetingMailer.date_extension_email(@meeting_user)
  end
end