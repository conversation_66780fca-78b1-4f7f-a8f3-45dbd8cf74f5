class ReportMailerPreview < ActionMailer::Preview
  
  # Preview this email at http://0.0.0.0:5100/rails/mailers/report_mailer/attendance_report
  # Template at: /app/views/report_mailer/attendance_report.html.erb
  def attendance_report
    recipient = "<EMAIL>"
    pdf_data = "Fake PDF Content" 
    filename = "attendance_report.pdf"
    employee_name = "<PERSON>"
    selected_date = "Únor 2025"
    total_hours = "40"

    ReportMailer.attendance_report(recipient, pdf_data, filename, employee_name, selected_date, total_hours)
  end
end