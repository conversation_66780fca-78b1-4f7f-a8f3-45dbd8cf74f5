class CompanyConnectionMailerPreview < ActionMailer::Preview
  
  # Preview this email at http://0.0.0.0:5100/rails/mailers/company_connection_mailer/existing_user_notification
  def existing_user_notification
    sender = User.first || FactoryBot.create(:user)
    user = User.second || FactoryBot.create(:user)
    company = Company.first || FactoryBot.create(:company)
    contract = Contract.first || FactoryBot.create(:contract, company: company)

    CompanyConnectionMailer.existing_user_notification(
      sender: sender,
      user: user,
      company: company,
      contract: contract
    )
  end

  # Preview this email at http://0.0.0.0:5100/rails/mailers/company_connection_mailer/new_user_invitation
  def new_user_invitation
    sender = User.first || FactoryBot.create(:user)
    company = Company.first || FactoryBot.create(:company)
    contract = Contract.first || FactoryBot.create(:contract, company: company)
    token = "sample_invitation_token"

    CompanyConnectionMailer.new_user_invitation(
      sender: sender,
      email: "<EMAIL>",
      company: company,
      contract: contract,
      token: token
    )
  end

end