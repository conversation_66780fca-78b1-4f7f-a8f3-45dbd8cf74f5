class DeviseMailerPreview < ActionMailer::Preview
  
  # Preview this email at http://0.0.0.0:5100/rails/mailers/devise_mailer/invitation_instructions
  # Template at: /app/views/devise/mailer/invitation_instructions.html.erb
  def invitation_instructions
    user = User.new(email: "<EMAIL>")
    token = "dummy_invite_token"
    Devise::Mailer.invitation_instructions(user, token)
  end

  # Preview this email at http://0.0.0.0:5100/rails/mailers/devise_mailer/confirmation_instructions
  def confirmation_instructions
    user = User.new(email: "<EMAIL>", confirmation_token: "dummy_token")
    Devise::Mailer.confirmation_instructions(user, user.confirmation_token)
  end

  # Preview this email at http://0.0.0.0:5100/rails/mailers/devise_mailer/reset_password_instructions
  def reset_password_instructions
    user = User.new(email: "<EMAIL>", reset_password_token: "dummy_token")
    Devise::Mailer.reset_password_instructions(user, user.reset_password_token)
  end
end