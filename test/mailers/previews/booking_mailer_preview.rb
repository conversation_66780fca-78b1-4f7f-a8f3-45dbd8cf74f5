class BookingMailerPreview < ActionMailer::Preview
  private

  def create_sample_data
    @user = OpenStruct.new(
      email: "<EMAIL>"
    )
  
    @company_user_role = OpenStruct.new(
      user: @user,
      role: OpenStruct.new(name: "owner")
    )
  
    @company = OpenStruct.new(
      name: "Sample Company",
      slug: "sample-company",
      company_user_roles: [@company_user_role],
      users: [@user]
    )
  
    # Create a chainable mock object
    mock_chain = Class.new do
      def initialize(final_result)
        @final_result = final_result
      end
  
      def joins(*_args)
        self
      end
  
      def where(*_args)
        self
      end
  
      def pluck(*_args)
        @final_result
      end
    end
  
    # Add methods to company to mimic ActiveRecord behavior
    def @company.company_user_roles
      @mock_chain ||= mock_chain.new(["<EMAIL>"])
    end
  
    @booking_link = OpenStruct.new(
      name: "Consultation Meeting"
    )
  
    @booking = OpenStruct.new(
      client_email: "<EMAIL>",
      client_name: "John Client",
      access_token: "sample-token-12345",
      booking_link: @booking_link,
      company: @company,
      preferred_date: Time.current + 2.days, 
      preferred_period: "morning",          
      specific_time: Time.current.change(hour: 10, min: 30),
      start_time: Time.current + 2.days + 9.hours, 
      end_time: Time.current + 2.days + 11.hours   
    )
  end

  public

  # Preview all emails at http://localhost:5100/rails/mailers/booking_mailer


  # Preview at http://localhost:5100/rails/mailers/booking_mailer/confirmation_email
  def confirmation_email
    create_sample_data
    BookingMailer.confirmation_email(@booking)
  end

  # Preview at http://localhost:5100/rails/mailers/booking_mailer/access_email
  def access_email
    create_sample_data
    BookingMailer.access_email(@booking)
  end

  # Preview at http://localhost:5100/rails/mailers/booking_mailer/booking_notification_new
  def booking_notification_new
    create_sample_data
    BookingMailer.booking_notification(@booking, :new, @user.email)
  end

  # Preview at http://localhost:5100/rails/mailers/booking_mailer/booking_notification_update
  def booking_notification_update
    create_sample_data
    BookingMailer.booking_notification(@booking, :update, @user.email)
  end

  # Preview at http://localhost:5100/rails/mailers/booking_mailer/booking_notification_cancel
  def booking_notification_cancel
    create_sample_data
    BookingMailer.booking_notification(@booking, :cancel, @user.email)
  end

  # Preview at http://localhost:5100/rails/mailers/booking_mailer/booking_updated_email
  def booking_updated_email
    create_sample_data
    original_details = {
      start_time: (Time.current + 7.days).to_s, # Ensure it's a valid string
      end_time: (Time.current + 7.days + 1.hour).to_s, # Ensure it's a valid string
      preferred_period: "night",
      preferred_date: (Time.current - 1.day).to_date.to_s # Ensure it's a valid string
    }
    BookingMailer.booking_updated_email(@booking, original_details)
  end

  # Preview at http://localhost:5100/rails/mailers/booking_mailer/booking_confirmed_email
  def booking_confirmed_email
    create_sample_data
    @booking.confirmed_time = Time.current + 2.days + 14.hours
    BookingMailer.booking_confirmed_email(@booking)
  end

  # Preview at http://localhost:5100/rails/mailers/booking_mailer/booking_cancelled_email
  def booking_cancelled_email
    create_sample_data
    BookingMailer.booking_cancelled_email(@booking)
  end


end