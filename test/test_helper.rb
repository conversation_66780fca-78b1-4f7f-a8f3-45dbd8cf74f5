require "simplecov"
SimpleCov.start "rails" do
  add_filter "/bin/"
  add_filter "/db/"
  add_filter "/test/"
  add_filter "/config/"
end

ENV["RAILS_ENV"] ||= "test"
require_relative "../config/environment"
require "rails/test_help"
require "mocha/minitest"

module ActiveRecord
  class Base
    mattr_accessor :shared_connection
    @@shared_connection = nil

    def self.connection
      @@shared_connection || retrieve_connection
    end
  end
end

# Disable fixture loading for specific test cases
module ActiveRecord
  module TestFixtures
    alias_method :original_setup_fixtures, :setup_fixtures
    
    def setup_fixtures
      if self.class.ancestors.any? { |klass| klass.name == 'NoActiveRecordTestCase' }
        # Skip fixture loading for NoActiveRecordTestCase descendants
        @fixture_cache = {}
        @fixture_connections = []
        @loaded_fixtures = {}
      else
        original_setup_fixtures
      end
    end
  end
end

class ActiveSupport::TestCase
  include FactoryBot::Syntax::Methods if defined?(FactoryBot)
  
  # Run tests in parallel with specified workers
  parallelize(workers: :number_of_processors)
  
  # Setup all fixtures in test/fixtures/*.yml for all tests in alphabetical order.
  fixtures :all
  
  setup do
    # Reset any Mocha stubs/expectations from previous tests
    mocha_teardown
    # Setup new Mocha context for this test
    mocha_setup
    # Initialize @instances as an empty array
    @instances = []
  end

  teardown do
    # Clean up any remaining Mocha stubs/expectations
    mocha_teardown
    # Clean up created records
    if @instances.present?
      @instances.each(&:destroy)
      @instances.clear
    end
  end

  def before_setup
    super
    # Disable referential integrity for fixture loading
    connection = ActiveRecord::Base.connection
    connection.execute('SET session_replication_role = replica;')
  end

  def after_teardown
    # Re-enable referential integrity
    connection = ActiveRecord::Base.connection
    connection.execute('SET session_replication_role = DEFAULT;')
    super
  end

  private

  def disable_referential_integrity
    connection = ActiveRecord::Base.connection
    begin
      connection.execute('SET session_replication_role = replica;')
      yield
    ensure
      connection.execute('SET session_replication_role = DEFAULT;')
    end
  end
end

# Create a class with fixtures disabled for the PublicMeetingsControllerTest
class ActionController::TestCase
  include Rails.application.routes.url_helpers
  include ActionDispatch::TestProcess
  
  # Skip loading fixtures for controller tests that inherit from this class
  def self.inherited(subclass)
    subclass.use_transactional_tests = false
    super
  end
  
  def setup
    @routes = Rails.application.routes
    @instances = []
    Mocha::Mockery.instance.stubba.unstub_all
  end

  def teardown
    if @instances.present?
      @instances.each(&:destroy)
      @instances.clear
    end
    Mocha::Mockery.instance.stubba.unstub_all
  end
end

# Create a class that doesn't load any fixtures
class NoActiveRecordTestCase < Minitest::Test
  include ActiveSupport::Testing::TimeHelpers
  include ActiveSupport::Testing::Assertions
  include Minitest::Assertions
  include ActiveSupport::Testing::SetupAndTeardown
  include Mocha::API

  def run
    result = nil
    capture_exceptions do
      result = super
    end
    result
  end

  def setup
    mocha_setup
  end

  def teardown
    mocha_teardown
  end

  private

  def capture_exceptions
    yield
  rescue Minitest::Assertion => e
    self.failures << e
  rescue Exception => e
    self.failures << Minitest::UnexpectedError.new(e)
  end
end
