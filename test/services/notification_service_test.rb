require "test_helper"

class NotificationServiceTest < ActiveSupport::TestCase
  setup do
    @company = companies(:acme_corp)
    @user = users(:john)
    @work = works(:scheduled_work)
    @contract = contracts(:john_acme)
  end

  test "create_for_work_assignment creates notification for added assignment" do
    assert_difference 'Notification.count', 1 do
      NotificationService.create_for_work_assignment(@work, @contract, :added)
    end
    
    notification = Notification.last
    assert_equal @user, notification.user
    assert_equal @company, notification.company
    assert_equal @work, notification.notifiable
    assert_equal 'work_assignment_added', notification.notification_type
    assert_includes notification.title, @work.title
    assert_includes notification.message, @work.location
  end

  test "create_for_work_assignment creates notification for removed assignment" do
    assert_difference 'Notification.count', 1 do
      NotificationService.create_for_work_assignment(@work, @contract, :removed)
    end
    
    notification = Notification.last
    assert_equal 'work_assignment_removed', notification.notification_type
  end

  test "create_for_work_status_change creates notification" do
    old_status = 'scheduled'
    new_status = 'in_progress'
    
    # Create assignment first
    assignment = WorkAssignment.create!(
      work: @work,
      contract: @contract,
      company: @company
    )
    
    assert_difference 'Notification.count', 1 do
      NotificationService.create_for_work_status_change(@work, old_status, new_status)
    end
    
    notification = Notification.last
    assert_equal 'work_status_changed', notification.notification_type
    assert_includes notification.message, old_status
    assert_includes notification.message, new_status
  end

  test "create_for_booking creates notification" do
    booking = bookings(:pending_booking)
    
    assert_difference 'Notification.count', 1 do
      NotificationService.create_for_booking(booking)
    end
    
    notification = Notification.last
    assert_equal 'booking_received', notification.notification_type
    assert_equal booking, notification.notifiable
    assert_includes notification.message, booking.client_name
  end

  test "create_for_invitation creates notification" do
    invitation = Invitation.create!(
      email: '<EMAIL>',
      invited_by: @user,
      company: @company,
      role: roles(:employee),
      contract: contracts(:jane_acme)
    )
    
    # Create user for the invitation
    invited_user = User.create!(
      email: '<EMAIL>',
      password: 'password123',
      password_confirmation: 'password123'
    )
    
    assert_difference 'Notification.count', 1 do
      NotificationService.create_for_invitation(invitation, invited_user)
    end
    
    notification = Notification.last
    assert_equal 'invitation_received', notification.notification_type
    assert_equal invitation, notification.notifiable
    assert_equal invited_user, notification.user
    assert_includes notification.message, @company.name
  end

  test "create_for_event creates notification" do
    event = events(:vacation_john)
    
    assert_difference 'Notification.count', 1 do
      NotificationService.create_for_event(event)
    end
    
    notification = Notification.last
    assert_equal 'event_pending', notification.notification_type
    assert_equal event, notification.notifiable
    assert_includes notification.message, I18n.t("activerecord.attributes.event.event_types.#{event.event_type}")
  end

  test "should send email for plus tier subscriptions" do
    # Set up Plus tier subscription
    subscription = Subscription.create!(
      company: @company,
      plan: plans(:plus),
      status: 'active',
      current_period_start: 1.month.ago,
      current_period_end: 1.month.from_now
    )
    
    # For now, just test that the method doesn't raise an error
    # Email functionality would be tested when WorkAssignmentMailer is implemented
    assert_nothing_raised do
      NotificationService.create_for_work_assignment(@work, @contract, :added)
    end
  end
end