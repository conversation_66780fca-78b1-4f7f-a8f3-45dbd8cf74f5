{"private": true, "type": "module", "devDependencies": {"@playwright/test": "^1.52.0", "@rollup/plugin-terser": "^0.4.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1", "@testing-library/vue": "^8.1.0", "@vitest/ui": "^2.1.9", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.21", "jsdom": "^24.1.3", "msw": "^2.8.4", "playwright": "^1.52.0", "tailwindcss": "^3.4.1", "vite": "^5.4.11", "vite-plugin-ruby": "^5.1.1", "vitest": "^2.1.9"}, "dependencies": {"@hotwired/turbo-rails": "^8.0.12", "@vitejs/plugin-vue": "^5.1.5", "@vuepic/vue-datepicker": "^10.0.0", "axios": "^1.7.9", "dayjs": "^1.11.13", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "lucide-vue-next": "^0.473.0", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vuex": "^4.0.2"}, "scripts": {"dev": "vite dev", "build": "vite build --emptyOutDir", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug"}}