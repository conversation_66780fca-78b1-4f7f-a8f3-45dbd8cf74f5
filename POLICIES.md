# Týmbox Authorization Policies



firstRole: state => state.role,
hasRole: state => role => state.roles.includes(role),
hasAnyRole: state => roleList => roleList.some(role => state.roles.includes(role)),
isOwner: state => state.roles.includes('owner'),
isAdmin: state => state.roles.includes('admin'),
isSupervisor: state => state.roles.includes('supervisor'),
isManager: state => ['owner', 'admin', 'supervisor'].some(r => state.roles.includes(r)),

Active permissions in v1 users_controller
permissions = 
            can_manage_contracts: allowed_to?(:manage_contract?, @company),
            can_view_owner_section: allowed_to?(:view_owner_section?, @company),
            can_receive_team_status_emails?: allowed_to?(:receive_team_status_emails?, @company),


# We use: bookings (feature), manager roles (scattered actions)
app/controllers/api/v1/subscription_controller.rb
if ['plus', 'premium'].include?(plan_name)
  available_features.push('booking', 'reservation', 'meeting')
end

## Company Policies
### manage?
User needs to be owner OR (has advanced role AND company has plus/premium plan)
User can edit, update, and view company details

### manage_subscription?
User needs to be owner OR (has advanced role AND company has plus/premium plan)
User can manage subscription plans and payments

### manage_contract? -----> permission: can_manage_contract
*Editing contracts: index, fetch, new, create, resend_invitation, update _role*
User needs to be owner OR (has advanced role AND company has plus/premium plan)
User can create, view, edit and update contracts

### view_bookings?
Any authenticated user with role in company
User can view booking lists and individual booking details

### manage_bookings?
Company needs plus/premium plan
Enables the booking feature for the company

### manage_booking_actions?
Company needs plus/premium plan AND user needs to be owner OR has advanced role
User can update, confirm, cancel bookings and convert them to works

### new? and create?
User needs to be owner OR (has advanced role AND company has plus/premium plan)
User can create additional companies

### team_summary?
User needs to be owner OR (has advanced role AND company has plus/premium plan)
User can access team summary reports

### manage_works?
User needs any role (owner, employee, supervisor, admin)
User can manage works through company

### receive_team_status_emails?
Receive daily email with team at work
If the company has a plus or premium plan
AND if the user is the owner OR (has advanced role AND the company has a plus or premium plan)
Special:
View: AuthMixin
Backend: TeamStatusReportService query scoped to companies with subscription.plan plus or premium


## Contract Policies

### index?
- User needs to be owner OR (has advanced role AND company has plus/premium plan)
- User can view the list of all contracts

### show?
- User is the contract owner OR company owner OR (has advanced role AND company has plus/premium plan)
- User can view individual contract details

### manage?
- User needs to be owner OR (has advanced role AND company has plus/premium plan)
- User can manage contract details and settings

### edit? and update?
- User needs to be owner OR (has advanced role AND company has plus/premium plan)
- User can edit and update contract details

### suspend?
- User needs to be owner OR (has advanced role AND company has plus/premium plan)
- User cannot suspend their own contract
- User can suspend other contracts

### terminate?
- User needs to be owner OR (has advanced role AND company has plus/premium plan)
- User cannot terminate their own contract
- User can terminate other contracts

### reactivate?
- User needs to be owner OR (has advanced role AND company has plus/premium plan)
- User cannot reactivate their own contract
- User can reactivate other contracts

### destroy?
- User needs to be owner OR (has advanced role AND company has plus/premium plan)
- User cannot delete their own contract
- User can delete other contracts


## Booking Policies

### view_bookings?
- Any authenticated user with role in company
- User can view the list of all bookings and individual booking details
- Actions: index, fetch, show

### manage_booking_actions?
- Company needs plus/premium plan
- User needs to be owner OR has advanced role (admin, supervisor)
- User can perform booking management actions
- Actions: update, confirm, cancel, convert_to_work


## Mainbox Policies

### show?
- User needs to be authenticated
- User can view their mainbox dashboard

### view_owner_section?
- User needs to be owner OR admin
- User can view the owner section of the dashboard

### view_employee_section?
- User needs any role in the company
- User can view the employee section of the dashboard

### manage?
- User needs to be owner OR admin
- User can manage the mainbox settings



## Work Policies

### access_works?
- User needs any role (owner, employee, supervisor, admin)
- User can access the works feature

### show?
- User needs any role (owner, employee, supervisor, admin)
- User can view individual work items

### manage_works?
- User needs any role (owner, employee, supervisor, admin)
- User can manage works

### create?
- User needs any role (owner, employee, supervisor, admin)
- User can create new works

### update?
- User needs any role (owner, employee, supervisor, admin)
- User can update existing works

### destroy?
- User needs any role (owner, employee, supervisor, admin)
- User can delete works