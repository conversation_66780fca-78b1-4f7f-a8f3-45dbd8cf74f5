require_relative "boot"

require "rails"
# Pick the frameworks you want:
require "active_model/railtie"
# require "active_job/railtie"
require "active_record/railtie"
require "active_storage/engine"
require "action_controller/railtie"
require "action_mailer/railtie"
# require "action_mailbox/engine"
# require "action_text/engine"
require "action_view/railtie"
# require "action_cable/engine"
require "rails/test_unit/railtie"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module Attendifyapp
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.0

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    config.time_zone = "CET"
    # config.eager_load_paths << Rails.root.join("extras")

    # Don't generate system test files.
    config.generators.system_tests = nil

    # config/initializers/locale.rb
    # Where the I18n library should search for translation files
    # Load locale files in nested dictionaries as well
    I18n.load_path += Dir[Rails.root.join("config", "locales", '**', "*.{rb,yml}")]

    # Permitted locales available for the application
    I18n.available_locales = [:en, :cs, :sk]

    # Set default locale to something other than :en
    I18n.default_locale = :cs

    # Default Rails way of disabling Active Storage routes
    # config.active_storage.draw_routes = false
    config.active_storage.variant_processor = :mini_magick


    config.hosts = nil
  end
end
