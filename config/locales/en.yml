# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t "hello"
#
# In views, this is aliased to just `t`:
#
#     <%= t("hello") %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   "true": "foo"
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  hello: "Hello world"
  action_policy:
    company_policy:
      new?: "You are not allowed to create new company."
      new: "You are not allowed to create new company."
  activerecord:
    errors:
      models:
        work:
          at_least_one_assignment: "Work must have at least one assignment"
        work_assignment:
          has_daily_activities: "You can't leave this work assignment because you have daily activities recorded for it"
          has_work_sessions: "You can't leave this work assignment because you have work sessions recorded for it"
  time:
    formats:
      long: "%B %d, %Y %H:%M:%S"
      short: "%d %b, %Y"