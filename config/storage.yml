test:
  service: S3
  access_key_id: <%= Rails.application.credentials.dig(:aws, :access_key_id) %>
  secret_access_key: <%= Rails.application.credentials.dig(:aws, :secret_access_key) %>
  region: eu-central-1 
  bucket: attendifyapp-test-logos 
  public: false
  force_path_style: true
  endpoint: <%= Rails.env.development? ? 'http://0.0.0.0:5100' : nil %>

local:
  service: S3
  access_key_id: <%= Rails.application.credentials.dig(:aws, :access_key_id) %>
  secret_access_key: <%= Rails.application.credentials.dig(:aws, :secret_access_key) %>
  region: eu-central-1 
  bucket: attendifyapp-dev-logos 
  public: false
  force_path_style: true
  endpoint: <%= Rails.env.development? ? 'http://0.0.0.0:5100' : nil %>

amazon_dev:
  service: S3
  access_key_id: <%= Rails.application.credentials.dig(:aws, :access_key_id) %>
  secret_access_key: <%= Rails.application.credentials.dig(:aws, :secret_access_key) %>
  region: eu-central-1
  bucket: attendifyapp-dev-logos
  public: false

amazon_prod:
  service: S3
  access_key_id: <%= Rails.application.credentials.dig(:aws, :access_key_id) %>
  secret_access_key: <%= Rails.application.credentials.dig(:aws, :secret_access_key) %>
  region: eu-central-1 
  bucket: attendifyapp-prod-logos 
  public: false
  force_path_style: true

amazon_staging:
  service: S3
  access_key_id: <%= Rails.application.credentials.dig(:aws, :access_key_id) %>
  secret_access_key: <%= Rails.application.credentials.dig(:aws, :secret_access_key) %>
  region: eu-central-1 
  bucket: attendifyapp-staging-logos 
  public: false
  force_path_style: true 
  # endpoint: <%= Rails.env.staging? ? 'https://effective-umbrella-q6ff.onrender.com' : nil %>