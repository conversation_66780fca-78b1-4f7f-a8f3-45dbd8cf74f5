require "active_support/core_ext/integer/time"

Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb.

  # In the development environment your application's code is reloaded any time
  # it changes. This slows down response time but is perfect for development
  # since you don't have to restart the web server when you make code changes.
  config.cache_classes = false

  # Do not eager load code on boot.
  config.eager_load = false

  # Show full error reports.
  config.consider_all_requests_local = true

  # Enable server timing
  config.server_timing = true

  # Enable/disable caching. By default caching is disabled.
  # Run rails dev:cache to toggle caching.
  if Rails.root.join("tmp/caching-dev.txt").exist?
    config.action_controller.perform_caching = true
    config.action_controller.enable_fragment_cache_logging = true

    config.cache_store = :memory_store
    config.public_file_server.headers = {
      "Cache-Control" => "public, max-age=#{2.days.to_i}"
    }
  else
    config.action_controller.perform_caching = false

    config.cache_store = :null_store
  end

  # Print deprecation notices to the Rails logger.
  config.active_support.deprecation = :log

  # Raise exceptions for disallowed deprecations.
  config.active_support.disallowed_deprecation = :raise

  # Tell Active Support which deprecation messages to disallow.
  config.active_support.disallowed_deprecation_warnings = []

  # Raise an error on page load if there are pending migrations.
  config.active_record.migration_error = :page_load

  # Highlight code that triggered database queries in logs.
  config.active_record.verbose_query_logs = true

  # Suppress logger output for asset requests.
  config.assets.quiet = true

  config.assets.debug = true
  config.assets.compile = true
  config.assets.check_precompiled_asset = false

  # Raises error for missing translations.
  # config.i18n.raise_on_missing_translations = true

  # Annotate rendered view with file names.
  # config.action_view.annotate_rendered_view_with_filenames = true

  # Uncomment if you wish to allow Action Cable access from any origin.
  # config.action_cable.disable_request_forgery_protection = true
  
  #Detects and warns you about N+1 queries and unused eager loading.
  config.after_initialize do
    Bullet.enable = true
    Bullet.alert = true # Popups in the browser
    Bullet.console = true # Log output in the console
    Bullet.rails_logger = true # Log output in Rails logs
    #Bullet.add_footer = true # Add warnings in the HTML response footer
    ## Optional: Whitelist specific N+1 queries if they're intentional
    # Bullet.add_whitelist(type: :n_plus_one_query, class_name: 'Model', association: :association_name)
  end

  config.action_mailer.default_url_options = { host: '0.0.0.0', port: 5100 }
  config.action_mailer.delivery_method = :test
  #config.action_mailer.delivery_method = :resend
  config.action_mailer.perform_deliveries = true
  config.action_mailer.raise_delivery_errors = true
  
  # Custom email log for development: 
  # config.action_mailer.logger = Logger.new(STDOUT)
  config.action_mailer.logger = nil
  config.action_mailer.observers = ['EmailLoggerObserver']

  config.action_mailer.preview_path = "#{Rails.root}/test/mailers/previews"

  # Active Storage Configuration
  config.active_storage.service = :amazon_dev
  config.active_storage.replace_on_assign_to_many = true
  config.active_storage.service_urls_expire_in = 1.hour
  config.active_storage.url_options = {
    host: '0.0.0.0',
    port: 5100,
    protocol: 'http'
}

end
