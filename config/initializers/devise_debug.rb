module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def self.check_mailer_methods
    puts "Devise Version: #{Devise::VERSION}"
    puts "Rails Version: #{Rails.version}"
    puts "Ruby Version: #{RUBY_VERSION}"
    
    puts "\nDevise::Mailer Methods:"
    Devise::Mailer.instance_methods(false).select do |m|
      m.to_s.include?('password')
    end.each do |method|
      puts method
    end
    
    puts "\nDevise::Mailer Singleton Methods:"
    Devise::Mailer.methods(false).select do |m|
      m.to_s.include?('password')
    end.each do |method|
      puts method
    end
  end
end
