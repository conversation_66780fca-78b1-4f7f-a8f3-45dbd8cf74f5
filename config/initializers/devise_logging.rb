Rails.application.config.after_initialize do
  Devise::PasswordsController.class_eval do
    around_action :debug_password_update_flow, only: [:update]

    private

    def debug_password_update_flow
      #Rails.logger.debug "Before Devise update: #{resource.inspect}"
      yield # Proceed with the normal flow
      #Rails.logger.debug "After Devise update: #{resource.errors.full_messages}" if resource&.errors&.any?
      #Rails.logger.debug "Incoming parameters: #{params.inspect}"
      #Rails.logger.debug "Permitted parameters: #{params.require(:user).permit(:password, :password_confirmation, :reset_password_token).inspect}"
      #Rails.logger.debug "Reset token valid? #{User.with_reset_password_token(params[:reset_password_token]).present?}"
      #Rails.logger.debug "Sanitized params: #{devise_parameter_sanitizer.sanitize(:password_update)}"
      #Rails.logger.debug "Submitted token: #{params[:reset_password_token]}"

    end
  end
end

