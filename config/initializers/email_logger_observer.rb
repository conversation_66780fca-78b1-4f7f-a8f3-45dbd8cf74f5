class EmailLoggerObserver
  def self.delivered_email(message)
    puts "--- Delivering email (STDOUT) ---"
    # Optional: Log headers for context
    puts "To: #{message.to.join(', ')}"
    puts "From: #{message.from.join(', ')}"
    puts "Subject: #{message.subject}"
    puts "--- Decoded Body ---"

    # Check if the email is multipart (HTML and Text)
    if message.multipart?
      if message.html_part
        puts "--- HTML Part (Decoded) ---"
        # Access the decoded body of the HTML part
        puts message.html_part.body.decoded
      end
      if message.text_part
        puts "--- Text Part (Decoded) ---"
        # Access the decoded body of the Text part
        puts message.text_part.body.decoded
      end
    else
      # If it's a single part email (e.g., only HTML or only Text)
      puts message.body.decoded # Decode the main body
    end

    puts "--- End email delivery (STDOUT) ---"
  end
end