# frozen_string_literal: true

Rails.application.config.active_storage.resolve_model_to_route = :rails_storage_proxy

# Set URL options specifically for Active Storage
Rails.application.config.to_prepare do
  host = if Rails.env.production?
           'app.tymbox.cz'
         elsif Rails.env.staging?
           'effective-umbrella-q6ff.onrender.com'
         else
           '0.0.0.0'
         end
  port = Rails.env.production? || Rails.env.staging? ? nil : 5100
  protocol = Rails.env.production? || Rails.env.staging? ? 'https' : 'http'
  
  # This is the correct configuration for Active Storage
  Rails.application.config.active_storage.url_options = {
    host: host,
    port: port,
    protocol: protocol
  }
end