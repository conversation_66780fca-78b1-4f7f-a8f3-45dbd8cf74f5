Rails.application.routes.draw do

  # Add a JSON-specific route outside the locale scope
  get 'companies.json', to: 'companies#index', defaults: { format: :json }
  put 'companies/:id', to: 'companies#update'
  get 'holidays.json', to: 'holidays#index', defaults: { format: :json }

  scope "/:locale", locale: /#{I18n.available_locales.join("|")}/ do
    
    # Devise routes (authentication)
    devise_for :users, controllers: {
      registrations: 'users/registrations'
    }
    
    # Custom logout route
    # devise_scope :user do
    #   get '/logout', to: 'devise/sessions#destroy'
    # end


    # Public routes that need localization but don't require authentication
    
    # Terms routes
    get 'terms/:type', to: 'terms#show'
    
    # TODO: Test devise properly, especiall this
    # User signup
    get 'users/sign_up', to: 'users/registrations#new'
    
    # Routes that require authentication
    authenticated :user do
      # Specific page routes that need localization and authentication
      get 'companies', to: 'companies#index'
      get 'companies/new', to: 'companies#new'
      post 'companies', to: 'companies#create'
      post 'companies/switch', to: 'companies#switch'
      get 'companies/:id/edit', to: 'companies#edit'
      put 'companies/:id', to: 'companies#update'
      patch 'companies/:id', to: 'companies#update'
      get 'works', to: 'works#index'
      get 'bookings', to: 'bookings#index'
      get 'meetings', to: 'meetings#index'
      get 'daily_logs/report', to: 'daily_logs#report'
      get 'daily_logs/owner_reports', to: 'daily_logs#owner_reports'
      get 'holidays', to: 'holidays#index'
      get 'company_connections', to: 'company_connections#index'
      get 'user_settings/edit', to: 'user_settings#edit'
      get 'contracts', to: 'contracts#index'
      get 'contracts/new', to: 'contracts#new'
      get 'events', to: 'events#index'
      
      # Root path for authenticated users
      root to: "mainbox#show", as: :authenticated_root_with_locale
    end
    
    devise_scope :user do
      # The root path for non-authenticated users needs to be distinct or handled differently
      # If you want it to also be locale-scoped, it should be inside this scope.
      # Otherwise, it might need to be outside or have a redirect.
      # For now, let's assume it should also be locale-scoped.
      root to: "devise/sessions#new", as: :unauthenticated_root_with_locale # Renamed to avoid conflict
    end

  end

  # Terms routes - accessible without locale prefix
  get 'terms/:type', to: 'terms#show'

  # Public booking routes
  get 'r/:company_slug/:slug', to: 'public_bookings#show', as: :public_booking
  post 'r/:company_slug/:slug/bookings', to: 'public_bookings#create'
  get 'r/:company_slug/:slug/calendar', to: 'public_bookings#calendar'
  
  # Booking management routes
  get 'r/:company_slug/manage/:token', to: 'public_bookings#manage', as: :manage_booking
  get 'r/:company_slug/bookings/:token', to: 'public_bookings#show_by_token', as: :show_booking_by_token
  patch 'r/:company_slug/bookings/:token', to: 'public_bookings#update_booking', as: :update_booking_by_token
  delete 'r/:company_slug/bookings/:token', to: 'public_bookings#cancel_booking', as: :cancel_booking_by_token
  
  # Private meetings routes
  get 'private_meetings/:token', to: 'public_meetings#show', as: :public_meeting
  patch 'private_meetings/:token', to: 'public_meetings#update'
  get 'private_meetings/:token/meeting', to: 'public_meetings#meeting'


# Need authentication, non locale scope

# All resource definitions with full CRUD operations
resources :companies, except: [:index, :new] do
  member do
    post :leave
    post :upload_logo
  end
end

resources :contracts, except: [:index, :new]  do
  member do
    post :suspend
    post :reactivate
    post :terminate
    post :update_role
    post :resend_invitation
  end
  collection do
    get :fetch
    get :colleagues
  end
end

resources :company_connections, except: [:index] do
  collection do
    get :fetch
  end
  member do
    post :accept
  end
end

resources :daily_logs, except: [:report] do
  collection do
    post :create_for_report
    get :fetch
    get :last
    get :fetch_report_data
    get :fetch_employee_report_data
    get :fetch_day_data
    get :current_status
    get :team_summary
  end
end

resources :events, except: [:index] do
  collection do
    get :fetch
  end
end

resources :daily_activities do
  collection do
    post :start_work_activity
    get :current_work_activity
  end
  member do
    post :end_work_activity
  end
end

resources :assignments, only: [:index] do
  post :switch, on: :collection
end

resource :company_settings, only: [:edit, :update]
resource :user_settings, only: [:update] do
  collection do
    get :index
  end
end

resources :breaks do
  collection do
    post :create_for_report
    get :today
  end
end

resources :works, except: [:index] do
  collection do
    get :fetch
    get :assigned
  end
  member do
    post :take_assignment
    delete :leave_assignment
  end
end

resources :work_sessions, only: [] do
  member do
    post :check_in
    post :check_out
  end
end

resources :booking_links

resources :bookings, except: [:index] do
  collection do
    get :fetch
  end
  member do
    post :confirm
    post :cancel
    post :convert_to_work
  end
end

resources :meetings, except: [:index, :edit] do
  collection do
    get :fetch
    get :conflicts
  end
  member do
    post :resend_invitation
    post :extend_meeting_dates
    get :get_best_options
    post :add_user_to_confirmed_date
    post :confirm
  end
end

resource :user_profile, only: [:show, :edit, :update]

resources :holidays, except: [:index] do
  collection do
    post :sync
  end
end

# API routes
namespace :api do
  namespace :v1 do
    resource :user, only: [:show]
    resources :employees, only: [:index]
    resources :events, only: [:index] do
      member do
        post :confirm
      end
    end
    resources :notifications, only: [:index, :destroy] do
      collection do
        get :unread_count
        get :for_mainbox
        put :mark_all_read
      end
      member do
        put :read
        put :process, action: :process_notification
      end
    end
    get 'subscription_status', to: 'subscription#status'
    get 'translations/:locale', to: 'translations#index'
  end
end

# Other utility routes
get 'reports/activities', to: 'reports#activities'
post 'reports/send_pdf', to: 'reports#send_pdf'
post 'newsletter/subscribe', to: 'newsletter#subscribe'
post 'cron/daily_team_status', to: 'cron#daily_team_status'

resources :subscriptions, only: [:create] do
  collection do
    post 'activate_trial'
    get 'request_subscription'
    post 'process_subscription_request'
  end
end

# Fallback for root path if no locale is provided, redirect to default locale
root to: redirect("/#{I18n.default_locale}")
end
