SitemapGenerator::Sitemap.default_host = "https://tymbox.cz"

SitemapGenerator::Sitemap.create do
  # Add static pages
  # add features_path
  # add pricing_path
  # add mobilni_dochazka_path
  # add online_dochazka_path
  # add blog_mobilni_dochazka_bez_instalace_path
  # add blog_online_dochazka_pro_male_firmy_path
  # add blog_online_dochazka_zdarma_path
  # add blog_ztracite_cas_administrativou_path
  # add blog_dochazka_bez_skoleni_path
  
  # Add dynamic content
  # # For public booking links (if they should be indexed)
  # BookingLink.find_each do |booking_link|
  #   add public_booking_path(company_slug: booking_link.company.slug, slug: booking_link.slug)
  # end
  
  # Add other important pages you want to be indexed
  # Examples:
  # add articles_path
  # Article.find_each do |article|
  #   add article_path(article)
  # end

  #bundle exec rake sitemap:refresh
  
end