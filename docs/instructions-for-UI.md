Component Structure & UI Guidelines

Fundamental Styling Approach
- Follow mobile-first approach with responsive classes
- First try to reuse existing classes for the styling to be consistent across the app
- Use predefined variables from `app/assets/stylesheets/application.scss` for consistent colors and styling attributes
- Try to reuse predefined central utility classes from application.scss or `app/frontend/css/main.css`
- Use Tailwind utility classes for styling components if not predefined central utility classes are available
- Avoid scoped CSS unless absolutely necessary
- Do not use hovers nor transitions unless necessary
- Do not comment at the end of lines
- Consider the central .content-spacing class (@apply mt-4 mb-4 md:mt-6 md:mb-6;) for consistency between stacked cards/sections.

3. Cards (.card)
Purpose: Standard container for discrete UI sections Styles defined in application.scss.
Structure:
.card-header: (Optional) Contains title (h2), actions/links (use .action-group). Uses flexbox (justify-content: space-between).
.card-content: Holds primary information, lists, or forms. Includes padding.
.card-footer: (Optional, not in Mainbox) For summary actions, pagination.
Card Pattern should follow this structure:
```html
<div class="card">
  <div class="card-header">
    <h2>Card Title</h2>
    <span>Badge Text</span>
  </div>
  <div class="card-content">
  </div>
  <div class="card-footer">
  </div>
</div>
```

4. Lists (e.g., Employees, Bookings)
Spacing: use .item-list for consistent vertical spacing between list items.
List Item (.item-list-item):
Use flex items-center gap-3 p-2 rounded-md;
Typically includes an icon/avatar (use .avatar-placeholder-sm), primary text (.text-body or font-medium), secondary text/status (.text-caption or .text-muted), and potentially actions.

5. Buttons
Standard Buttons (.btn): Use variants defined in application.scss (.btn-primary, .btn-secondary, .btn-outline, .btn-light, .btn-danger-light).
Icon Buttons (.btn-icon): For actions represented solely by an icon. Use variants like .btn-icon-subtle (for collapse toggles) or .btn-icon-primary.
Text Links (.text-link, .text-link-action): Use .text-link-action for clickable text elements that trigger actions (like "Zobrazit vše"). Use .text-link for standard navigation links.
Disabled State: The .btn:disabled style provides a default disabled appearance. Apply it automatically via the :disabled attribute on <button> elements.

6. Typography
Centralized Reusable Classes: Use the newly defined text classes for consistency:
.text-title-xl, .text-title-lg: Large titles.
.text-subtitle: Section subtitles or less prominent headings.
.text-body: Default text content.
.text-muted, .text-caption: Secondary or less important text, often smaller.
Consistency: Adhere to these classes instead of applying multiple Tailwind typography utilities directly in components where possible.

7. Forms (Placeholders)
Structure: Group related elements using .form-group.
Elements: Use .form-label, .form-input, .form-select, .form-checkbox for consistent styling.
Errors: Display validation messages using .form-error-text.

8. Responsiveness
Mobile First: Design components with mobile constraints in mind first where applicable.
Tailwind Prefixes: Use sm:, md:, lg:, xl: prefixes for adapting layout, visibility (hidden md:inline), padding, margins, etc.
SCSS Media Queries: Used for more complex structural changes (e.g., .sidebar, .grid-cols-*) within application.scss.

9. State & Colors
Variables: Use the SCSS variables ($primary, $success, $warning, $danger, $gray-*) consistently for backgrounds, text, borders to convey meaning.
Badges (.badge-*): Use for status indicators (e.g., Success, Pending, Error).
Status Indicator (.status-indicator): Small dot indicator (e.g., .status-indicator-active).
Feedback States: Ensure clear :active, :focus, and :disabled states for all interactive elements.