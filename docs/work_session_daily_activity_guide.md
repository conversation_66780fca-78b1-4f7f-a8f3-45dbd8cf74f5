# WorkSession and DailyActivity Implementation Guide

## Overview

AttendifyApp uses two complementary models for tracking user activities and work sessions:
- **DailyActivity**: General-purpose activity tracking (any type of activity)
- **WorkSession**: Specific work session tracking with location features

## Model Descriptions

### DailyActivity
**Purpose**: Records various daily activities including work-related activities, breaks, and general time tracking entries.

**Key Features**:
- Activity types: `travel_to_work`, `work_at_location`, `work_remote`, `break`, `regular`
- Can be linked to DailyLog, Work, and WorkAssignment
- Provides formatted duration display
- More flexible and general-purpose than WorkSession

**Database Fields**:
```ruby
# Time tracking
t.datetime :start_time
t.datetime :end_time
t.integer :duration

# Descriptive
t.string :description
t.string :activity_type
t.string :place
t.string :project

# Location
t.jsonb :location_coordinates

# Relationships
t.bigint :user_id
t.bigint :company_id
t.bigint :contract_id
t.bigint :daily_log_id
t.bigint :work_id
t.bigint :work_assignment_id
```

### WorkSession
**Purpose**: Tracks specific work sessions for assigned works/jobs with check-in/check-out capabilities and location tracking.

**Key Features**:
- Status tracking: `in_progress`, `completed`, `cancelled`
- GPS-based check-in/check-out
- Location proximity validation (warns if >500m from work location)
- Automatically linked to DailyActivity for work activities

**Database Fields**:
```ruby
# Time tracking
t.datetime :start_time
t.datetime :end_time
t.integer :duration
t.string :status

# Location tracking
t.decimal :latitude
t.decimal :longitude
t.string :ip_address
t.string :check_in_location

# Check-in/out
t.datetime :check_in_time
t.datetime :check_out_time
t.text :check_in_notes

# Relationships
t.bigint :work_id
t.bigint :user_id
t.bigint :company_id
t.bigint :daily_activity_id
t.bigint :work_assignment_id
```

## Current Implementation Status

### ✅ Fully Implemented
- **DailyActivity**:
  - Full CRUD operations via DailyActivitiesController
  - API endpoints for work activity management
  - Frontend integration in TimeTracking.vue
  - Work activity modal for selecting and starting work
  - Activity list display

- **WorkSession**:
  - Model and database structure
  - Controller with check_in/check_out endpoints
  - Automatic creation when starting work activities

### ⚠️ Partially Implemented
- **WorkSession**:
  - Check-in/check-out endpoints exist but aren't exposed in UI
  - No frontend components for WorkSession management
  - Location tracking features not utilized in current UI

### ❌ Not Implemented
- No tests for either model
- WorkSession check-in/check-out UI
- Location-based features in frontend
- WorkSession detail views

## How They Work Together

### Work Activity Flow
1. User starts their workday (creates DailyLog)
2. User clicks "Start Work" button in TimeTracking component
3. Selects a Work assignment and activity type
4. System creates:
   - DailyActivity record (type: work_at_location/work_remote)
   - Associated WorkSession record (status: in_progress)
5. User can optionally check-in at work location (WorkSession feature - not in UI)
6. User ends work activity:
   - Updates DailyActivity end_time
   - Updates WorkSession to completed status

### Controller Endpoints

**DailyActivitiesController**:
- `GET /daily_activities` - List activities (filtered by date)
- `POST /daily_activities` - Create regular activity
- `POST /daily_activities/start_work_activity` - Start work with Work assignment
- `POST /daily_activities/:id/end_work_activity` - End work activity
- `GET /daily_activities/current_work_activity` - Get current active work

**WorkSessionsController**:
- `POST /work_sessions/:id/check_in` - Location-based check-in
- `POST /work_sessions/:id/check_out` - Location-based check-out

## Current Issues & Solutions

### Issue 1: DailyActivity Not Showing in TimeTracking
**Problem**: Despite implementation, activities might not display properly.

**Potential Causes**:
1. The component only shows activities when `showLogs` is toggled
2. Activities are fetched but might be empty if no current DailyLog exists
3. The conditional rendering depends on `isWorking` state

**Solution**: The activities section is hidden by default. Users need to click "Dnešní aktivity" to expand and see activities.

### Issue 2: WorkSession Not Used in UI
**Problem**: WorkSession has endpoints but no UI integration.

**Current State**: WorkSession is created automatically when starting work activities but check-in/check-out features are not exposed.

**Recommendation**: Consider adding check-in/check-out buttons when a work activity is active, especially for on-site work (work_at_location).

## Work Assignment Integration

When users have work assignments:
1. TimeTracking shows "Start Work" button when `hasAssignedWorks` is true
2. Modal displays assigned works filtered by current user's contract
3. Starting work creates both DailyActivity and WorkSession linked to:
   - The selected Work
   - The user's WorkAssignment
   - Current DailyLog

This allows tracking:
- Where users are working (Work location)
- What type of activity (travel/on-site/remote)
- Duration and timing
- Future: GPS check-in verification

## Recommendations

1. **Expose WorkSession Features**:
   - Add check-in/check-out buttons for on-site work
   - Show location verification status
   - Display check-in notes if user is far from location

2. **Improve Activity Display**:
   - Auto-expand activities when user is working
   - Show current work assignment status prominently
   - Add visual indicators for work vs regular activities

3. **Add Testing**:
   - Model tests for both DailyActivity and WorkSession
   - Controller tests for all endpoints
   - Frontend component tests for work activity flows

4. **Enhanced Features**:
   - Real-time location tracking for on-site work
   - Work session history and reports
   - Integration with invoicing/billing based on tracked time