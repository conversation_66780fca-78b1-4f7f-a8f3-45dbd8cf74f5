# Test Fixes Plan

## Current Test Status
- Total Tests: 52
- Passing: 28 (54%)
- Failing: 24 (46%)

## Issues to Fix

### 1. Missing Translations
**Status:** Partially Fixed ✅

Added translations for:
- Common actions (cancel, save, submit, etc.)
- Common fields (first_name, last_name, email, phone, job_title)
- Contract-specific translations
- Daily log translations
- Navigation translations

**Still Missing:**
- Modal-specific translations
- Flash message types
- Additional form field labels

### 2. Component Props Issues
**Status:** Partially Fixed ✅

Fixed:
- DailyLogForm: Added required `buttonLabel` prop
- ContractForm: Changed from `contract` to `contractId` prop

**Still to Fix:**
- Form validation expectations
- Component initialization issues
- Async data fetching in tests

### 3. Test Implementation Issues

#### ContractForm Tests
- **validates required fields**: Form submission prevention not working correctly
- **pre-fills form**: Need to wait for async data fetch

#### FlashMessages Tests
- Need to properly mock the global `window.showFlashMessage` function
- Test component reactivity to new messages

#### HeaderNav Tests
- Plugin duplication warnings (Vue store/i18n being applied multiple times)
- Need better isolation between tests

#### CentralModal Tests
- Modal visibility tests need proper Vue reactivity handling
- Focus trap testing needs DOM setup

#### DailyLogForm Tests
- Time input validation needs proper format checking
- API endpoint mocking needs adjustment

### 4. Mocking Issues

#### API Mocking
- Need consistent axios mock setup
- Handle both success and error cases properly
- Mock response data structure matching actual API

#### Vue Router Mocking
- Add proper route mocking for navigation tests
- Handle active route detection

#### Global Functions
- Mock `window.showFlashMessage`
- Mock `window.Turbo`
- Mock console methods consistently

## Fix Priority

### High Priority (Fix First)
1. **API mocking setup** - Create reusable mock setup
2. **Missing translations** - Complete the translation dictionary
3. **Component prop validation** - Ensure all required props are provided

### Medium Priority
1. **Async test handling** - Proper waitFor usage
2. **Vue plugin warnings** - Fix duplicate plugin registration
3. **Form validation tests** - Mock form validation properly

### Low Priority
1. **Focus management tests** - Complex DOM interaction
2. **Animation/transition tests** - May need to disable in tests
3. **Performance optimizations** - Test speed improvements

## Implementation Steps

### Step 1: Create Mock Helpers
```typescript
// test/frontend/utils/api-mocks.ts
export const setupAxiosMocks = () => {
  // Default success/error responses
  // Reusable mock patterns
}
```

### Step 2: Enhance Translation Setup
```typescript
// Add comprehensive translations to test-utils.ts
// Include all component-specific keys
```

### Step 3: Fix Individual Tests
1. Start with simplest components (FlashMessages, CentralModal)
2. Move to form components (ContractForm, DailyLogForm)
3. Fix navigation components (HeaderNav)
4. Address complex interactions last

### Step 4: Add Test Documentation
- Document common test patterns
- Create test templates for new components
- Add troubleshooting guide

## Expected Outcome
- All 52 tests passing
- No console warnings or errors
- Consistent test patterns established
- Easy to add new tests following patterns