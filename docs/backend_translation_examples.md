# Backend Translation Examples

This document provides practical examples of using the organized backend translation structure in controllers and models.

## Controller Examples

### BookingsController

```ruby
class BookingsController < ApplicationController
  
  def update
    if @booking.update(booking_params)
      render json: { 
        success: true,
        booking: @booking,
        message: booking_t('messages.updated') 
      }
    else
      render json: { 
        success: false, 
        errors: @booking.errors.full_messages,
        message: booking_t('errors.update_failed')
      }
    end
  end

  def confirm
    if @booking.confirm(date, time)
      render json: {
        success: true, 
        booking: @booking,
        message: booking_t('messages.confirmed') 
      }
    else
      render json: { 
        success: false, 
        errors: @booking.errors.full_messages,
        message: booking_t('errors.confirm_failed')
      }
    end
  end

  private

  # Helper method for controller-specific translations
  def booking_t(key, **options)
    t("controllers.bookings.#{key}", **options)
  end
end
```

## Model Examples

### User Model with Custom Validations

```ruby
class User < ApplicationRecord
  validate :custom_email_validation
  validate :password_strength

  private

  def custom_email_validation
    unless email =~ URI::MailTo::EMAIL_REGEXP
      errors.add(:email, model_t('errors.invalid_email_format'))
    end
    
    if User.where(email: email).where.not(id: id).exists?
      errors.add(:email, model_t('errors.email_already_taken'))
    end
  end

  def password_strength
    if password.present? && password.length < 6
      errors.add(:password, model_t('errors.password_too_short'))
    end
  end

  def activate_account!
    update(activated: true)
    # Could log or return the success message
    model_t('messages.account_activated')
  end
end
```

### Booking Model with Business Logic

```ruby
class Booking < ApplicationRecord
  validate :date_validation
  validate :time_slot_availability

  def send_reminder
    if BookingMailer.reminder_email(self).deliver_now
      model_t('messages.reminder_sent')
    else
      model_t('errors.reminder_failed')
    end
  end

  private

  def date_validation
    if preferred_date.present?
      if preferred_date < Date.current
        errors.add(:preferred_date, model_t('errors.past_date'))
      end
      
      unless preferred_date.is_a?(Date)
        errors.add(:preferred_date, model_t('errors.invalid_date'))
      end
    end
  end

  def time_slot_availability
    if specific_time.present? && preferred_date.present?
      existing_booking = Booking.where(
        preferred_date: preferred_date,
        specific_time: specific_time
      ).where.not(id: id).first
      
      if existing_booking
        errors.add(:specific_time, model_t('errors.time_slot_taken'))
      end
    end
  end
end
```

## Service Object Examples

### BookingService

```ruby
class BookingService
  def initialize(booking)
    @booking = booking
  end

  def confirm_with_notification
    if @booking.confirm
      BookingMailer.booking_confirmed_email(@booking).deliver_now
      {
        success: true,
        message: I18n.t('controllers.bookings.messages.confirmed')
      }
    else
      {
        success: false,
        message: I18n.t('controllers.bookings.errors.confirm_failed'),
        errors: @booking.errors.full_messages
      }
    end
  end

  def convert_to_work_with_validation
    if @booking.status == 'confirmed'
      work = @booking.convert_to_work
      if work
        {
          success: true,
          work: work,
          message: I18n.t('controllers.bookings.messages.converted_to_work')
        }
      else
        {
          success: false,
          message: I18n.t('controllers.bookings.errors.convert_failed')
        }
      end
    else
      {
        success: false,
        message: @booking.model_t('errors.cannot_convert_unconfirmed')
      }
    end
  end
end
```

## API Response Examples

### Successful Response
```json
{
  "success": true,
  "booking": { ... },
  "message": "Rezervace byla potvrzena."
}
```

### Error Response
```json
{
  "success": false,
  "errors": ["E-mail má neplatný formát", "Heslo musí mít alespoň 6 znaků"],
  "message": "Nepodařilo se aktualizovat rezervaci."
}
```

## Common/Shared Translation Usage

```ruby
# In controllers or services
role_name = t('roles.names.admin')
status_label = t('statuses.confirmed')
event_type = t('event_types.vacation')

# In views (to be moved to Vue.js eventually)
user_role = t("roles.names.#{current_user.role}")
booking_status = t("statuses.#{booking.status}")
```

## Best Practices

1. **Use helper methods** in controllers and ApplicationRecord for cleaner code
2. **Group translations by purpose**: messages (success), errors (failures), confirmations (prompts)
3. **Keep keys descriptive but concise**: `invalid_email_format` not `email_format_is_invalid_error`
4. **Use shared translations** for common values like roles, statuses, event types
5. **Return structured responses** from services with success/error messages
6. **Separate concerns**: Controllers handle API responses, Models handle validations 