# Backend Translation Structure Guide

## Overview

This guide explains the organized translation structure for **backend translations** in the Attendify application, which is transitioning to a Vue.js SPA architecture.

## Architecture Context

- **Frontend (Vue.js SPA)**: Will handle all view-related translations
- **Backend (Rails API)**: Handles controller responses, model validations, and system messages
- **Shared/Common**: Application-wide constants like roles, statuses, event types

## File Organization

### 1. Backend Structure
```yaml
# Controller-specific translations (API responses)
controllers:
  controller_name:
    messages:      # Success messages (operations completed successfully)
    errors:        # Error messages (operations failed)
    confirmations: # Server-side confirmation prompts

# Model-specific translations (validations, business logic)
models:
  model_name:
    errors:        # Custom validation error messages
    messages:      # Model-specific success messages
    statuses:      # Model-specific status translations

# Application-wide shared translations (remain at root level)
roles:             # User roles across the application
statuses:          # Global status values
event_types:       # Event type definitions
action_policy:     # Authorization messages
activerecord:      # Rails model translations
```

### 2. Frontend Structure (handled by Vue.js)
- All view labels, form texts, UI messages
- User-facing interface translations
- Navigation, buttons, tooltips, etc.

### 3. Legacy Sections (to be migrated/removed)
- `front:` - Will be moved to Vue.js frontend
- `messages:` - Will be moved to appropriate controllers/models sections

## Translation Key Naming Convention

- **Use action-based names**: `updated`, `created`, `deleted`, `confirmed`
- **Keep keys short but descriptive**: `converted_to_work` not `booking_converted_to_work_successfully`
- **Use underscores for multi-word keys**: `update_failed`, `convert_to_work`
- **Group by purpose**: `messages` (success), `errors` (failures), `confirmations` (prompts)

## Translation Texts
- **Special words**: `meeting` is translated into Slovak as `stretnutie` and Czech as `schůzka` in respective forms

## Backend Implementation

### Controller Helper Method
Add a private helper method to each controller for cleaner translation calls:

```ruby
private

def controller_name_t(key, **options)
  t("controllers.controller_name.#{key}", **options)
end
```

### Model Helper Method
Add to ApplicationRecord or specific models:

```ruby
def self.model_t(key, **options)
  I18n.t("models.#{model_name.underscore}.#{key}", **options)
end
```

### Usage Examples
```ruby
# Controller success message (API response)
message: booking_t('messages.updated')

# Controller error message
message: booking_t('errors.update_failed')

# Model validation error
errors.add(:base, User.model_t('errors.invalid_role'))

# Shared/common translations (root level)
status_name: t('statuses.confirmed')
role_name: t('roles.names.admin')
```

## Migration Strategy

1. **New backend translations**: Use `controllers.{name}` or `models.{name}` structure
2. **Common/shared data**: Keep at root level (roles, statuses, event_types, etc.)
3. **Frontend migrations**: Will be handled separately in Vue.js
4. **Legacy cleanup**: Gradually remove `front:` section as frontend takes over
5. **Temporary keep frontend**: Do not remove nor modify `front:` translations. If neccesary, create a copy of them in new file or location.

## Benefits

- **Clear separation**: Backend API vs Frontend UI translations
- **Organized**: Logical grouping by purpose (messages, errors, confirmations)
- **Scalable**: Easy to add new controllers/models following the same pattern
- **API-focused**: Optimized for JSON API responses to SPA frontend
- **Maintainable**: Related translations grouped together by component

## Examples

### BookingsController (Backend API)
See `app/controllers/bookings_controller.rb` and `controllers.bookings` section in `config/locales/cs.yml`

### User Model (Backend Validations)
```yaml
models:
  user:
    errors:
      invalid_email: "E-mail má neplatný formát"
      password_too_short: "Heslo je příliš krátké"
    messages:
      account_activated: "Účet byl aktivován"
```

### Shared/Common (Application-wide)
```yaml
roles:
  names:
    admin: "Administrátor"
    employee: "Zaměstnanec"

statuses:
  confirmed: "Potvrzeno"
  pending: "Čeká na vyřízení"
``` 