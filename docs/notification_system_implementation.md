# Notification System Implementation Guide

## Overview
The notification system follows the existing pattern in AttendifyApp where pending items are displayed in the Mainbox view until users process them. This guide outlines the implementation of notifications for work assignments and future notification needs.

## Current Notification Pattern
- **Mainbox View**: Central notification hub showing pending items
- **Pending Items**: Displayed until user takes action (confirm, cancel, etc.)
- **Visual Design**: Yellow-bordered cards for attention
- **Real-time Updates**: Items disappear after processing

## Notification System Architecture

### 1. Database Schema
```ruby
# Notifications table
create_table :notifications do |t|
  t.references :user, null: false, foreign_key: true
  t.references :company, null: false, foreign_key: true
  t.string :notifiable_type, null: false
  t.bigint :notifiable_id, null: false
  t.string :notification_type, null: false
  t.string :title
  t.text :message
  t.json :data
  t.datetime :read_at
  t.datetime :processed_at
  t.timestamps
end

add_index :notifications, [:notifiable_type, :notifiable_id]
add_index :notifications, [:user_id, :read_at]
add_index :notifications, [:company_id, :created_at]
```

### 2. Notification Types
- `work_assignment_added` - When user is assigned to a work
- `work_assignment_removed` - When user is removed from a work
- `work_status_changed` - When work status changes
- `work_check_in_reminder` - Reminder to check in at work location
- `booking_received` - New booking received (existing)
- `invitation_received` - New invitation (existing)
- `event_pending` - Event needs confirmation (existing)

### 3. Implementation Components

#### Backend
1. **Notification Model**
   - Polymorphic association to notifiable items
   - Scopes for pending, unread, processed
   - Auto-cleanup of old notifications

2. **NotificationService**
   - Create notifications
   - Mark as read/processed
   - Bulk operations

3. **Controllers**
   - NotificationsController for API endpoints
   - Integration with existing controllers

#### Frontend
1. **Mainbox Integration**
   - New section for work assignments
   - Consistent UI with existing pending items
   - Action buttons for each notification type

2. **Notification Store (Vuex)**
   - Centralized notification state
   - Real-time updates
   - Notification counts

3. **Components**
   - NotificationItem.vue - Reusable notification display
   - WorkAssignmentNotification.vue - Specific for work assignments
   - NotificationBadge.vue - For header/sidebar counts

### 4. Email Notifications (Plus Tier)
```ruby
# WorkAssignmentMailer
class WorkAssignmentMailer < ApplicationMailer
  def assignment_notification(work_assignment)
    # Only for Plus tier subscriptions
    return unless work_assignment.company.subscription.plus_or_higher?
    
    @work_assignment = work_assignment
    @user = work_assignment.contract.user
    @work = work_assignment.work
    
    mail(
      to: @user.email,
      subject: t('mailer.work_assignment.subject', work_title: @work.title)
    )
  end
end
```

## Implementation Plan

### Phase 1: Core Notification System
1. Create notifications table migration
2. Implement Notification model
3. Create NotificationService
4. Add NotificationsController
5. Update existing controllers to create notifications

### Phase 2: Frontend Integration
1. Add notifications to Mainbox view
2. Create notification components
3. Implement Vuex store for notifications
4. Add notification badges

### Phase 3: Work Assignment Notifications
1. Create notifications on assignment changes
2. Add work assignment notification UI
3. Implement action handlers
4. Add real-time updates

### Phase 4: Email Notifications (Plus Tier)
1. Create WorkAssignmentMailer
2. Add email templates
3. Implement tier checking
4. Add notification preferences

### Phase 5: Advanced Features
1. Push notifications (future)
2. SMS notifications (future)
3. Notification scheduling
4. Batch notifications

## API Endpoints

### Notifications Management
- `GET /api/v1/notifications` - List user notifications
- `GET /api/v1/notifications/unread_count` - Get unread count
- `PUT /api/v1/notifications/:id/read` - Mark as read
- `PUT /api/v1/notifications/:id/process` - Mark as processed
- `PUT /api/v1/notifications/mark_all_read` - Bulk read
- `DELETE /api/v1/notifications/:id` - Delete notification

## UI/UX Guidelines

### Notification Display
- Follow existing yellow-bordered card pattern
- Show notification type icon
- Include timestamp
- Action buttons based on type
- Auto-remove after processing

### Notification States
- **Unread**: Bold text, blue dot indicator
- **Read**: Normal text, no indicator
- **Processed**: Removed from view

### Action Patterns
- Work Assignment: "View Work" / "Dismiss"
- Status Change: "View Work" / "Mark as Read"
- Reminder: "Check In" / "Snooze" / "Dismiss"

## Integration Points

### WorksController
```ruby
# After assignment creation
NotificationService.create_work_assignment_notification(work_assignment, 'added')

# After assignment removal
NotificationService.create_work_assignment_notification(work_assignment, 'removed')
```

### Mainbox View
```javascript
// Add to computed properties
pendingWorkAssignments() {
  return this.notifications.filter(n => 
    n.notification_type === 'work_assignment_added' && !n.processed_at
  );
}

hasPendingWorkAssignments() {
  return this.pendingWorkAssignments.length > 0;
}
```

## Security Considerations
- Users can only see their own notifications
- Company isolation enforced
- Notification data sanitized
- Rate limiting on notification creation

## Performance Optimizations
- Index on user_id and read_at for fast queries
- Pagination for notification lists
- Background job for email sending
- Auto-cleanup of old notifications (30 days)

## Testing Strategy
- Unit tests for Notification model
- Service tests for NotificationService
- Controller tests for API endpoints
- Frontend component tests
- Integration tests for full flow