# Týmbox Frontend Component Design Guide
This guide documents the coding standards and conventions for Týmbox Vue components.

## Component Structure

### File Organization
- Place components in `app/frontend/components/`.
- Organize by feature when appropriate.
- Group related components in subdirectories.
- The app is not an SPA, it only has scattered components
- Top level Parent components are registered in the `app/frontend/entrypoints/application.js`

### Components Rules
- Use the same structure as the existing components
- Use Options API
- Use Tailwind but have custom predefined classes
- CRITICAL: for styling follow the instructions in `instructions-for-UI.md`
- Import Order: External Libs -> Utils -> Mixins -> Components -> Icons (alphabetical within groups)
- When needed we use:
import axios from 'axios'; -- for HTTP requests
import { mapGetters } from 'vuex'; -- for state management
import { dateFormatter } from '../utils/dateFormatter'; -- for date formatting
import authorizationMixin from '../mixins/authorizationMixin'; -- for authorization checks
import { Icon1, Icon2 } from 'lucide-vue-next'; -- for icons
export default {
  mixins: [authorizationMixin], // If needed
  },

## State Management (Vuex)
- Use namespaced modules for store organization.
- Follow the pattern in `userStore.js` for new store modules.
- Export store modules as `default`.

### Authorization & Subscription
- **Requirement:** Always use `authorizationMixin` for components requiring authorization or subscription checks.
- **Available via Mixin:**
    - Role Checks: `isOwner`, `isAdmin`, `isSupervisor`, `isManager`, `hasRole(role)`, `hasAnyRole(roles)`, `can(permission)`
    - Subscription Checks: `hasPlus`, `hasPremium`, `currentPlan`
- **Requirement:** Always conditionally render plus or premium features based on subscription status (using mixin properties).

## Core Utilities

### HTTP Requests (Axios)
- Use Axios for all HTTP requests.
- Include error handling with `try/catch` blocks.
- Log errors to console for debugging.
- Note: Endpoints may be at various paths (not strictly under `/api/`).
```js
async fetchData() {
  try {
    const response = await axios.get('/endpoint');
    this.data = response.data;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
}
```

### Date Formatting
- Use the central `dateFormatter` utility (`../utils/dateFormatter`).
- Import and use the appropriate method based on the required format.
- If required format is missing, update the `dateFormatter.js` utility.
```js
import { dateFormatter } from '../utils/dateFormatter';

// Usage example (in computed or methods)
formattedDate() {
  return dateFormatter.longDateCz(this.date);
}
```

### Flash Messages
- Primary Handling: Backend controllers.
- Frontend Handling: Use `sendFlashMessage` utility (`../utils/flashMessage`) **only** when necessary for component-specific logic not handled by the backend.
```js
import sendFlashMessage from '../utils/flashMessage';

// Example: Component-specific feedback
handleComponentError() {
  sendFlashMessage('Please check your input and try again', 'warning');
}
```

### Modal Dialogs
- Use the central modal system.
- Trigger modals via the `open-central-modal` custom event.
```js
openModal() {
  const eventDetail = {
    detail: {
      componentName: 'ModalComponent', // Target modal component name
      title: 'Modal Title',
      props: { // Props to pass to the modal component
        initialData: this.data 
      } 
    }
  }
  document.dispatchEvent(new CustomEvent('open-central-modal', eventDetail));
}
```

## Accessibility (A11y)

- **HTML:** Use semantic HTML elements (`<button>`, `<h1>`-`<h6>`, etc.).
- **ARIA:** Add `aria-label` to elements without visible text; use `aria-expanded` for collapsible sections.
- **Focus:** Ensure interactive elements are focusable; maintain logical tab order.

## Documentation and Comments

- **Component Documentation:** Include a brief description (`/* ... */`) at the top of complex components. Document props clearly.
- **Comments:**
    - Explain **WHY** code exists, not **WHAT** it does.
    - Avoid comments at the end of lines.
    - Use multi-line comments (`/* ... */`) for complex logic blocks.
```js
// Example: Explain rationale for a specific approach
// This approach is used because the API returns inconsistent data formats
if (condition) {
  // Complex logic block
}
```

