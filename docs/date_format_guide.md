# Date Formatting Guide

## Overview
This guide documents the date formatting implementation in AttendifyApp, including the migration from hard-coded locales to dynamic i18n-based locale handling.

## Current Implementation

### DateFormatter Utility (`app/frontend/utils/dateFormatter.js`)

The `DateFormatter` class provides centralized date formatting functionality:

```javascript
export class DateFormatter {
  // Core formatting method
  format(date = new Date(), locale = 'cs-CZ', options = { weekday: 'long', day: 'numeric', month: 'long' })

  // Generic methods that accept locale
  shortDate(date = new Date(), locale)
  longDate(date = new Date(), locale)

  // Legacy methods for backward compatibility (deprecated)
  shortDateCz(date = new Date())
  longDateCz(date = new Date())
  shortDateEn(date = new Date())
  longDateEn(date = new Date())

  // Helper to convert i18n locale to browser locale format
  getLocaleString(locale)
}

// Exported helper function
export const getLocaleString = (locale) => dateFormatter.getLocaleString(locale);
```

### Locale Mapping
The utility maps i18n locales to browser-compatible locale strings:
- `'cs'` → `'cs-CZ'` (Czech)
- `'sk'` → `'sk-SK'` (Slovak)
- `'en'` → `'en-US'` (English)

## Usage Pattern

### Recommended Pattern for Vue Components

```javascript
import { getLocaleString } from '../../utils/dateFormatter';

export default {
  methods: {
    formatDate(dateString) {
      const date = new Date(dateString);
      const locale = getLocaleString(this.$i18n.locale);
      return date.toLocaleDateString(locale, {
        weekday: 'long',
        day: 'numeric',
        month: 'long'
      });
    },
    
    formatTime(dateString) {
      const date = new Date(dateString);
      const locale = getLocaleString(this.$i18n.locale);
      return date.toLocaleTimeString(locale, {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    }
  }
}
```

### Using DateFormatter Instance

```javascript
import { dateFormatter } from '../../utils/dateFormatter';

export default {
  computed: {
    formattedDate() {
      const locale = dateFormatter.getLocaleString(this.$i18n.locale);
      return dateFormatter.longDate(new Date(), locale);
    }
  }
}
```

## Migration Status

### ✅ Completed Components
1. **Mainbox.vue**
   - Changed `formattedDate` from hard-coded data to computed property
   - Now uses dynamic locale from `$i18n.locale`

2. **EventShow.vue**
   - Updated `formatDate()` method
   - Updated `formatTime()` method

3. **ActivityDashboard.vue**
   - Updated `formattedPeriod` computed property
   - Updated `formatDate()` method
   - Updated `formatTime()` method

4. **OwnerMainbox.vue**
   - Updated `formatDateTime()` method
   - Updated `formatDate()` method
   - Updated `formatTime()` method

5. **CurrentDate.vue**
   - Updated `formatDate()` method
   - Changed data property to store Date object instead of string

### ❌ Components Still Using Hard-coded Locales

The following components still contain hard-coded locale strings ('cs-CZ', 'sk-SK', or 'en-US'):

1. **TimeTracking.vue**
2. **WorkShow.vue**
3. **EventForm.vue**
4. **ShowBooking.vue**
5. **MonthlyReport.vue**
6. **CompanyConnections.vue**
7. **TeamSummary.vue**
8. **ContractsList.vue**
9. **BookingLinkDetail.vue**
10. **MonthlyEventTable.vue**
11. **AddEventForm.vue**
12. **MeetingCard.vue**
13. **DailyLogsIndex.vue**
14. **MonthlyCalendar.vue**

## Migration Steps

To migrate a component from hard-coded locale to dynamic locale:

1. **Import the helper function:**
   ```javascript
   import { getLocaleString } from '../../utils/dateFormatter';
   ```

2. **Replace hard-coded locale strings:**
   ```javascript
   // Before:
   date.toLocaleDateString('cs-CZ', options);
   
   // After:
   const locale = getLocaleString(this.$i18n.locale);
   date.toLocaleDateString(locale, options);
   ```

3. **Test the component** with different locales by changing the language in the UI.

## Common Date Formatting Options

### Date Options
```javascript
// Short date (e.g., "15.3.")
{ day: 'numeric', month: 'numeric' }

// Long date (e.g., "středa 15. března 2024")
{ weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' }

// Medium date (e.g., "15. března")
{ day: 'numeric', month: 'long' }

// Date with time (e.g., "15.03.2024 14:30")
{ day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit' }
```

### Time Options
```javascript
// 24-hour format (e.g., "14:30")
{ hour: '2-digit', minute: '2-digit', hour12: false }

// With seconds (e.g., "14:30:45")
{ hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false }
```

## Best Practices

1. **Always use dynamic locale** - Never hard-code locale strings
2. **Import helper functions** - Use `getLocaleString` for locale conversion
3. **Consistent formatting** - Use similar date/time formats across the application
4. **Computed properties** - For reactive date formatting, use computed properties
5. **Handle edge cases** - Check for null/undefined dates before formatting

## Future Improvements

1. **Complete migration** - Update all remaining components to use dynamic locale
2. **Centralize options** - Consider creating predefined formatting options in dateFormatter
3. **Vue composable** - Create a composable for date formatting in Vue 3 migration
4. **Testing** - Add unit tests for date formatting with different locales