class CreateWorkAssignments < ActiveRecord::Migration[7.0]
  def change
    create_table :work_assignments do |t|
      t.references :work, null: false, foreign_key: true
      t.references :contract, null: false, foreign_key: true
      t.references :company, null: false, foreign_key: true
      t.string :role, default: "worker"
      t.boolean :is_lead, default: false
      
      t.timestamps
    end
    
    add_index :work_assignments, [:work_id, :contract_id], unique: true
    add_index :work_assignments, [:company_id, :contract_id]
  end
end
