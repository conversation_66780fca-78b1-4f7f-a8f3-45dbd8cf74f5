class CreateInvitations < ActiveRecord::Migration[7.0]
  def change
    create_table :invitations do |t|
      t.references :sender, null: false, foreign_key: { to_table: :users, on_delete: :nullify }
      t.references :recipient, foreign_key: { to_table: :users, on_delete: :nullify }
      t.references :company, null: false, foreign_key: { to_table: :users, on_delete: :nullify }

      t.string :email, null: false
      t.string :unique_token, null: false
      t.string :status, default: 'pending', null: false

      # Status tracking
      t.datetime :expires_at, null: false
      t.datetime :accepted_at
      t.json :metadata, default: {}

      # Audit columns
      t.timestamps
    end

    # Indexes for performance and uniqueness
    add_index :invitations, :unique_token, unique: true
    add_index :invitations, [:sender_id, :email], unique: true
    add_index :invitations, [:status, :expires_at]

  end
end
