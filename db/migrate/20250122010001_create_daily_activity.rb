class CreateDailyActivity < ActiveRecord::Migration[7.0]
  def change
    create_table :daily_activities do |t|
      t.references :company, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.references :daily_log, null: true, foreign_key: true
      t.references :contract, null: true, foreign_key: true
      t.datetime :start_time
      t.datetime :end_time
      t.integer :duration
      t.text :description
      t.string :place
      t.string :project

      t.timestamps
    end
  end
end