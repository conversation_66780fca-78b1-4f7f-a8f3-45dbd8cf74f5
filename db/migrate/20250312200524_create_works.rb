class CreateWorks < ActiveRecord::Migration[7.0]
  def change
    create_table :works do |t|
      t.string :title, null: false
      t.text :description
      t.string :location
      t.string :status, default: "scheduled"
      t.datetime :scheduled_start_date
      t.datetime :scheduled_end_date
      t.bigint :client_id
      t.bigint :company_id, null: false
      t.string :work_type
      t.boolean :is_recurring, default: false
      t.float :latitude
      t.float :longitude

      t.timestamps
    end

    add_index :works, :company_id
    add_index :works, :client_id
    add_index :works, [:status, :scheduled_start_date]
    add_index :works, [:company_id, :status]
    
    add_foreign_key :works, :companies
  end
end
