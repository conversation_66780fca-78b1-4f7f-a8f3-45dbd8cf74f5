class CreateMeetings < ActiveRecord::Migration[7.0]
  def change
    create_table :meetings do |t|
      t.references :company, null: false, foreign_key: true
      t.references :created_by, null: false, foreign_key: { to_table: :users }
      t.string :title, null: false
      t.text :description
      t.string :place
      t.jsonb :day_options, default: {}, null: false
      t.datetime :confirmed_date

      t.timestamps
    end
    
    add_index :meetings, [:company_id, :created_at]
  end
end
