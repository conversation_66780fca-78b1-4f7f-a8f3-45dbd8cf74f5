class UpdateUserReferencesInContractsAndCompanyUserRoles < ActiveRecord::Migration[7.0]
  def change
    # Fixing business logic - scenario User leaves the Company
    remove_foreign_key :contracts, :users
    remove_foreign_key :company_user_roles, :users
    change_column_null :contracts, :user_id, true
    change_column_null :company_user_roles, :user_id, true
    add_foreign_key :contracts, :users, on_delete: :nullify
    add_foreign_key :company_user_roles, :users, on_delete: :nullify
  end
end
