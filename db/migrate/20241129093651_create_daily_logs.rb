class CreateDailyLogs < ActiveRecord::Migration[7.0]
  def change
    create_table :daily_logs do |t|
      t.references :contract, null: false, foreign_key: true
      t.references :company, null: false, foreign_key: true
      t.references :user, null: true, foreign_key: { on_delete: :nullify }
      t.datetime :start_time
      t.datetime :end_time
      t.text :description
      t.string :place
      t.string :status
      t.string :start_ip
      t.integer :duration
      t.integer :workday
      t.float :latitude
      t.float :longitude

      t.timestamps
    end
  end
end
