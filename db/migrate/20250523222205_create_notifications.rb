class CreateNotifications < ActiveRecord::Migration[7.0]
  def change
    create_table :notifications do |t|
      t.references :user, null: false, foreign_key: true
      t.references :company, null: false, foreign_key: true
      t.references :notifiable, polymorphic: true, null: false
      t.string :notification_type
      t.string :title
      t.text :message
      t.json :data
      t.datetime :read_at
      t.datetime :processed_at

      t.timestamps
    end
    
    add_index :notifications, [:user_id, :read_at]
    add_index :notifications, [:company_id, :created_at]
    add_index :notifications, :notification_type
  end
end
