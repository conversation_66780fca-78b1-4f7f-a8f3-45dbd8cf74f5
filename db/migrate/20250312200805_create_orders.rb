class CreateOrders < ActiveRecord::Migration[7.0]
  def change
    create_table :orders do |t|
      t.string :client_name
      t.string :client_email
      t.string :client_phone
      t.text :service_requested
      t.string :status, default: "pending"
      t.text :notes
      t.bigint :company_id, null: false
      t.bigint :work_id
      t.datetime :requested_date
      t.string :location
      t.float :latitude
      t.float :longitude
      
      t.timestamps
    end

    add_index :orders, :company_id
    add_index :orders, :work_id
    add_index :orders, [:company_id, :status]
    add_index :orders, :client_email
    
    add_foreign_key :orders, :companies
    add_foreign_key :orders, :works, on_delete: :nullify
  end
end
