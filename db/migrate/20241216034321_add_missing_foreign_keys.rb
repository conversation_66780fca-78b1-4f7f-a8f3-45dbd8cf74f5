class AddMissingForeignKeys < ActiveRecord::Migration[7.0]
  def change
    remove_foreign_key :user_profiles, :users
    remove_foreign_key :company_user_roles, :users
    remove_foreign_key :settings, :companies
    add_foreign_key :user_profiles, :users, on_delete: :cascade
    add_foreign_key :company_user_roles, :users, on_delete: :nullify
    add_foreign_key :settings, :companies, on_delete: :cascade
  end
end
