class CreateEvents < ActiveRecord::Migration[7.0]
  def change
    create_table :events do |t|
      t.references :company, null: false, foreign_key: true
      t.references :contract, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.datetime :start_time
      t.datetime :end_time
      t.string :event_type
      t.string :status
      t.text :description  
      t.integer :duration
      t.integer :workday

      t.timestamps
    end
  end
end
