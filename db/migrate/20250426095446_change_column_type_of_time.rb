class ChangeColumnTypeOfTime < ActiveRecord::Migration[7.0]
  def up
    # Add new datetime columns
    add_column :bookings, :specific_time_new, :datetime
    add_column :bookings, :confirmed_time_new, :datetime
    add_column :works, :specific_time_new, :datetime
    add_column :works, :confirmed_time_new, :datetime
    
    # No data copying needed if we're okay with setting values to nil
    # Or we could delete records that have data
    # Uncomment the next line if you want to delete any records with data
    # execute("DELETE FROM bookings; DELETE FROM works;")
    
    # Remove old columns
    remove_column :bookings, :specific_time
    remove_column :bookings, :confirmed_time
    remove_column :works, :specific_time
    remove_column :works, :confirmed_time
    
    # Rename new columns to original names
    rename_column :bookings, :specific_time_new, :specific_time
    rename_column :bookings, :confirmed_time_new, :confirmed_time
    rename_column :works, :specific_time_new, :specific_time
    rename_column :works, :confirmed_time_new, :confirmed_time
  end
  
  def down
    # Just in case you need to roll back
    add_column :bookings, :specific_time_rollback, :time
    add_column :bookings, :confirmed_time_rollback, :time
    add_column :works, :specific_time_rollback, :time
    add_column :works, :confirmed_time_rollback, :time
    
    # Remove new columns
    remove_column :bookings, :specific_time
    remove_column :bookings, :confirmed_time
    remove_column :works, :specific_time
    remove_column :works, :confirmed_time
    
    # Rename rollback columns to original names
    rename_column :bookings, :specific_time_rollback, :specific_time
    rename_column :bookings, :confirmed_time_rollback, :confirmed_time
    rename_column :works, :specific_time_rollback, :specific_time
    rename_column :works, :confirmed_time_rollback, :confirmed_time
  end
end
