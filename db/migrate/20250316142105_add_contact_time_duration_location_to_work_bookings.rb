class AddContactTimeDurationLocationToWorkBookings < ActiveRecord::Migration[7.0]
  def change
    add_column :booking_links, :location, :string
    add_column :booking_links, :location_required, :boolean, default: false
    add_column :booking_links, :is_remote, :boolean, default: false
    
    add_column :bookings, :location, :string
    add_column :bookings, :latitude, :float
    add_column :bookings, :longitude, :float
    add_column :bookings, :duration, :integer

    add_column :works, :duration, :integer
    add_column :works, :preferred_period, :string
    add_column :works, :specific_time, :time
  end
end
