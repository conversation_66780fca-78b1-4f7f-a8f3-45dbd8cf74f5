class CreateMeetingUsers < ActiveRecord::Migration[7.0]
  def change
    create_table :meeting_users do |t|
      t.references :meeting, null: false, foreign_key: true
      t.references :contract, foreign_key: true
      t.string :email, null: false
      t.string :token, null: false
      t.jsonb :selected_dates, default: {}, null: false

      t.timestamps
    end
    
    add_index :meeting_users, :token, unique: true
    add_index :meeting_users, [:meeting_id, :email], unique: true
  end
end
