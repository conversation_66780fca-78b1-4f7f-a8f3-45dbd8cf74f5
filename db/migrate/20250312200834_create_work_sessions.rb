class CreateWorkSessions < ActiveRecord::Migration[7.0]
  def change
    create_table :work_sessions do |t|
      t.bigint :work_id, null: false
      t.bigint :user_id, null: false
      t.bigint :company_id, null: false
      t.datetime :start_time, null: false
      t.datetime :end_time
      t.integer :duration
      t.text :notes
      t.string :status, default: "in_progress"
      t.float :latitude
      t.float :longitude
      t.string :ip_address
      
      t.timestamps
    end

    add_index :work_sessions, :work_id
    add_index :work_sessions, :user_id
    add_index :work_sessions, :company_id
    add_index :work_sessions, [:user_id, :start_time]
    add_index :work_sessions, [:work_id, :status]
    
    add_foreign_key :work_sessions, :works
    add_foreign_key :work_sessions, :users
    add_foreign_key :work_sessions, :companies
  end
end
