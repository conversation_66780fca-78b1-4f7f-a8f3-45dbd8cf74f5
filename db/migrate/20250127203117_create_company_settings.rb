class CreateCompanySettings < ActiveRecord::Migration[7.0]
  def change
    create_table :company_settings do |t|
      t.references :company, null: false, foreign_key: { on_delete: :cascade }
      t.integer :break_duration, default: 30
      t.boolean :auto_break, default: true
      t.boolean :auto_end, default: true
      t.boolean :allow_overtime, default: true
      t.string :timezone, default: 'Prague'

      t.timestamps
    end
  end
end
