class CreateUserSettings < ActiveRecord::Migration[7.0]
  def change
    create_table :user_settings do |t|
      t.references :user, null: false, foreign_key: { on_delete: :cascade }
      t.time :default_start_time
      t.time :default_end_time
      t.time :default_break_start
      t.boolean :auto_break, default: true
      t.string :timezone, default: 'Prague'

      t.timestamps
    end
  end
end
