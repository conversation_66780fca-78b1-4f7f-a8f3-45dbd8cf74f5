class CreateBookingLinks < ActiveRecord::Migration[7.0]
  def change
    create_table :booking_links do |t|
      t.belongs_to :company, null: false, foreign_key: true
      t.string :name, null: false
      t.text :description
      t.boolean :active, default: true
      t.string :slug, null: false
      t.text :preferred_days, array: true, default: []
      t.integer :duration, default: 60  # duration in minutes
      t.string :color, default: "#4a86e8"  # default blue color

      t.timestamps
    end
    add_index :booking_links, [:company_id, :slug], unique: true
  end
end
