class CreateBookings < ActiveRecord::Migration[7.0]
  def change
    create_table :bookings do |t|
      t.belongs_to :booking_link, null: false, foreign_key: true
      t.belongs_to :company, null: false, foreign_key: true
      t.string :client_name, null: false
      t.string :client_email, null: false
      t.string :client_phone
      t.date :preferred_date, null: false
      t.string :preferred_period, null: false
      t.time :specific_time
      t.text :message
      t.string :status, default: "pending"
      t.datetime :confirmed_time

      t.timestamps
    end
    add_index :bookings, [:company_id, :status]
    add_index :bookings, :client_email
  end
end
