--- !ruby/object:ActiveRecord::ConnectionAdapters::SchemaCache
columns:
  schema_migrations:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: version
    sql_type_metadata: &1 !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::TypeMetadata
      delegate_dc_obj: !ruby/object:ActiveRecord::ConnectionAdapters::SqlTypeMetadata
        sql_type: character varying
        type: :string
        limit:
        precision:
        scale:
      oid: 1043
      fmod: -1
    'null': false
    default:
    default_function:
    collation:
    comment:
  ar_internal_metadata:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: key
    sql_type_metadata: *1
    'null': false
    default:
    default_function:
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: value
    sql_type_metadata: *1
    'null': true
    default:
    default_function:
    collation:
    comment:
  - &4 !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: created_at
    sql_type_metadata: &2 !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::TypeMetadata
      delegate_dc_obj: !ruby/object:ActiveRecord::ConnectionAdapters::SqlTypeMetadata
        sql_type: timestamp(6) without time zone
        type: :datetime
        limit:
        precision: 6
        scale:
      oid: 1114
      fmod: 6
    'null': false
    default:
    default_function:
    collation:
    comment:
  - &5 !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: updated_at
    sql_type_metadata: *2
    'null': false
    default:
    default_function:
    collation:
    comment:
  user_profiles:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial: true
    generated: ''
    name: id
    sql_type_metadata: &3 !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::TypeMetadata
      delegate_dc_obj: !ruby/object:ActiveRecord::ConnectionAdapters::SqlTypeMetadata
        sql_type: bigint
        type: :integer
        limit: 8
        precision:
        scale:
      oid: 20
      fmod: -1
    'null': false
    default:
    default_function: nextval('user_profiles_id_seq'::regclass)
    collation:
    comment:
  - &6 !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: user_id
    sql_type_metadata: *3
    'null': false
    default:
    default_function:
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: first_name
    sql_type_metadata: *1
    'null': true
    default:
    default_function:
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: last_name
    sql_type_metadata: *1
    'null': true
    default:
    default_function:
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: prefix
    sql_type_metadata: *1
    'null': true
    default:
    default_function:
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: title_prefix
    sql_type_metadata: *1
    'null': true
    default:
    default_function:
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: title_suffix
    sql_type_metadata: *1
    'null': true
    default:
    default_function:
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: location
    sql_type_metadata: *1
    'null': true
    default:
    default_function:
    collation:
    comment:
  - *4
  - *5
  companies:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial: true
    generated: ''
    name: id
    sql_type_metadata: *3
    'null': false
    default:
    default_function: nextval('companies_id_seq'::regclass)
    collation:
    comment:
  - &7 !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: name
    sql_type_metadata: *1
    'null': true
    default:
    default_function:
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: slug
    sql_type_metadata: *1
    'null': true
    default:
    default_function:
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: subdomain
    sql_type_metadata: *1
    'null': true
    default:
    default_function:
    collation:
    comment:
  - *4
  - *5
  company_user_roles:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial: true
    generated: ''
    name: id
    sql_type_metadata: *3
    'null': false
    default:
    default_function: nextval('company_user_roles_id_seq'::regclass)
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: company_id
    sql_type_metadata: *3
    'null': false
    default:
    default_function:
    collation:
    comment:
  - *6
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: role_id
    sql_type_metadata: *3
    'null': false
    default:
    default_function:
    collation:
    comment:
  - *4
  - *5
  users:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial: true
    generated: ''
    name: id
    sql_type_metadata: *3
    'null': false
    default:
    default_function: nextval('users_id_seq'::regclass)
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: email
    sql_type_metadata: *1
    'null': false
    default: ''
    default_function:
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: encrypted_password
    sql_type_metadata: *1
    'null': false
    default: ''
    default_function:
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: reset_password_token
    sql_type_metadata: *1
    'null': true
    default:
    default_function:
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: reset_password_sent_at
    sql_type_metadata: *2
    'null': true
    default:
    default_function:
    collation:
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial:
    generated: ''
    name: remember_created_at
    sql_type_metadata: *2
    'null': true
    default:
    default_function:
    collation:
    comment:
  - *4
  - *5
  roles:
  - !ruby/object:ActiveRecord::ConnectionAdapters::PostgreSQL::Column
    serial: true
    generated: ''
    name: id
    sql_type_metadata: *3
    'null': false
    default:
    default_function: nextval('roles_id_seq'::regclass)
    collation:
    comment:
  - *7
  - *4
  - *5
primary_keys:
  schema_migrations: version
  ar_internal_metadata: key
  user_profiles: id
  companies: id
  company_user_roles: id
  users: id
  roles: id
data_sources:
  schema_migrations: true
  ar_internal_metadata: true
  user_profiles: true
  companies: true
  company_user_roles: true
  users: true
  roles: true
indexes:
  schema_migrations: []
  ar_internal_metadata: []
  user_profiles:
  - !ruby/object:ActiveRecord::ConnectionAdapters::IndexDefinition
    table: user_profiles
    name: index_user_profiles_on_user_id
    unique: false
    columns:
    - user_id
    lengths: {}
    orders: {}
    opclasses: {}
    where:
    type:
    using: :btree
    comment:
  companies: []
  company_user_roles:
  - !ruby/object:ActiveRecord::ConnectionAdapters::IndexDefinition
    table: company_user_roles
    name: index_company_user_roles_on_company_id
    unique: false
    columns:
    - company_id
    lengths: {}
    orders: {}
    opclasses: {}
    where:
    type:
    using: :btree
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::IndexDefinition
    table: company_user_roles
    name: index_company_user_roles_on_role_id
    unique: false
    columns:
    - role_id
    lengths: {}
    orders: {}
    opclasses: {}
    where:
    type:
    using: :btree
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::IndexDefinition
    table: company_user_roles
    name: index_company_user_roles_on_user_id
    unique: false
    columns:
    - user_id
    lengths: {}
    orders: {}
    opclasses: {}
    where:
    type:
    using: :btree
    comment:
  users:
  - !ruby/object:ActiveRecord::ConnectionAdapters::IndexDefinition
    table: users
    name: index_users_on_email
    unique: true
    columns:
    - email
    lengths: {}
    orders: {}
    opclasses: {}
    where:
    type:
    using: :btree
    comment:
  - !ruby/object:ActiveRecord::ConnectionAdapters::IndexDefinition
    table: users
    name: index_users_on_reset_password_token
    unique: true
    columns:
    - reset_password_token
    lengths: {}
    orders: {}
    opclasses: {}
    where:
    type:
    using: :btree
    comment:
  roles: []
version: 20241118081406
database_version: 160004
