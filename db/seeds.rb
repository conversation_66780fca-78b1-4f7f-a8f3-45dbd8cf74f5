# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Examples:
#
#   movies = Movie.create([{ name: "Star Wars" }, { name: "Lord of the Rings" }])
#   Character.create(name: "<PERSON>", movie: movies.first)

['owner', 'employee', 'admin', 'supervisor', 'vendor', 'client'].each do |role_name|
  Role.find_or_create_by!(name: role_name)
end

puts "Default roles created/verified!"

Plan.find_or_create_by!(name: "plus") do |plan|
  plan.description = "Plus"
  plan.price = "590.00"
  plan.duration = 30
  plan.active = true
end

Plan.find_or_create_by!(name: "premium") do |plan|
  plan.description = "Premium"
  plan.price = "890.00"
  plan.duration = 30
  plan.active = true
end

puts "Default plans created/verified!"

c = Company.find(1)
subscription = c.subscriptions.find_or_create_by!(plan: Plan.find(1)) do |sub|
  sub.start_date = Date.today
  sub.expire_date = Date.today + 3650
  sub.status = 'active'
end

puts "Default subscription created/verified for Company ID 1!"